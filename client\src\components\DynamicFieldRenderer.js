import React from 'react';
import {
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Box,
  Typography
} from '@mui/material';
import TextWithSuggestions from './TextWithSuggestions';

const DynamicFieldRenderer = ({ 
  field, 
  value, 
  onChange, 
  error, 
  disabled = false,
  tasks = [] // Para campos de tipo select que necesiten tareas
}) => {
  const handleChange = (event) => {
    const newValue = event.target.type === 'checkbox' 
      ? event.target.checked 
      : event.target.value;
    onChange(field.name, newValue);
  };

  // Función para obtener opciones de select
  const getSelectOptions = () => {
    if (field.name === 'task') {
      // Campo especial para tareas
      return tasks.map(task => ({
        value: task.id,
        label: task.title
      }));
    }
    
    if (field.options) {
      // Opciones definidas en el campo
      return field.options.split(',').map(option => ({
        value: option.trim(),
        label: option.trim()
      }));
    }
    
    return [];
  };

  // Campos que no deben renderizarse como dinámicos (se manejan especialmente)
  const specialFields = ['title', 'description', 'hoursWorked', 'startTime', 'endTime', 'estimatedHours', 'dueDate'];
  
  if (specialFields.includes(field.name)) {
    return null; // Estos campos se renderizan en el formulario principal
  }

  const renderField = () => {
    switch (field.type) {
      case 'text':
        return (
          <TextField
            fullWidth
            label={field.label}
            value={value || ''}
            onChange={handleChange}
            placeholder={field.placeholder}
            required={field.required}
            error={!!error}
            helperText={error}
            disabled={disabled}
          />
        );

      case 'textarea':
        return (
          <TextField
            fullWidth
            multiline
            rows={3}
            label={field.label}
            value={value || ''}
            onChange={handleChange}
            placeholder={field.placeholder}
            required={field.required}
            error={!!error}
            helperText={error}
            disabled={disabled}
          />
        );

      case 'number':
        return (
          <TextField
            fullWidth
            type="number"
            label={field.label}
            value={value || ''}
            onChange={handleChange}
            placeholder={field.placeholder}
            required={field.required}
            error={!!error}
            helperText={error}
            disabled={disabled}
            inputProps={{ step: 0.1, min: 0 }}
          />
        );

      case 'date':
        return (
          <TextField
            fullWidth
            type="date"
            label={field.label}
            value={value || ''}
            onChange={handleChange}
            required={field.required}
            error={!!error}
            helperText={error}
            disabled={disabled}
            InputLabelProps={{ shrink: true }}
          />
        );

      case 'datetime-local':
        return (
          <TextField
            fullWidth
            type="datetime-local"
            label={field.label}
            value={value || ''}
            onChange={handleChange}
            required={field.required}
            error={!!error}
            helperText={error}
            disabled={disabled}
            InputLabelProps={{ shrink: true }}
          />
        );

      case 'select':
        const options = getSelectOptions();
        return (
          <FormControl fullWidth required={field.required} error={!!error} disabled={disabled}>
            <InputLabel>{field.label}</InputLabel>
            <Select
              value={value || ''}
              onChange={handleChange}
              label={field.label}
            >
              {!field.required && (
                <MenuItem value="">
                  <em>{field.placeholder || 'Ninguno'}</em>
                </MenuItem>
              )}
              {options.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {error && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                {error}
              </Typography>
            )}
          </FormControl>
        );

      case 'checkbox':
        return (
          <FormControlLabel
            control={
              <Checkbox
                checked={value === true || value === 'true'}
                onChange={handleChange}
                disabled={disabled}
              />
            }
            label={field.label}
          />
        );

      case 'text-with-suggestions':
        return (
          <TextWithSuggestions
            field={field}
            value={value}
            onChange={onChange}
            error={error}
            disabled={disabled}
          />
        );

      default:
        return (
          <TextField
            fullWidth
            label={field.label}
            value={value || ''}
            onChange={handleChange}
            placeholder={field.placeholder}
            required={field.required}
            error={!!error}
            helperText={error}
            disabled={disabled}
          />
        );
    }
  };

  return (
    <Box sx={{ mb: 2 }}>
      {renderField()}
    </Box>
  );
};

export default DynamicFieldRenderer;
