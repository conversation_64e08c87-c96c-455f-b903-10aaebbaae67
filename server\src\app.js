// Añadir al inicio del archivo
const cron = require('node-cron');
const { moverTareasARevision, getWorkScheduleEndTimes } = require('./utils/workHourUtils');

// Reemplazar la tarea programada fija con una versión dinámica
// Programar la tarea para ejecutarse cada 5 minutos y verificar si es hora de mover tareas
cron.schedule('*/5 * * * 1-7', async () => {
  console.log('[Cron] Verificando si es hora de mover tareas a revisión');
  try {
    // Obtener los horarios de fin de jornada de la base de datos
    const endTimes = await getWorkScheduleEndTimes();
    
    // Obtener la hora actual en Lima, Perú (UTC-5)
    const now = new Date();
    const limaOffset = -5 * 60; // Lima es UTC-5
    const serverOffset = now.getTimezoneOffset();
    const offsetDiff = limaOffset - serverOffset;
    const limaDate = new Date(now.getTime() + offsetDiff * 60000);
    
    // Obtener el día actual (0 = Domingo, 1 = Lunes, etc.)
    const currentDay = limaDate.getDay();
    
    // Obtener la hora y minutos actuales
    const currentHour = limaDate.getHours();
    const currentMinute = limaDate.getMinutes();
    
    // Verificar si hay un horario configurado para hoy
    const todayEndTime = endTimes.find(schedule => schedule.dayOfWeek === currentDay);
    
    if (todayEndTime) {
      // Extraer hora y minutos del horario de fin
      const [endHour, endMinute] = todayEndTime.endTime.split(':').map(Number);
      
      // Verificar si es hora de mover tareas (dentro de los 10 minutos después del fin de jornada)
      const isEndOfWorkday = 
        (currentHour === endHour && currentMinute >= endMinute && currentMinute < endMinute + 10) || 
        (currentHour === endHour + 1 && currentMinute < (endMinute + 10) % 60 && endMinute >= 50);
      
      if (isEndOfWorkday) {
        console.log('[Cron] Fin de jornada detectado, ejecutando movimiento de tareas');
        const resultado = await moverTareasARevision();
        console.log('[Cron] Resultado:', resultado);
      } else {
        console.log(`[Cron] No es hora de mover tareas. Hora actual: ${currentHour}:${currentMinute}, Fin de jornada: ${endHour}:${endMinute}`);
      }
    } else {
      console.log(`[Cron] No hay horario configurado para hoy (día ${currentDay})`);
    }
  } catch (error) {
    console.error('[Cron] Error al verificar horario para mover tareas:', error);
  }
});