FROM node:18-alpine

WORKDIR /app

# Instalar dependencias necesarias para Prisma, PostgreSQL y herramientas de red
RUN apk add --no-cache openssl postgresql-client bash curl netcat-openbsd

# Cambiar el propietario del directorio de trabajo
RUN chown -R node:node /app

# Cambiar al usuario node
USER node

COPY --chown=node:node package*.json ./

# Instalar dependencias de forma simple y directa
RUN npm install --production=false --verbose

# Verificar que multer esté instalado
RUN node -e "require('multer'); console.log('✅ multer instalado correctamente')"

# Verificar otras dependencias críticas
RUN node -e "require('@prisma/client'); console.log('✅ @prisma/client instalado correctamente')"
RUN node -e "require('express'); console.log('✅ express instalado correctamente')"

# Copiar el resto de los archivos como usuario node
COPY --chown=node:node . .

# Generar el cliente Prisma
RUN npx prisma generate

# Crear un script para regenerar el cliente Prisma cuando sea necesario
RUN echo '#!/bin/sh\nnpx prisma generate' > /app/generate-prisma.sh \
    && chmod +x /app/generate-prisma.sh

EXPOSE 5000
EXPOSE 5555

CMD ["npm", "start"]
