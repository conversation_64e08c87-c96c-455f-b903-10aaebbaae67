const axios = require('axios');
const crypto = require('crypto');
const https = require('https');

// Clave para desencriptar API keys
const ENCRYPTION_KEY = process.env.API_KEY_ENCRYPTION_KEY || 'your-32-char-secret-key-here-123';

const decrypt = (encryptedText) => {
  try {
    const decipher = crypto.createDecipher('aes-256-cbc', ENCRYPTION_KEY);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    console.error('Error al desencriptar:', error);
    return encryptedText; // Devolver el texto original si falla
  }
};

class ApiExecutionService {
  /**
   * Ejecuta una petición API configurada dinámicamente
   */
  async executeRequest(integration, endpoint, apiKey, user, isTest = false) {
    try {
      console.log(`🔗 Ejecutando petición API: ${endpoint.name} para usuario ${user.email}`);
      
      // Configuración básica
      const url = `${integration.baseUrl}${endpoint.path}`;
      const headers = { ...integration.authConfig };
      
      // Agregar API key
      if (integration.authType === 'api_key') {
        headers['authtoken'] = apiKey;
      }
      
      // Configurar SSL si es necesario
      const requestConfig = {
        method: endpoint.method,
        url,
        headers,
        timeout: 30000
      };
      
      if (integration.skipSslVerification && url.startsWith('https://')) {
        requestConfig.httpsAgent = new https.Agent({
          rejectUnauthorized: false
        });
      }
      
      // Ejecutar petición
      const response = await axios(requestConfig);
      
      return {
        success: true,
        data: response.data,
        status: response.status,
        headers: response.headers
      };
      
    } catch (error) {
      console.error('Error en executeRequest:', error.message);
      return {
        success: false,
        error: error.message,
        status: error.response?.status || 500
      };
    }
  }

  /**
   * Prueba la conexión con una API externa
   */
  async testConnection(integration, endpoint, apiKey) {
    try {
      const url = `${integration.baseUrl}${endpoint.path}`;
      const headers = { ...integration.authConfig };
      
      if (integration.authType === 'api_key') {
        headers['authtoken'] = apiKey;
      }
      
      const requestConfig = {
        method: 'GET',
        url,
        headers,
        timeout: 10000
      };
      
      if (integration.skipSslVerification && url.startsWith('https://')) {
        requestConfig.httpsAgent = new https.Agent({
          rejectUnauthorized: false
        });
      }
      
      const response = await axios(requestConfig);
      
      return {
        success: true,
        status: response.status,
        message: 'Conexión exitosa'
      };
      
    } catch (error) {
      return {
        success: false,
        error: error.message,
        status: error.response?.status || 500
      };
    }
  }
}

module.exports = new ApiExecutionService();
