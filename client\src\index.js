import React from 'react';
import ReactDOM from 'react-dom/client';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import App from './App';
import theme from './theme';
import io from 'socket.io-client';
import { API_URL } from './config';

// Inicializar el socket y hacerlo disponible globalmente
const initializeSocket = () => {
  const token = localStorage.getItem('token');
  if (token) {
    const socket = io(API_URL, {
      auth: { token }
    });
    window.socket = socket;
    
    socket.on('connect', () => {
      console.log('Conectado al servidor de WebSockets');
    });
    
    socket.on('connect_error', (error) => {
      console.error('Error de conexión WebSocket:', error);
    });
    
    return socket;
  }
  return null;
};

// Inicializar socket antes de renderizar la aplicación
initializeSocket();

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <App />
    </ThemeProvider>
  </React.StrictMode>
);
