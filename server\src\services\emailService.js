const nodemailer = require('nodemailer');

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  async sendTaskNotification(user, task) {
    const mailOptions = {
      from: process.env.SMTP_FROM,
      to: user.email,
      subject: `Nueva tarea asignada: ${task.title}`,
      html: `
        <h2>Nueva tarea asignada</h2>
        <p>Hola ${user.name},</p>
        <p>Se te ha asignado una nueva tarea:</p>
        <ul>
          <li><strong>Título:</strong> ${task.title}</li>
          <li><strong>Descripción:</strong> ${task.description}</li>
          <li><strong>Fecha límite:</strong> ${new Date(task.dueDate).toLocaleDateString()}</li>
          <li><strong>Horas estimadas:</strong> ${task.estimatedHours}</li>
        </ul>
        <p>Por favor, revisa tu tablero Kanban para más detalles.</p>
      `,
    };

    return this.transporter.sendMail(mailOptions);
  }

  async sendApprovalNotification(user, type, item) {
    const mailOptions = {
      from: process.env.SMTP_FROM,
      to: user.email,
      subject: `Solicitud de aprobación: ${type}`,
      html: `
        <h2>Nueva solicitud de aprobación</h2>
        <p>Hola ${user.name},</p>
        <p>Tienes una nueva solicitud pendiente de aprobación:</p>
        <ul>
          <li><strong>Tipo:</strong> ${type}</li>
          ${type === 'TIME_ENTRY' ? `
            <li><strong>Título:</strong> ${item.title}</li>
            <li><strong>Horas:</strong> ${item.hoursWorked}</li>
            <li><strong>Fecha:</strong> ${new Date(item.startTime).toLocaleDateString()}</li>
          ` : `
            <li><strong>Tipo de permiso:</strong> ${item.type}</li>
            <li><strong>Duración:</strong> ${item.duration} horas</li>
            <li><strong>Fecha inicio:</strong> ${new Date(item.startTime).toLocaleDateString()}</li>
          `}
        </ul>
        <p>Por favor, revisa el panel de aprobaciones para tomar acción.</p>
      `,
    };

    return this.transporter.sendMail(mailOptions);
  }

  async sendStatusUpdateNotification(user, type, item, status) {
    const mailOptions = {
      from: process.env.SMTP_FROM,
      to: user.email,
      subject: `Actualización de estado: ${type}`,
      html: `
        <h2>Actualización de estado</h2>
        <p>Hola ${user.name},</p>
        <p>Tu solicitud ha sido ${status === 'APPROVED' ? 'aprobada' : 'rechazada'}:</p>
        <ul>
          <li><strong>Tipo:</strong> ${type}</li>
          ${type === 'TIME_ENTRY' ? `
            <li><strong>Título:</strong> ${item.title}</li>
            <li><strong>Horas:</strong> ${item.hoursWorked}</li>
          ` : `
            <li><strong>Tipo de permiso:</strong> ${item.type}</li>
            <li><strong>Duración:</strong> ${item.duration} horas</li>
          `}
        </ul>
        ${status === 'REJECTED' ? `<p><strong>Motivo:</strong> ${item.rejectionReason || 'No especificado'}</p>` : ''}
      `,
    };

    return this.transporter.sendMail(mailOptions);
  }
}

module.exports = new EmailService();
