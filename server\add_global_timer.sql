-- Agregar campos de temporizador global a la tabla Task
-- Ejecutar en la base de datos Docker

-- Agregar campos de temporizador global
ALTER TABLE "Task" ADD COLUMN IF NOT EXISTS "timerIsRunning" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "Task" ADD COLUMN IF NOT EXISTS "timerStartTime" TIMESTAMP(3);
ALTER TABLE "Task" ADD COLUMN IF NOT EXISTS "timerLastStarted" TIMESTAMP(3);
ALTER TABLE "Task" ADD COLUMN IF NOT EXISTS "timerAccumulatedSeconds" INTEGER NOT NULL DEFAULT 0;
ALTER TABLE "Task" ADD COLUMN IF NOT EXISTS "timerCurrentUserId" TEXT;

-- Agregar foreign key constraint para timerCurrentUserId
ALTER TABLE "Task" ADD CONSTRAINT "Task_timerCurrentUserId_fkey" 
FOREIGN KEY ("timerCurrentUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Verificar que los campos se agregaron correctamente
SELECT 
    column_name, 
    data_type, 
    column_default, 
    is_nullable 
FROM information_schema.columns 
WHERE table_name = 'Task' 
AND column_name LIKE 'timer%'
ORDER BY column_name;

-- Mostrar algunas tareas de ejemplo con los nuevos campos
SELECT 
    id,
    LEFT(title, 30) as titulo,
    status,
    "timerIsRunning",
    "timerAccumulatedSeconds",
    "timerCurrentUserId"
FROM "Task" 
ORDER BY "updatedAt" DESC 
LIMIT 3;
