import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material';
import CssBaseline from '@mui/material/CssBaseline';
import Navbar from './components/layout/Navbar';
import Dashboard from './pages/Dashboard';
import KanbanBoard from './pages/KanbanBoard';
import TimeTracking from './pages/TimeTracking';
import TimeApproval from './pages/TimeApproval';
import Permissions from './pages/Permissions';
import PermissionApproval from './pages/PermissionApproval';
import Reports from './pages/Reports';
import Settings from './pages/Settings';
import Login from './pages/Login';
import Register from './pages/Register';
import Backups from './pages/Backups';
import LDAPSettings from './pages/LDAPSettings';
import CompletedTasks from './pages/CompletedTasks';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

// Componente de ruta protegida
const PrivateRoute = ({ children }) => {
  const token = localStorage.getItem('token');
  return token ? children : <Navigate to="/login" />;
};

// Componente de ruta protegida para administradores
const AdminRoute = ({ children }) => {
  const token = localStorage.getItem('token');
  const userRole = localStorage.getItem('userRole');
  const isAdmin = userRole === 'ADMIN' || userRole === 'MANAGER';
  
  if (!token) return <Navigate to="/login" />;
  if (!isAdmin) return <Navigate to="/" />;
  return children;
};

function App() {
  const token = localStorage.getItem('token');

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <div className="app">
          {token && <Navbar />}
          <main className="main-content">
            <Routes>
              {/* Rutas públicas */}
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />

              {/* Rutas protegidas */}
              <Route path="/" element={
                <PrivateRoute>
                  <Dashboard />
                </PrivateRoute>
              } />
              <Route path="/kanban" element={
                <PrivateRoute>
                  <KanbanBoard />
                </PrivateRoute>
              } />
              <Route path="/time-tracking" element={
                <PrivateRoute>
                  <TimeTracking />
                </PrivateRoute>
              } />
              <Route path="/time-approval" element={
                <AdminRoute>
                  <TimeApproval />
                </AdminRoute>
              } />
              <Route path="/permissions" element={
                <PrivateRoute>
                  <Permissions />
                </PrivateRoute>
              } />
              <Route path="/permission-approval" element={
                <AdminRoute>
                  <PermissionApproval />
                </AdminRoute>
              } />
              <Route path="/reports" element={
                <AdminRoute>
                  <Reports />
                </AdminRoute>
              } />
              <Route path="/settings" element={
                <AdminRoute>
                  <Settings />
                </AdminRoute>
              } />
              <Route path="/backups" element={
                <PrivateRoute>
                  <Backups />
                </PrivateRoute>
              } />
              <Route path="/ldap-settings" element={
                <AdminRoute>
                  <LDAPSettings />
                </AdminRoute>
              } />
              <Route path="/completed" element={
                <PrivateRoute>
                  <CompletedTasks />
                </PrivateRoute>
              } />
            </Routes>
          </main>
        </div>
      </Router>
    </ThemeProvider>
  );
}

export default App;
