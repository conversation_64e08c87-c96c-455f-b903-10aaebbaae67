/**
 * Utility functions for formatting time values consistently across the application
 */

/**
 * Formats hours (decimal) into a human-readable string with hours and minutes
 * @param {number} hours - Hours as a decimal number (e.g., 1.5 for 1 hour and 30 minutes)
 * @returns {string} Formatted string in the format "Xh Ym"
 */
const formatHoursAndMinutes = (hours) => {
  if (hours === null || hours === undefined || isNaN(hours)) {
    return '0h 0m';
  }
  
  const wholeHours = Math.floor(hours);
  const minutes = Math.round((hours - wholeHours) * 60);
  
  return `${wholeHours}h ${minutes}m`;
};

/**
 * Formats seconds into a human-readable string with hours and minutes
 * @param {number} seconds - Total seconds
 * @returns {string} Formatted string in the format "Xh Ym"
 */
const formatSecondsToHoursAndMinutes = (seconds) => {
  if (seconds === null || seconds === undefined || isNaN(seconds)) {
    return '0h 0m';
  }
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  return `${hours}h ${minutes}m`;
};

module.exports = {
  formatHoursAndMinutes,
  formatSecondsToHoursAndMinutes
};