const express = require('express');
const router = express.Router();
const apiIntegrationController = require('../controllers/apiIntegrationController');
const apiImportController = require('../controllers/apiImportController');
const { authenticateToken, isAdmin } = require('../middleware/auth');

// Aplicar middleware de autenticación a todas las rutas
router.use(authenticateToken);

// ========================================
// RUTAS PARA GESTIÓN DE INTEGRACIONES (Solo Admin)
// ========================================

// Obtener todas las integraciones API
router.get('/', isAdmin, apiIntegrationController.getApiIntegrations);

// Crear nueva integración API
router.post('/', isAdmin, apiIntegrationController.createApiIntegration);

// Actualizar integración API
router.put('/:id', isAdmin, apiIntegrationController.updateApiIntegration);

// Eliminar integración API
router.delete('/:id', isAdmin, apiIntegrationController.deleteApiIntegration);

// ========================================
// RUTAS PARA GESTIÓN DE API KEYS (Solo Admin)
// ========================================

// Obtener API keys de usuarios para una integración
router.get('/:integrationId/users', isAdmin, apiIntegrationController.getUserApiKeys);

// Configurar API key para un usuario específico
router.put('/:integrationId/users/:userId/api-key', isAdmin, apiIntegrationController.setUserApiKey);

// Probar API key de un usuario específico
router.post('/:integrationId/users/:userId/test', isAdmin, apiIntegrationController.testUserApiKey);

// ========================================
// RUTAS PARA IMPORTACIÓN DE DATOS (Usuarios)
// ========================================

// Importar datos del usuario desde todas las integraciones configuradas
router.post('/import-user-data', apiImportController.importUserData);

// Obtener integraciones disponibles para el usuario actual
router.get('/available', apiImportController.getAvailableIntegrations);

// Obtener logs de importación del usuario actual
router.get('/import-logs', apiImportController.getImportLogs);

// ========================================
// RUTAS ADICIONALES PARA ADMINISTRACIÓN
// ========================================

// Obtener estadísticas de uso de integraciones (Solo Admin)
router.get('/stats', isAdmin, async (req, res) => {
  try {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    // Estadísticas generales
    const totalIntegrations = await prisma.apiIntegration.count();
    const activeIntegrations = await prisma.apiIntegration.count({
      where: { isActive: true }
    });

    // Usuarios con API keys configuradas
    const usersWithApiKeys = await prisma.userApiKey.groupBy({
      by: ['userId'],
      _count: {
        userId: true
      }
    });

    // Importaciones del último mes
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    const recentImports = await prisma.apiImportLog.count({
      where: {
        executedAt: {
          gte: lastMonth
        }
      }
    });

    const successfulImports = await prisma.apiImportLog.count({
      where: {
        executedAt: {
          gte: lastMonth
        },
        status: 'success'
      }
    });

    // Tareas creadas por integraciones
    const tasksFromIntegrations = await prisma.task.count({
      where: {
        source: 'API_IMPORT'
      }
    });

    // Top integraciones más usadas
    const topIntegrations = await prisma.apiImportLog.groupBy({
      by: ['integrationId'],
      _count: {
        integrationId: true
      },
      orderBy: {
        _count: {
          integrationId: 'desc'
        }
      },
      take: 5
    });

    res.json({
      totalIntegrations,
      activeIntegrations,
      usersWithApiKeys: usersWithApiKeys.length,
      recentImports,
      successfulImports,
      successRate: recentImports > 0 ? ((successfulImports / recentImports) * 100).toFixed(1) : 0,
      tasksFromIntegrations,
      topIntegrations
    });

  } catch (error) {
    console.error('Error al obtener estadísticas:', error);
    res.status(500).json({ error: 'Error al obtener estadísticas de integraciones' });
  }
});

// Obtener logs de todas las importaciones (Solo Admin)
router.get('/all-logs', isAdmin, async (req, res) => {
  try {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    const { page = 1, limit = 50, status, integrationId, userId } = req.query;

    // Construir filtros
    const where = {};
    if (status) where.status = status;
    if (integrationId) where.integrationId = integrationId;
    if (userId) where.userId = userId;

    const logs = await prisma.apiImportLog.findMany({
      where,
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        },
        integration: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        executedAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: parseInt(limit)
    });

    const total = await prisma.apiImportLog.count({ where });

    res.json({
      logs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error al obtener logs de importación:', error);
    res.status(500).json({ error: 'Error al obtener los logs de importación' });
  }
});

// Limpiar logs antiguos (Solo Admin)
router.delete('/logs/cleanup', isAdmin, async (req, res) => {
  try {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    const { daysOld = 90 } = req.body;
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const deletedCount = await prisma.apiImportLog.deleteMany({
      where: {
        executedAt: {
          lt: cutoffDate
        }
      }
    });

    res.json({
      message: `${deletedCount.count} logs eliminados exitosamente`,
      deletedCount: deletedCount.count,
      cutoffDate
    });

  } catch (error) {
    console.error('Error al limpiar logs:', error);
    res.status(500).json({ error: 'Error al limpiar los logs antiguos' });
  }
});

// Probar conexión de una integración sin guardar (Solo Admin)
router.post('/test-connection', isAdmin, async (req, res) => {
  try {
    const {
      baseUrl,
      authType,
      authConfig,
      endpoint,
      testApiKey
    } = req.body;

    if (!baseUrl || !endpoint || !testApiKey) {
      return res.status(400).json({ 
        error: 'baseUrl, endpoint y testApiKey son requeridos' 
      });
    }

    // Crear objeto temporal de integración para la prueba
    const tempIntegration = {
      baseUrl,
      authType,
      authConfig
    };

    // Crear usuario temporal para variables dinámicas
    const tempUser = {
      email: req.user.email,
      id: req.user.id,
      name: req.user.name
    };

    // Ejecutar petición de prueba
    const { executeApiRequest } = require('../controllers/apiIntegrationController');
    const result = await executeApiRequest(
      tempIntegration,
      endpoint,
      testApiKey,
      tempUser,
      true // isTest = true
    );

    res.json(result);

  } catch (error) {
    console.error('Error al probar conexión:', error);
    res.status(500).json({
      success: false,
      error: 'Error al probar la conexión',
      message: error.message
    });
  }
});

module.exports = router;
