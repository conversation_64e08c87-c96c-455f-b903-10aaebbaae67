const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map();
  }

  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: [
          process.env.CLIENT_URL || 'http://172.18.10.84:3000',
          'https://kanban.sg.gestionados.pe'
        ],
        methods: ['GET', 'POST']
      }
    });

    this.io.use((socket, next) => {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication error'));
      }

      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        socket.user = decoded;
        next();
      } catch (err) {
        next(new Error('Authentication error'));
      }
    });

    this.io.on('connection', (socket) => {
      console.log(`Usuario conectado: ${socket.user.email}`);
      this.connectedUsers.set(socket.user.id, socket);

      // Manejar unirse a salas de tareas
      socket.on('joinTask', (taskId) => {
        this.joinTaskRoom(socket, taskId);
      });

      // Manejar salir de salas de tareas
      socket.on('leaveTask', (taskId) => {
        this.leaveTaskRoom(socket, taskId);
      });

      // Manejar solicitudes de sincronización de temporizador
      socket.on('syncTimer', async (taskId) => {
        try {
          // Aquí podríamos obtener el estado actual del temporizador desde la base de datos
          // y emitirlo a todos los clientes en la sala de la tarea
          const { PrismaClient } = require('@prisma/client');
          const prisma = new PrismaClient();
          
          const activeTimers = await prisma.activeTimer.findMany({
            where: { taskId: taskId },
            include: { user: true }
          });
          
          activeTimers.forEach(timer => {
            let accumulatedTime = timer.accumulatedTime;
            if (timer.isRunning) {
              const now = new Date();
              const elapsedSeconds = Math.floor((now - new Date(timer.lastStarted)) / 1000);
              accumulatedTime += elapsedSeconds;
            }
            
            this.notifyTimerUpdate(taskId, {
              accumulatedTime,
              isRunning: timer.isRunning,
              userId: timer.userId,
              userName: timer.user.name
            });
          });
        } catch (error) {
          console.error('Error al sincronizar temporizador:', error);
        }
      });

      socket.on('disconnect', () => {
        console.log(`Usuario desconectado: ${socket.user.email}`);
        this.connectedUsers.delete(socket.user.id);
      });

      // Unirse a salas basadas en rol
      if (socket.user.role === 'ADMIN' || socket.user.role === 'MANAGER') {
        socket.join('managers');
      }
    });
  }

  // Enviar notificación a un usuario específico
  sendToUser(userId, event, data) {
    const userSocket = this.connectedUsers.get(userId);
    if (userSocket) {
      userSocket.emit(event, data);
    }
  }

  // Enviar notificación a todos los managers/admins
  sendToManagers(event, data) {
    this.io.to('managers').emit(event, data);
  }

  // Enviar notificación a todos los usuarios
  broadcast(event, data) {
    this.io.emit(event, data);
  }

  // Enviar notificación de nueva tarea
  notifyNewTask(task, assignedTo) {
    this.sendToUser(assignedTo, 'newTask', {
      message: `Nueva tarea asignada: ${task.title}`,
      task
    });
  }

  // Notificar cambio de estado en tarea
  notifyTaskStatusChange(task, updatedBy) {
    const message = `Tarea "${task.title}" cambió a estado: ${task.status}`;
    this.sendToUser(task.assignedTo, 'taskUpdate', { message, task });
    this.sendToManagers('taskUpdate', { message, task });
  }

  // Notificar nueva solicitud de tiempo
  notifyTimeRequest(timeEntry, requestedBy) {
    this.sendToManagers('newTimeRequest', {
      message: `Nueva solicitud de tiempo de ${requestedBy.name}`,
      timeEntry
    });
  }

  // Notificar aprobación/rechazo de tiempo
  notifyTimeApprovalUpdate(timeEntry, action) {
    this.sendToUser(timeEntry.userId, 'timeApprovalUpdate', {
      message: `Tu registro de tiempo ha sido ${action === 'approved' ? 'aprobado' : 'rechazado'}`,
      timeEntry
    });
  }

  // Notificar nueva solicitud de permiso
  notifyPermissionRequest(permission) {
    this.sendToManagers('newPermissionRequest', {
      message: `Nueva solicitud de permiso de ${permission.user.name}`,
      permission
    });
  }

  // Notificar actualización de permiso
  notifyPermissionUpdate(permission, status) {
    try {
      console.log(`Notificando actualización de permiso: ${permission.id}, estado: ${status}`);
      
      let message = 'Tu solicitud de permiso ha sido actualizada';
      if (status === 'APPROVED') {
        message = 'Tu solicitud de permiso ha sido aprobada';
      } else if (status === 'REJECTED') {
        message = 'Tu solicitud de permiso ha sido rechazada';
      }
      
      this.sendToUser(permission.userId, 'permissionUpdate', {
        message,
        permission
      });
    } catch (error) {
      console.error('Error al notificar actualización de permiso:', error);
    }
  }

  // Notificar sincronización LDAP completada
  notifyLDAPSync(stats) {
    this.sendToManagers('ldapSync', {
      message: 'Sincronización LDAP completada',
      stats
    });
  }

  // Notificar backup completado
  notifyBackupComplete(backup) {
    this.sendToManagers('backupComplete', {
      message: 'Backup completado exitosamente',
      backup
    });
  }

  // Notify task movement
  notifyTaskMovement(task) {
    this.broadcast('taskMoved', {
      task,
      message: `Task "${task.title}" moved to ${task.status}`
    });
  }
  
  // Update timer sync method
  notifyTimerUpdate(taskId, timerData) {
    // Broadcast to all clients including sender
    this.io.to(`task_${taskId}`).emit(`taskTimer_${taskId}`, {
      ...timerData,
      timestamp: Date.now(),
      taskId
    });
  }

  // Add method to join task timer room
  joinTaskRoom(socket, taskId) {
    console.log(`Usuario ${socket.user.email} unido a sala de tarea ${taskId}`);
    socket.join(`task_${taskId}`);
  }

  // Add method to leave task room
  leaveTaskRoom(socket, taskId) {
    console.log(`Usuario ${socket.user.email} abandonó sala de tarea ${taskId}`);
    socket.leave(`task_${taskId}`);
  }

  // Add a new method to notify about work hour restrictions
  notifyWorkHourRestriction(userId, message) {
    this.sendToUser(userId, 'workHourRestriction', {
      message,
      timestamp: Date.now()
    });
  }

  // Notify global timer updates
  notifyGlobalTimerUpdate(taskData) {
    // Broadcast to all clients about global timer changes
    this.broadcast('globalTimerUpdate', {
      task: taskData,
      timestamp: Date.now()
    });

    // Also send to specific task room if exists
    this.io.to(`task_${taskData.id}`).emit('globalTimerUpdate', {
      task: taskData,
      timestamp: Date.now()
    });
  }
}

module.exports = new SocketService();
