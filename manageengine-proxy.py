#!/usr/bin/env python3
import http.server
import urllib.request
import urllib.parse
import ssl
import json
from urllib.error import URLError

# Configuración
PROXY_PORT = 8081
TARGET_HOST = '************'
TARGET_PORT = 8080

class ProxyHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.handle_request()
    
    def do_POST(self):
        self.handle_request()
    
    def do_PUT(self):
        self.handle_request()
    
    def do_DELETE(self):
        self.handle_request()
    
    def do_OPTIONS(self):
        # Manejar preflight CORS
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, authtoken, Accept, PORTALID')
        self.end_headers()
    
    def handle_request(self):
        try:
            print(f"📥 Proxy request: {self.command} {self.path}")
            
            # Construir URL de destino
            target_url = f"https://{TARGET_HOST}:{TARGET_PORT}{self.path}"
            print(f"🔗 Forwarding to: {target_url}")
            
            # Preparar headers
            headers = {}
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'connection']:
                    headers[header] = value
            
            print(f"📋 Headers: {headers}")
            
            # Leer body si existe
            content_length = int(self.headers.get('Content-Length', 0))
            body = None
            if content_length > 0:
                body = self.rfile.read(content_length)
            
            # Crear request
            req = urllib.request.Request(target_url, data=body, headers=headers, method=self.command)
            
            # Crear contexto SSL que ignore certificados
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # Ejecutar petición
            with urllib.request.urlopen(req, context=ssl_context, timeout=30) as response:
                print(f"✅ Response status: {response.status}")
                
                # Enviar respuesta
                self.send_response(response.status)
                
                # Copiar headers
                for header, value in response.headers.items():
                    if header.lower() not in ['connection', 'transfer-encoding']:
                        self.send_header(header, value)
                
                # Headers CORS
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                # Copiar body
                self.wfile.write(response.read())
                
        except URLError as e:
            print(f"❌ URL Error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            error_response = json.dumps({"error": str(e)}).encode()
            self.wfile.write(error_response)
            
        except Exception as e:
            print(f"❌ Proxy error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            error_response = json.dumps({"error": str(e)}).encode()
            self.wfile.write(error_response)

if __name__ == '__main__':
    server = http.server.HTTPServer(('0.0.0.0', PROXY_PORT), ProxyHandler)
    print(f"🚀 ManageEngine Proxy running on port {PROXY_PORT}")
    print(f"📡 Forwarding to: https://{TARGET_HOST}:{TARGET_PORT}")
    print(f"🔗 Use this URL in Docker: http://************:{PROXY_PORT}")
    print("Press Ctrl+C to stop")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Proxy stopped")
        server.shutdown()
