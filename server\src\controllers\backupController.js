const backupService = require('../services/backupService');

exports.createBackup = async (req, res) => {
  try {
    console.log('🔧 INICIANDO PROCESO DE BACKUP...');
    console.log('Usuario solicitante:', req.user?.id, req.user?.email, req.user?.role);

    const result = await backupService.createBackup();
    console.log('✅ BACKUP COMPLETADO EXITOSAMENTE:', result);

    res.json({
      success: true,
      message: 'Backup creado exitosamente',
      ...result
    });
  } catch (error) {
    console.error('❌ ERROR DETALLADO AL CREAR BACKUP:', {
      message: error.message,
      stack: error.stack,
      code: error.code,
      name: error.name
    });

    res.status(500).json({
      success: false,
      error: error.message,
      details: process.env.NODE_ENV === 'development' ? {
        stack: error.stack,
        code: error.code,
        name: error.name
      } : undefined
    });
  }
};

exports.restoreBackup = async (req, res) => {
  try {
    const { filename } = req.params;
    const result = await backupService.restoreBackup(filename);
    res.json(result);
  } catch (error) {
    console.error('Error al restaurar backup:', error);
    res.status(500).json({ error: error.message });
  }
};

exports.listBackups = async (req, res) => {
  try {
    const backups = await backupService.listBackups();
    res.json(backups);
  } catch (error) {
    console.error('Error al listar backups:', error);
    res.status(500).json({ error: error.message });
  }
};

exports.diagnoseBackup = async (req, res) => {
  try {
    console.log('Iniciando diagnóstico de backup...');
    const result = await backupService.diagnoseBackupIssues();
    console.log('Diagnóstico completado');
    res.json(result);
  } catch (error) {
    console.error('Error en diagnóstico de backup:', error);
    res.status(500).json({ error: error.message });
  }
};

exports.downloadBackup = async (req, res) => {
  try {
    const { filename } = req.params;
    console.log('🔽 INICIANDO DESCARGA DE BACKUP:', filename);
    console.log('Usuario solicitante:', req.user?.id, req.user?.email, req.user?.role);

    // Validar nombre de archivo
    if (!filename || !filename.endsWith('.sql')) {
      return res.status(400).json({
        success: false,
        error: 'Nombre de archivo inválido'
      });
    }

    const result = await backupService.downloadBackup(filename);

    // Configurar headers para descarga
    res.setHeader('Content-Type', 'application/sql');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', result.size);

    console.log('✅ DESCARGA INICIADA:', {
      filename,
      size: result.size,
      path: result.path
    });

    // Enviar archivo
    res.sendFile(result.path);
  } catch (error) {
    console.error('❌ ERROR AL DESCARGAR BACKUP:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

exports.uploadBackup = async (req, res) => {
  try {
    console.log('🔼 INICIANDO CARGA DE BACKUP...');
    console.log('Usuario solicitante:', req.user?.id, req.user?.email, req.user?.role);

    // Verificar que se subió un archivo
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No se proporcionó ningún archivo'
      });
    }

    console.log('📁 ARCHIVO RECIBIDO:', {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      filename: req.file.filename
    });

    const result = await backupService.uploadBackup(req.file);

    console.log('✅ BACKUP CARGADO EXITOSAMENTE:', result);
    res.json({
      success: true,
      message: 'Backup cargado exitosamente',
      ...result
    });
  } catch (error) {
    console.error('❌ ERROR AL CARGAR BACKUP:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};



