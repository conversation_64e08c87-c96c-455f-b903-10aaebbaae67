const calendarService = require('../services/calendarService');

exports.syncHolidays = async (req, res) => {
  try {
    const holidays = await calendarService.syncHolidays();
    res.json({ message: 'Feriados sincronizados exitosamente', holidays });
  } catch (error) {
    console.error('Error al sincronizar feriados:', error);
    res.status(500).json({ error: 'Error al sincronizar feriados' });
  }
};

exports.getHolidays = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    const start = startDate ? new Date(startDate) : new Date();
    const end = endDate ? new Date(endDate) : new Date(start.getFullYear(), 11, 31);

    const holidays = await calendarService.getHolidays(start, end);
    res.json(holidays);
  } catch (error) {
    console.error('Error al obtener feriados:', error);
    res.status(500).json({ error: 'Error al obtener feriados' });
  }
};
