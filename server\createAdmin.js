const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdmin() {
  try {
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        password: hashedPassword,
        role: 'ADMIN',
        twoFactorEnabled: false,
        isLDAPUser: false,
      },
    });
    
    console.log('Usuario administrador creado:');
    console.log(`Email: ${user.email}`);
    console.log('Contraseña: admin123');
    console.log(`Rol: ${user.role}`);
    
  } catch (error) {
    if (error.code === 'P2002') {
      console.log('<NAME_EMAIL> ya existe.');
    } else {
      console.error('Error al crear el administrador:', error);
    }
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin();
