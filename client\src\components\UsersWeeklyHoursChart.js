import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  NavigateBefore as PrevIcon,
  NavigateNext as NextIcon,
  Today as TodayIcon,
} from '@mui/icons-material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import axios from 'axios';
import { format, startOfWeek, endOfWeek, addWeeks, subWeeks } from 'date-fns';
import { es } from 'date-fns/locale';
import { API_URL } from '../config';

const UsersWeeklyHoursChart = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [usersData, setUsersData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [tabValue, setTabValue] = useState(0);
  const [filterRole, setFilterRole] = useState('ALL');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedWeek, setSelectedWeek] = useState(new Date()); // Semana actual por defecto

  useEffect(() => {
    fetchUsersWeeklyHours();
  }, [selectedWeek]); // Recargar cuando cambie la semana seleccionada

  useEffect(() => {
    // Aplicar filtros cuando cambian los datos o los filtros
    applyFilters();
  }, [usersData, filterRole, searchTerm]);

  // Update the fetchUsersWeeklyHours function to use the correct API URL
  const fetchUsersWeeklyHours = async () => {
    try {
      setLoading(true);

      // Calcular el rango de la semana seleccionada
      const weekStart = startOfWeek(selectedWeek, { weekStartsOn: 1 }); // Lunes como inicio
      const weekEnd = endOfWeek(selectedWeek, { weekStartsOn: 1 });

      console.log('📅 OBTENIENDO DATOS PARA SEMANA:', {
        selectedWeek: format(selectedWeek, 'yyyy-MM-dd'),
        weekStart: format(weekStart, 'yyyy-MM-dd'),
        weekEnd: format(weekEnd, 'yyyy-MM-dd')
      });

      // Use the same base URL as in the Dashboard component
      const response = await axios.get(`${API_URL}/api/dashboard/users-weekly-hours`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        params: {
          startDate: format(weekStart, 'yyyy-MM-dd'),
          endDate: format(weekEnd, 'yyyy-MM-dd')
        }
      });

      // Add console log to debug the response
      console.log('Users weekly hours response:', response.data);

      // Check if the response data is valid
      if (Array.isArray(response.data)) {
        setUsersData(response.data);
        setFilteredData(response.data);
        setError(null);
      } else {
        setError('Formato de datos inválido');
        console.error('Invalid data format:', response.data);
      }
    } catch (err) {
      console.error('Error al obtener horas semanales de usuarios:', err);
      setError(`Error al cargar datos de usuarios: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...usersData];

    // Filtrar por rol
    if (filterRole !== 'ALL') {
      filtered = filtered.filter(user => user.role === filterRole);
    }

    // Filtrar por término de búsqueda
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(term) ||
        user.email.toLowerCase().includes(term)
      );
    }

    setFilteredData(filtered);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleRoleFilterChange = (event) => {
    setFilterRole(event.target.value);
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Funciones para navegación de semanas
  const handlePrevWeek = () => {
    setSelectedWeek(prevWeek => subWeeks(prevWeek, 1));
  };

  const handleNextWeek = () => {
    setSelectedWeek(prevWeek => addWeeks(prevWeek, 1));
  };

  const handleCurrentWeek = () => {
    setSelectedWeek(new Date());
  };

  // Función para obtener el rango de fechas de la semana actual
  const getCurrentWeekRange = () => {
    const weekStart = startOfWeek(selectedWeek, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(selectedWeek, { weekStartsOn: 1 });
    return `${format(weekStart, 'dd/MM/yyyy', { locale: es })} - ${format(weekEnd, 'dd/MM/yyyy', { locale: es })}`;
  };

  // Verificar si es la semana actual
  const isCurrentWeek = () => {
    const now = new Date();
    const currentWeekStart = startOfWeek(now, { weekStartsOn: 1 });
    const selectedWeekStart = startOfWeek(selectedWeek, { weekStartsOn: 1 });
    return format(currentWeekStart, 'yyyy-MM-dd') === format(selectedWeekStart, 'yyyy-MM-dd');
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  // Preparar datos para el gráfico de barras
  const chartData = filteredData.map(user => ({
    name: user.name,
    horas: parseFloat(user.currentWeekHours),
    email: user.email,
    role: user.role,
  }));

  return (
    <Paper sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Horas Semanales por Usuario
        </Typography>

        {/* Navegación de semanas */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="Semana anterior">
            <IconButton onClick={handlePrevWeek} size="small">
              <PrevIcon />
            </IconButton>
          </Tooltip>

          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            minWidth: '200px',
            textAlign: 'center'
          }}>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {getCurrentWeekRange()}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {isCurrentWeek() ? 'Semana Actual' : format(selectedWeek, 'MMMM yyyy', { locale: es })}
            </Typography>
          </Box>

          <Tooltip title="Semana siguiente">
            <IconButton onClick={handleNextWeek} size="small">
              <NextIcon />
            </IconButton>
          </Tooltip>

          {!isCurrentWeek() && (
            <Tooltip title="Ir a semana actual">
              <Button
                variant="outlined"
                size="small"
                startIcon={<TodayIcon />}
                onClick={handleCurrentWeek}
                sx={{ ml: 1 }}
              >
                Actual
              </Button>
            </Tooltip>
          )}
        </Box>
      </Box>

      {/* Filtros */}
      <Box sx={{ display: 'flex', mb: 2, gap: 2 }}>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Rol</InputLabel>
          <Select
            value={filterRole}
            label="Rol"
            onChange={handleRoleFilterChange}
          >
            <MenuItem value="ALL">Todos</MenuItem>
            <MenuItem value="ADMIN">Administrador</MenuItem>
            <MenuItem value="MANAGER">Gerente</MenuItem>
            <MenuItem value="SUPERVISOR">Supervisor</MenuItem>
            <MenuItem value="EMPLOYEE">Empleado</MenuItem>
          </Select>
        </FormControl>

        <TextField
          label="Buscar usuario"
          variant="outlined"
          size="small"
          value={searchTerm}
          onChange={handleSearchChange}
          sx={{ flexGrow: 1 }}
        />
      </Box>

      {/* Tabs para cambiar entre vista de gráfico y tabla */}
      <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 2 }}>
        <Tab label="Gráfico" />
        <Tab label="Tabla" />
        <Tab label="Historial" />
      </Tabs>

      {/* Vista de gráfico */}
      {tabValue === 0 && (
        <Box sx={{ height: 400 }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 70,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="name"
                angle={-45}
                textAnchor="end"
                height={70}
              />
              <YAxis label={{ value: 'Horas', angle: -90, position: 'insideLeft' }} />
              <RechartsTooltip
                formatter={(value, name) => [`${value}h`, 'Horas Trabajadas']}
                labelFormatter={(value) => `Usuario: ${value}`}
              />
              <Legend />
              <Bar dataKey="horas" name={`Horas ${isCurrentWeek() ? 'Semana Actual' : 'de la Semana'}`} fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </Box>
      )}

      {/* Vista de tabla */}
      {tabValue === 1 && (
        <TableContainer sx={{ maxHeight: 400 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>Usuario</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Rol</TableCell>
                <TableCell align="right">
                  Horas de la Semana
                  <Typography variant="caption" display="block">
                    {getCurrentWeekRange()}
                  </Typography>
                </TableCell>
                <TableCell>Rango de Fechas</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredData.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.role}</TableCell>
                  <TableCell align="right">{user.currentWeekHours}h</TableCell>
                  <TableCell>{user.currentWeekRange || 'N/A'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Vista de historial */}
      {tabValue === 2 && (
        <TableContainer sx={{ maxHeight: 400 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>Usuario</TableCell>
                <TableCell>
                  Semana Actual
                  <Typography variant="caption" display="block">
                    {filteredData[0]?.currentWeekRange || ''}
                  </Typography>
                </TableCell>
                {filteredData[0]?.previousWeeks.map((week, index) => (
                  <TableCell key={index}>
                    {week.weekStart}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredData.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.currentWeekHours}h</TableCell>
                  {user.previousWeeks.map((week, index) => (
                    <TableCell key={index}>{week.hours}h</TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Paper>
  );
};

export default UsersWeeklyHoursChart;