/**
 * Utilidad para mapear tipos de permisos entre enum de BD y nombres en español
 */

// Mapeo de enum de BD a español
const PERMISSION_TYPE_TO_SPANISH = {
  'VACATION': 'Vacaciones',
  'SICK_LEAVE': 'Enfermedad',
  'PERSONAL_LEAVE': 'Personal',
  'OVERTIME_COMPENSATION': 'Compensación de horas extra',
  'OTHER': 'Otro<PERSON>'
};

// Mapeo de español a enum de BD
const SPANISH_TO_PERMISSION_TYPE = {
  'Vacaciones': 'VACATION',
  'Enfermedad': 'SICK_LEAVE',
  'Descanso médico': 'SICK_LEAVE',
  'Personal': 'PERSONAL_LEAVE',
  'Permiso personal': 'PERSONAL_LEAVE',
  'Compensación de horas extra': 'OVERTIME_COMPENSATION',
  'Compensacion': 'OVERTIME_COMPENSATION',
  'Maternidad': 'OTHER',
  'Paternidad': 'OTHER',
  '<PERSON>lo': 'OTHER',
  'Matrimonio': 'OTHER',
  'O<PERSON><PERSON>': 'OTHER'
};

/**
 * Convierte un tipo de permiso del enum de BD a español
 * @param {string} enumType - Tipo del enum (ej: 'VACATION')
 * @returns {string} Nombre en español (ej: 'Vacaciones')
 */
function enumToSpanish(enumType) {
  return PERMISSION_TYPE_TO_SPANISH[enumType] || enumType;
}

/**
 * Convierte un nombre en español a enum de BD
 * @param {string} spanishName - Nombre en español (ej: 'Vacaciones')
 * @returns {string} Tipo del enum (ej: 'VACATION')
 */
function spanishToEnum(spanishName) {
  return SPANISH_TO_PERMISSION_TYPE[spanishName] || 'OTHER';
}

/**
 * Convierte un array de permisos con tipos en enum a español
 * @param {Array} permissions - Array de permisos con campo 'type'
 * @returns {Array} Array con tipos convertidos a español
 */
function convertPermissionsToSpanish(permissions) {
  return permissions.map(permission => ({
    ...permission,
    type: enumToSpanish(permission.type)
  }));
}

/**
 * Convierte estadísticas agrupadas por tipo a español
 * @param {Array} stats - Array de estadísticas con campo 'type'
 * @returns {Array} Array con tipos convertidos a español
 */
function convertStatsToSpanish(stats) {
  return stats.map(stat => ({
    ...stat,
    type: enumToSpanish(stat.type)
  }));
}

/**
 * Obtiene todos los tipos de permisos en español
 * @returns {Array} Array de objetos con value (enum) y label (español)
 */
function getAllPermissionTypesInSpanish() {
  return Object.entries(PERMISSION_TYPE_TO_SPANISH).map(([enumValue, spanishLabel]) => ({
    value: enumValue,
    label: spanishLabel
  }));
}

module.exports = {
  enumToSpanish,
  spanishToEnum,
  convertPermissionsToSpanish,
  convertStatsToSpanish,
  getAllPermissionTypesInSpanish,
  PERMISSION_TYPE_TO_SPANISH,
  SPANISH_TO_PERMISSION_TYPE
};
