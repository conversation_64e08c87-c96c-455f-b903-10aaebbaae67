const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testCalendarAPI() {
  try {
    console.log('🔍 PROBANDO API DEL CALENDARIO');
    console.log('='.repeat(60));

    // Simular la llamada a getCompletedTasksByDate para el viernes 4 de julio
    const date = '2025-07-04';
    console.log(`📅 Probando fecha: ${date}`);

    // Obtener configuración de WorkSchedule
    const workSchedules = await prisma.workSchedule.findMany();
    console.log(`⚙️ Horarios configurados: ${workSchedules.length}`);

    // Obtener TimeEntries aprobados para esa fecha
    const timeEntries = await prisma.timeEntry.findMany({
      where: {
        status: 'APPROVED',
        startTime: {
          gte: new Date(`${date}T00:00:00.000Z`),
          lt: new Date(`${date}T23:59:59.999Z`)
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    console.log(`📝 TimeEntries encontrados: ${timeEntries.length}`);

    // Procesar cada entrada como lo hace el código real
    const virtualTasks = timeEntries.map(entry => {
      console.log(`\n🔍 Procesando: ${entry.title}`);
      console.log(`   Inicio UTC: ${entry.startTime}`);
      console.log(`   Fin UTC: ${entry.endTime}`);

      // APLICAR LA NUEVA LÓGICA CON CONVERSIÓN A HORA PERUANA
      const entryDateUTC = new Date(entry.startTime);
      const entryEndUTC = new Date(entry.endTime);
      
      // Convertir a hora peruana (UTC-5)
      const entryDatePeru = new Date(entryDateUTC.getTime() - 5 * 60 * 60 * 1000);
      const entryEndPeru = new Date(entryEndUTC.getTime() - 5 * 60 * 60 * 1000);
      
      console.log(`   Inicio Perú: ${entryDatePeru.toISOString()} (${entryDatePeru.getHours()}:${entryDatePeru.getMinutes().toString().padStart(2, '0')})`);
      console.log(`   Fin Perú: ${entryEndPeru.toISOString()} (${entryEndPeru.getHours()}:${entryEndPeru.getMinutes().toString().padStart(2, '0')})`);

      const dayOfWeek = entryDatePeru.getDay();
      const workSchedule = workSchedules.find(ws => ws.dayOfWeek === dayOfWeek);
      
      let isOutsideOfficeHours = false;
      if (workSchedule) {
        const officeStart = workSchedule.startTime.split(':');
        const officeEnd = workSchedule.endTime.split(':');
        const officeStartMinutes = parseInt(officeStart[0]) * 60 + parseInt(officeStart[1]);
        const officeEndMinutes = parseInt(officeEnd[0]) * 60 + parseInt(officeEnd[1]);
        const entryStartMinutes = entryDatePeru.getHours() * 60 + entryDatePeru.getMinutes();
        const entryEndMinutes = entryEndPeru.getHours() * 60 + entryEndPeru.getMinutes();
        
        isOutsideOfficeHours = entryStartMinutes < officeStartMinutes || entryEndMinutes > officeEndMinutes;
        
        console.log(`   Horario oficina: ${workSchedule.startTime} - ${workSchedule.endTime}`);
        console.log(`   Minutos oficina: ${officeStartMinutes} - ${officeEndMinutes}`);
        console.log(`   Minutos entrada: ${entryStartMinutes} - ${entryEndMinutes}`);
        console.log(`   ¿Fuera de horario? ${isOutsideOfficeHours ? '🔴 SÍ' : '🟢 NO'}`);
      } else {
        isOutsideOfficeHours = true;
        console.log(`   ❌ No hay horario para día ${dayOfWeek} - FUERA DE HORARIO`);
      }

      return {
        id: `timeentry-${entry.id}`,
        title: entry.title,
        startTime: entry.startTime,
        endTime: entry.endTime,
        isOutsideOfficeHours,
        type: 'manual'
      };
    });

    console.log(`\n📊 RESUMEN DE RESULTADOS:`);
    virtualTasks.forEach(task => {
      console.log(`   ${task.title}: ${task.isOutsideOfficeHours ? '🔴 ROJO' : '🟢 VERDE'}`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testCalendarAPI();
