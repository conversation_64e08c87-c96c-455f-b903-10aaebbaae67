const express = require('express');
const router = express.Router();
const settingsController = require('../controllers/settingsController');
const { authenticateToken } = require('../middleware/auth');

// Rutas para horarios de trabajo
router.post('/work-schedule', authenticateToken, settingsController.createWorkSchedule);
router.get('/work-schedules', authenticateToken, settingsController.getWorkSchedules);

// Rutas para multiplicadores de tiempo
router.post('/multipliers', authenticateToken, settingsController.createTimeMultiplier);
router.get('/multipliers', authenticateToken, settingsController.getTimeMultipliers);
router.put('/multipliers/:id', authenticateToken, settingsController.updateTimeMultiplier);
router.delete('/multipliers/:id', authenticateToken, settingsController.deleteTimeMultiplier);

// Rutas para tipos de permisos
router.post('/permission-types', authenticateToken, settingsController.createPermissionType);
router.get('/permission-types', authenticateToken, settingsController.getPermissionTypes);
router.put('/permission-types/:id', authenticateToken, settingsController.updatePermissionType);
router.delete('/permission-types/:id', authenticateToken, settingsController.deletePermissionType);

module.exports = router;
