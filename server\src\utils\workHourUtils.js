const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Checks if the current time is within work hours
 * @returns {Promise<{isWithinWorkHours: boolean, message: string, debug: object}>}
 */
async function isWithinWorkHours() {
  try {
    // Obtener la configuración de horario de trabajo
    const workSchedules = await prisma.workSchedule.findMany();
    
    // Use Lima, Peru time zone (UTC-5)
    const now = new Date();
    // Adjust to Lima, Peru time zone
    const limaOffset = -5 * 60; // Lima is UTC-5
    const serverOffset = now.getTimezoneOffset();
    const offsetDiff = limaOffset - serverOffset;
    
    // Create a new date adjusted to Lima time
    const limaDate = new Date(now.getTime() + offsetDiff * 60000);
    
    const currentDay = limaDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentTime = limaDate.getHours() * 60 + limaDate.getMinutes(); // Tiempo actual en minutos
    
    console.log(`[WorkHourCheck] Server time: ${now.toLocaleString()}`);
    console.log(`[WorkHourCheck] Lima, Peru time: ${limaDate.toLocaleString()}`);
    console.log(`[WorkHourCheck] Current day in Lima: ${currentDay} (${getDayName(currentDay)})`);
    
    if (workSchedules.length === 0) {
      // Si no hay horarios configurados, permitir cualquier hora
      console.log('[WorkHourCheck] No work schedules configured, allowing all hours');
      return { 
        isWithinWorkHours: true, 
        message: '',
        debug: { 
          reason: 'no_schedules', 
          schedules: [],
          serverTime: now.toLocaleString(),
          limaTime: limaDate.toLocaleString()
        }
      };
    }
    
    console.log(`[WorkHourCheck] Found ${workSchedules.length} work schedule configurations`);
    
    // Verificar si el día actual es un día laboral
    const todaySchedule = workSchedules.find(schedule => schedule.dayOfWeek === currentDay);
    
    if (!todaySchedule) {
      console.log(`[WorkHourCheck] Today (${getDayName(currentDay)}) is not a work day`);
      return { 
        isWithinWorkHours: false, 
        message: `No se pueden mover tareas a "En Progreso" fuera del horario laboral. Hoy (${getDayName(currentDay)}) no es un día laboral.`,
        debug: { 
          reason: 'not_work_day', 
          currentDay, 
          workDays: workSchedules.map(s => s.dayOfWeek),
          serverTime: now.toLocaleString(),
          limaTime: limaDate.toLocaleString()
        }
      };
    }
    
    // Convertir horarios a minutos para comparación
    const startTimeParts = todaySchedule.startTime.split(':');
    const endTimeParts = todaySchedule.endTime.split(':');
    
    const startTimeMinutes = parseInt(startTimeParts[0]) * 60 + parseInt(startTimeParts[1]);
    const endTimeMinutes = parseInt(endTimeParts[0]) * 60 + parseInt(endTimeParts[1]);
    
    console.log(`[WorkHourCheck] Work hours today: ${todaySchedule.startTime} - ${todaySchedule.endTime}`);
    console.log(`[WorkHourCheck] Current time in minutes: ${currentTime}, Work hours: ${startTimeMinutes} - ${endTimeMinutes}`);
    
    // Verificar si la hora actual está dentro del horario laboral
    if (currentTime < startTimeMinutes || currentTime > endTimeMinutes) {
      console.log(`[WorkHourCheck] Current time is outside work hours`);
      return { 
        isWithinWorkHours: false, 
        message: `No se pueden mover tareas a "En Progreso" fuera del horario laboral (${todaySchedule.startTime} - ${todaySchedule.endTime}).`,
        debug: { 
          reason: 'outside_hours', 
          currentTime, 
          startTimeMinutes, 
          endTimeMinutes,
          serverTime: now.toLocaleString(),
          limaTime: limaDate.toLocaleString()
        }
      };
    }
    
    console.log(`[WorkHourCheck] Current time is within work hours`);
    return { 
      isWithinWorkHours: true, 
      message: '',
      debug: { 
        reason: 'within_hours', 
        currentTime, 
        startTimeMinutes, 
        endTimeMinutes,
        serverTime: now.toLocaleString(),
        limaTime: limaDate.toLocaleString()
      }
    };
  } catch (error) {
    console.error('Error al verificar horario de trabajo:', error);
    // En caso de error, permitir la operación
    return { 
      isWithinWorkHours: true, 
      message: '',
      debug: { reason: 'error', error: error.message }
    };
  }
}

function getDayName(dayNumber) {
  const days = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
  return days[dayNumber];
}

/**
 * Mueve automáticamente las tareas de IN_PROGRESS a REVIEW cuando termina el horario laboral
 * y pausa los temporizadores activos
 * @returns {Promise<{success: boolean, count: number, message: string}>}
 */
async function moverTareasARevision() {
  try {
    // Verificar si estamos fuera del horario laboral
    const { isWithinWorkHours: withinHours, message, debug } = await isWithinWorkHours();
    
    if (withinHours) {
      console.log('[MoverTareas] Actualmente en horario laboral, no se requiere acción');
      return { success: true, count: 0, message: 'Dentro del horario laboral, no se movieron tareas' };
    }
    
    // Encontrar todas las tareas en estado IN_PROGRESS
    const tareasEnProgreso = await prisma.task.findMany({
      where: {
        status: 'IN_PROGRESS'
      },
      include: {
        assignee: true,
        activeTimers: true
      }
    });
    
    if (tareasEnProgreso.length === 0) {
      console.log('[MoverTareas] No se encontraron tareas en progreso');
      return { success: true, count: 0, message: 'No se encontraron tareas en progreso' };
    }
    
    console.log(`[MoverTareas] Se encontraron ${tareasEnProgreso.length} tareas para mover a revisión`);
    
    // Mover cada tarea a REVIEW y pausar sus temporizadores
    const actualizaciones = [];
    const temporizadoresActualizados = [];
    
    for (const tarea of tareasEnProgreso) {
      // Actualizar la tarea a estado REVIEW
      actualizaciones.push(
        prisma.task.update({
          where: { id: tarea.id },
          data: {
            status: 'REVIEW'
          }
        })
      );
      
      // Pausar todos los temporizadores activos asociados a esta tarea
      for (const temporizador of tarea.activeTimers) {
        if (temporizador.isRunning) {
          const ahora = new Date();
          const segundosTranscurridos = Math.floor((ahora - new Date(temporizador.lastStarted)) / 1000);
          
          temporizadoresActualizados.push(
            prisma.activeTimer.update({
              where: { id: temporizador.id },
              data: {
                isRunning: false,
                accumulatedTime: temporizador.accumulatedTime + segundosTranscurridos
              }
            })
          );
        }
      }
      
      console.log(`[MoverTareas] Moviendo tarea "${tarea.title}" a REVIEW y pausando ${tarea.activeTimers.filter(t => t.isRunning).length} temporizadores`);
    }
    
    // Ejecutar todas las actualizaciones
    await Promise.all([...actualizaciones, ...temporizadoresActualizados]);
    
    console.log(`[MoverTareas] Se movieron con éxito ${actualizaciones.length} tareas a REVIEW y se pausaron ${temporizadoresActualizados.length} temporizadores`);
    
    // Notificar a todos los clientes sobre los cambios
    const socketService = require('../services/socketService');
    socketService.notifyBoardUpdate();
    
    return { 
      success: true, 
      count: actualizaciones.length, 
      message: `Se movieron ${actualizaciones.length} tareas a revisión y se pausaron ${temporizadoresActualizados.length} temporizadores debido al fin del horario laboral` 
    };
  } catch (error) {
    console.error('[MoverTareas] Error al mover tareas a revisión:', error);
    return { 
      success: false, 
      count: 0, 
      message: `Error al mover tareas a revisión: ${error.message}` 
    };
  }
}

/**
 * Obtiene los horarios de fin de jornada de todos los días configurados
 * @returns {Promise<Array<{dayOfWeek: number, endTime: string}>>}
 */
async function getWorkScheduleEndTimes() {
  try {
    // Obtener todos los horarios de trabajo de la base de datos
    const workSchedules = await prisma.workSchedule.findMany({
      select: {
        dayOfWeek: true,
        endTime: true
      }
    });
    
    // Si no hay horarios configurados, usar un horario predeterminado (18:00 de lunes a viernes)
    if (workSchedules.length === 0) {
      console.log('[getWorkScheduleEndTimes] No hay horarios configurados, usando valores predeterminados');
      return [
        { dayOfWeek: 1, endTime: '18:00' }, // Lunes
        { dayOfWeek: 2, endTime: '18:00' }, // Martes
        { dayOfWeek: 3, endTime: '18:00' }, // Miércoles
        { dayOfWeek: 4, endTime: '18:00' }, // Jueves
        { dayOfWeek: 5, endTime: '18:00' }  // Viernes
      ];
    }
    
    console.log(`[getWorkScheduleEndTimes] Horarios obtenidos: ${JSON.stringify(workSchedules)}`);
    return workSchedules;
  } catch (error) {
    console.error('[getWorkScheduleEndTimes] Error al obtener horarios:', error);
    // En caso de error, devolver horario predeterminado
    return [
      { dayOfWeek: 1, endTime: '18:00' }, // Lunes
      { dayOfWeek: 2, endTime: '18:00' }, // Martes
      { dayOfWeek: 3, endTime: '18:00' }, // Miércoles
      { dayOfWeek: 4, endTime: '18:00' }, // Jueves
      { dayOfWeek: 5, endTime: '18:00' }  // Viernes
    ];
  }
}

// Actualizar el módulo exportado para incluir la nueva función
module.exports = {
  isWithinWorkHours,
  moverTareasARevision,
  getWorkScheduleEndTimes
};