const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkCurrentWorkSchedule() {
  try {
    console.log('🔍 VERIFICANDO CONFIGURACIÓN ACTUAL DE WORKSCHEDULE');
    console.log('='.repeat(60));

    // 1. Obtener configuración actual de WorkSchedule
    const workSchedules = await prisma.workSchedule.findMany({
      orderBy: { dayOfWeek: 'asc' }
    });

    console.log(`\n📅 HORARIOS DE TRABAJO CONFIGURADOS: ${workSchedules.length}`);
    
    if (workSchedules.length > 0) {
      const dayNames = ['Domingo', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Mi<PERSON><PERSON><PERSON><PERSON>', 'Ju<PERSON>', 'Viernes', 'Sábado'];
      workSchedules.forEach(schedule => {
        console.log(`  - ${dayNames[schedule.dayOfWeek]} (${schedule.dayOfWeek}): ${schedule.startTime} - ${schedule.endTime}`);
      });

      // Verificar específicamente el viernes (día 5)
      const fridaySchedule = workSchedules.find(ws => ws.dayOfWeek === 5);
      if (fridaySchedule) {
        console.log(`\n🎯 CONFIGURACIÓN ESPECÍFICA PARA VIERNES:`);
        console.log(`   Inicio: ${fridaySchedule.startTime}`);
        console.log(`   Fin: ${fridaySchedule.endTime}`);
        console.log(`   Almuerzo: ${fridaySchedule.lunchBreak} (${fridaySchedule.lunchDuration}min)`);
        
        // Simular el cálculo para 17:00-18:00
        const officeStart = fridaySchedule.startTime.split(':');
        const officeEnd = fridaySchedule.endTime.split(':');
        const officeStartMinutes = parseInt(officeStart[0]) * 60 + parseInt(officeStart[1]);
        const officeEndMinutes = parseInt(officeEnd[0]) * 60 + parseInt(officeEnd[1]);
        
        const testStartMinutes = 17 * 60 + 0; // 17:00
        const testEndMinutes = 18 * 60 + 0;   // 18:00
        
        console.log(`\n🧪 SIMULACIÓN PARA TAREA 17:00-18:00:`);
        console.log(`   Horario oficina: ${officeStartMinutes} - ${officeEndMinutes} minutos`);
        console.log(`   Tarea: ${testStartMinutes} - ${testEndMinutes} minutos`);
        
        const isOutsideOfficeHours = testStartMinutes < officeStartMinutes || testEndMinutes > officeEndMinutes;
        console.log(`   ¿Está fuera de horario? ${isOutsideOfficeHours ? '🔴 SÍ' : '🟢 NO'}`);
        
        if (isOutsideOfficeHours) {
          if (testStartMinutes < officeStartMinutes) {
            console.log(`   Razón: Inicia antes del horario (${testStartMinutes} < ${officeStartMinutes})`);
          }
          if (testEndMinutes > officeEndMinutes) {
            console.log(`   Razón: Termina después del horario (${testEndMinutes} > ${officeEndMinutes})`);
          }
        }
      } else {
        console.log(`\n❌ NO se encontró configuración para viernes (día 5)`);
      }
    } else {
      console.log('  ⚠️ No hay horarios configurados en la base de datos');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCurrentWorkSchedule();
