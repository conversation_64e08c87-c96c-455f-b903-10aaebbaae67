const { PrismaClient } = require('@prisma/client');
const permissionValidationService = require('../services/permissionValidationService');
const { enumToSpanish, spanishToEnum } = require('../utils/permissionTypeMapper');
const prisma = new PrismaClient();

// Importar socketService si existe, sino crear un mock
let socketService;
try {
  socketService = require('../services/socketService');
} catch (error) {
  // Mock del socketService si no existe
  socketService = {
    notifyPermissionRequest: () => console.log('📢 Mock: Permission request notification'),
    notifyPermissionUpdate: () => console.log('📢 Mock: Permission update notification')
  };
}

// Crear una nueva solicitud de permiso
exports.createPermission = async (req, res) => {
  try {
    const { type, startTime, endTime, reason, overtimeHoursUsed, overtimeHoursAvailable } = req.body;
    const userId = req.user.id;

    console.log('🔍 Validando nueva solicitud de permiso:', { type, startTime, endTime, userId });

    // Validar la solicitud de permiso
    const validationResult = await permissionValidationService.validatePermissionRequest(
      { type, startTime, endTime, reason, overtimeHoursUsed, overtimeHoursAvailable },
      userId
    );

    if (!validationResult.isValid) {
      console.log('❌ Validación fallida:', validationResult.errors);
      return res.status(400).json({
        error: 'Solicitud de permiso inválida',
        errors: validationResult.errors,
        warnings: validationResult.warnings,
        details: validationResult.details
      });
    }

    // Si hay advertencias, las incluimos en la respuesta pero permitimos continuar
    if (validationResult.warnings.length > 0) {
      console.log('⚠️ Advertencias en validación:', validationResult.warnings);
    }

    // Mapear el tipo de permiso al enum correcto
    const enumType = spanishToEnum(type);

    const permissionData = {
      type: enumType,
      startTime: new Date(startTime),
      endTime: new Date(endTime),
      reason,
      userId,
      status: 'PENDING'
    };

    // Agregar campos específicos para compensación de horas extra
    if (type === 'Compensación de horas extra' || enumType === 'OVERTIME_COMPENSATION') {
      permissionData.overtimeHoursUsed = overtimeHoursUsed;
      permissionData.overtimeHoursAvailable = overtimeHoursAvailable;
    }

    const permission = await prisma.permission.create({
      data: permissionData,
      include: {
        user: true
      }
    });

    console.log('✅ Permiso creado exitosamente:', permission.id);

    // Notificar nueva solicitud de permiso
    socketService.notifyPermissionRequest(permission);

    // Incluir advertencias en la respuesta si las hay
    const response = {
      ...permission,
      validationWarnings: validationResult.warnings
    };

    res.status(201).json(response);
  } catch (error) {
    console.error('Error al crear solicitud de permiso:', error);
    res.status(500).json({ error: 'Error al crear la solicitud de permiso' });
  }
};

// Obtener permisos del usuario
exports.getUserPermissions = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      status,
      type,
      startDate,
      endDate,
      permissionStartDate,
      permissionEndDate,
      searchText
    } = req.query;

    console.log('🔍 OBTENIENDO PERMISOS PARA USUARIO:', userId);
    console.log('🔍 FILTROS APLICADOS:', req.query);

    // Construir filtros dinámicamente
    const whereClause = { userId };

    // Filtro por estado
    if (status && status !== '') {
      whereClause.status = status;
    }

    // Filtro por tipo
    if (type && type !== '') {
      whereClause.type = type;
    }

    // Filtro por rango de fechas de creación
    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999); // Incluir todo el día
        whereClause.createdAt.lte = endDateTime;
      }
    }

    // Filtro por rango de fechas del permiso
    if (permissionStartDate || permissionEndDate) {
      if (permissionStartDate && permissionEndDate) {
        whereClause.OR = [
          {
            startTime: {
              gte: new Date(permissionStartDate),
              lte: new Date(permissionEndDate)
            }
          },
          {
            endTime: {
              gte: new Date(permissionStartDate),
              lte: new Date(permissionEndDate)
            }
          },
          {
            AND: [
              { startTime: { lte: new Date(permissionStartDate) } },
              { endTime: { gte: new Date(permissionEndDate) } }
            ]
          }
        ];
      }
    }

    // Filtro por texto de búsqueda (en razón)
    if (searchText && searchText.trim() !== '') {
      whereClause.reason = {
        contains: searchText.trim(),
        mode: 'insensitive'
      };
    }

    const permissions = await prisma.permission.findMany({
      where: whereClause,
      orderBy: {
        startTime: 'desc',
      },
    });

    console.log('✅ PERMISOS ENCONTRADOS:', permissions.length);
    console.log('📋 DETALLES DE PERMISOS:', permissions.map(p => ({
      id: p.id,
      type: p.type,
      status: p.status,
      startTime: p.startTime,
      endTime: p.endTime,
      overtimeHoursUsed: p.overtimeHoursUsed
    })));

    // Convertir tipos de permisos a español
    const permissionsInSpanish = permissions.map(permission => ({
      ...permission,
      type: enumToSpanish(permission.type)
    }));

    res.json(permissionsInSpanish);
  } catch (error) {
    console.error('❌ ERROR AL OBTENER PERMISOS:', error);
    console.error('Error details:', error);
    res.status(500).json({
      error: 'Error al obtener los permisos',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Aprobar o rechazar un permiso
exports.updatePermissionStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    console.log(`Actualizando estado del permiso ${id} a ${status}`);

    const permission = await prisma.permission.update({
      where: { id: id },
      data: {
        status,
        approverId: req.user.id,
        approvedAt: new Date()
      },
      include: {
        user: true,
        approver: true
      }
    });

    // Notificar actualización de permiso
    socketService.notifyPermissionUpdate(permission, status);

    res.json(permission);
  } catch (error) {
    console.error('Error al actualizar estado del permiso:', error);
    res.status(500).json({ error: 'Error al actualizar el estado del permiso' });
  }
};

// Obtener permisos pendientes (para administradores)
exports.getPendingPermissions = async (req, res) => {
  try {
    const {
      status,
      type,
      employeeId,
      startDate,
      endDate,
      permissionStartDate,
      permissionEndDate,
      searchText
    } = req.query;

    console.log('🔍 OBTENIENDO PERMISOS PARA ADMIN');
    console.log('🔍 FILTROS APLICADOS:', req.query);

    // Construir filtros dinámicamente
    const whereClause = {};

    // Filtro por estado
    if (status && status !== '') {
      whereClause.status = status;
    }

    // Filtro por tipo
    if (type && type !== '') {
      whereClause.type = type;
    }

    // Filtro por empleado específico
    if (employeeId && employeeId !== '') {
      whereClause.userId = employeeId;
    }

    // Filtro por rango de fechas de creación
    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999); // Incluir todo el día
        whereClause.createdAt.lte = endDateTime;
      }
    }

    // Filtro por rango de fechas del permiso
    if (permissionStartDate || permissionEndDate) {
      if (permissionStartDate && permissionEndDate) {
        whereClause.OR = [
          {
            startTime: {
              gte: new Date(permissionStartDate),
              lte: new Date(permissionEndDate)
            }
          },
          {
            endTime: {
              gte: new Date(permissionStartDate),
              lte: new Date(permissionEndDate)
            }
          },
          {
            AND: [
              { startTime: { lte: new Date(permissionStartDate) } },
              { endTime: { gte: new Date(permissionEndDate) } }
            ]
          }
        ];
      }
    }

    // Filtro por texto de búsqueda (en razón o nombre de empleado)
    if (searchText && searchText.trim() !== '') {
      whereClause.OR = [
        {
          reason: {
            contains: searchText.trim(),
            mode: 'insensitive'
          }
        },
        {
          user: {
            OR: [
              {
                firstName: {
                  contains: searchText.trim(),
                  mode: 'insensitive'
                }
              },
              {
                lastName: {
                  contains: searchText.trim(),
                  mode: 'insensitive'
                }
              }
            ]
          }
        }
      ];
    }

    const permissions = await prisma.permission.findMany({
      where: whereClause,
      include: {
        user: true,
        approver: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    console.log('✅ PERMISOS ENCONTRADOS:', permissions.length);

    // Convertir tipos de permisos a español
    const permissionsInSpanish = permissions.map(permission => ({
      ...permission,
      type: enumToSpanish(permission.type)
    }));

    res.json(permissionsInSpanish);
  } catch (error) {
    console.error('❌ ERROR AL OBTENER PERMISOS:', error);
    console.error('Error details:', error);
    res.status(500).json({
      error: 'Error al obtener los permisos',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Verificar disponibilidad de fechas
exports.checkAvailability = async (req, res) => {
  try {
    const { startTime, endTime, userId } = req.body;

    const overlappingPermissions = await prisma.permission.findMany({
      where: {
        userId,
        status: 'APPROVED',
        OR: [
          {
            AND: [
              { startTime: { lte: new Date(startTime) } },
              { endTime: { gte: new Date(startTime) } },
            ],
          },
          {
            AND: [
              { startTime: { lte: new Date(endTime) } },
              { endTime: { gte: new Date(endTime) } },
            ],
          },
        ],
      },
    });

    res.json({
      available: overlappingPermissions.length === 0,
      conflictingPermissions: overlappingPermissions,
    });
  } catch (error) {
    console.error('Error al verificar disponibilidad:', error);
    res.status(500).json({ error: 'Error al verificar la disponibilidad' });
  }
};

// Obtener permisos aprobados por rango de fechas
exports.getApprovedPermissionsByDate = async (req, res) => {
  try {
    console.log('Recibida solicitud de permisos aprobados por fecha:', req.query);
    const { startDate, endDate, userId } = req.query;

    // Validar fechas
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Se requieren fechas de inicio y fin' });
    }

    // Convertir a objetos Date y ajustar para incluir todo el día
    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);

    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999);

    console.log(`Buscando permisos entre ${start.toISOString()} y ${end.toISOString()}`);

    // CONTROL DE ACCESO POR ROL (igual que en taskController)
    const isAdminOrManager = req.user.role === 'ADMIN' || req.user.role === 'MANAGER';
    let targetUserId;
    let shouldShowAllUsers = false;

    if (isAdminOrManager) {
      // Admin/Manager puede ver todos o filtrar por usuario específico
      if (!userId || userId === 'ALL') {
        shouldShowAllUsers = true;
        console.log(`👨‍💼 ADMIN/MANAGER consultando permisos de TODOS los usuarios`);
      } else {
        targetUserId = userId;
        shouldShowAllUsers = false;
        console.log(`👨‍💼 ADMIN/MANAGER consultando permisos de usuario específico: ${targetUserId}`);
      }
    } else {
      // Usuario normal SOLO ve sus propios permisos
      targetUserId = req.user.id;
      shouldShowAllUsers = false;
      console.log(`👤 USUARIO NORMAL consultando solo sus propios permisos: ${targetUserId}`);
      console.log(`🔒 Acceso restringido: Usuario ${req.user.email} (${req.user.role}) no puede ver permisos de otros usuarios`);
    }

    // Construir la consulta WHERE
    const whereClause = {
      status: 'APPROVED', // SOLO permisos aprobados
      // Permisos que se superponen con el rango de fechas
      OR: [
        // Permisos que comienzan dentro del rango
        {
          startTime: {
            gte: start,
            lte: end
          }
        },
        // Permisos que terminan dentro del rango
        {
          endTime: {
            gte: start,
            lte: end
          }
        },
        // Permisos que abarcan todo el rango
        {
          startTime: {
            lte: start
          },
          endTime: {
            gte: end
          }
        }
      ]
    };

    // Solo agregar filtro de usuario si no queremos mostrar todos
    if (!shouldShowAllUsers) {
      whereClause.userId = targetUserId;
    }

    console.log(`🔍 CONSULTANDO permisos aprobados ${shouldShowAllUsers ? 'de TODOS los usuarios' : `para usuario ${targetUserId}`}`);

    // Consultar permisos aprobados que se superponen con el rango de fechas
    const approvedPermissions = await prisma.permission.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        startTime: 'asc'
      }
    });

    console.log(`🏖️ RESULTADO DE CONSULTA DE PERMISOS:`);
    console.log(`📊 Total permisos aprobados encontrados: ${approvedPermissions.length}`);

    if (approvedPermissions.length > 0) {
      console.log(`📋 DETALLES DE PERMISOS APROBADOS:`);
      approvedPermissions.forEach((permission, index) => {
        console.log(`${index + 1}. ID: ${permission.id}`);
        console.log(`   📝 Tipo: ${permission.type}`);
        console.log(`   📝 Razón: "${permission.reason || 'Sin razón especificada'}"`);
        console.log(`   ✅ Estado: ${permission.status}`);
        console.log(`   👤 Usuario: ${permission.user?.name || 'Sin usuario'} (ID: ${permission.userId})`);
        console.log(`   🕐 Inicio: ${permission.startTime}`);
        console.log(`   🕐 Fin: ${permission.endTime}`);
        console.log(`   ---`);
      });
    } else {
      console.log(`❌ NO se encontraron permisos aprobados con los criterios especificados`);
      console.log('🔍 Criterios aplicados:');
      console.log(`   - Rango de fechas: ${start.toISOString()} a ${end.toISOString()}`);
      console.log(`   - Estado requerido: APPROVED`);
      if (!shouldShowAllUsers) {
        console.log(`   - Usuario específico: ${targetUserId}`);
      }
    }

    // Formatear respuesta
    const formattedPermissions = approvedPermissions.map(permission => ({
      id: permission.id,
      type: permission.type,
      startTime: permission.startTime,
      endTime: permission.endTime,
      reason: permission.reason,
      status: permission.status,
      user: permission.user ? {
        id: permission.user.id,
        name: permission.user.name,
        email: permission.user.email
      } : null
    }));

    res.json(formattedPermissions);
  } catch (error) {
    console.error('Error al obtener permisos aprobados por fecha:', error);
    res.status(500).json({ error: 'Error al obtener permisos aprobados' });
  }
};

// Obtener horas extra disponibles para el usuario
exports.getOvertimeHours = async (req, res) => {
  try {
    const userId = req.user.id;

    console.log('🔍 CALCULANDO HORAS EXTRA PARA USUARIO:', userId);

    // Obtener todas las entradas de tiempo del usuario que tienen horas multiplicadas
    const overtimeEntries = await prisma.timeEntry.findMany({
      where: {
        userId: userId,
        status: 'APPROVED',
        multipliedHours: {
          not: null
        }
      },
      include: {
        multipliers: true
      }
    });

    console.log('📊 ENTRADAS DE HORAS EXTRA ENCONTRADAS:', overtimeEntries.length);

    // Calcular total de horas extra acumuladas
    const totalOvertimeHours = overtimeEntries.reduce((total, entry) => {
      // Las horas extra son la diferencia entre horas multiplicadas y horas trabajadas
      const regularHours = entry.hoursWorked || 0;
      const multipliedHours = entry.multipliedHours || 0;
      const overtimeHours = Math.max(0, multipliedHours - regularHours);
      return total + overtimeHours;
    }, 0);

    // Obtener permisos de compensación ya utilizados
    // Temporalmente comentado hasta resolver el problema con OVERTIME_COMPENSATION
    const usedCompensations = []; // await prisma.permission.findMany({
    //   where: {
    //     userId: userId,
    //     type: 'OVERTIME_COMPENSATION',
    //     status: {
    //       in: ['APPROVED', 'PENDING'] // Incluir pendientes para evitar doble uso
    //     }
    //   }
    // });

    console.log('📋 COMPENSACIONES UTILIZADAS:', usedCompensations.length);

    // Calcular horas ya utilizadas en compensaciones
    const usedOvertimeHours = usedCompensations.reduce((total, permission) => {
      return total + (permission.overtimeHoursUsed || 0);
    }, 0);

    // Calcular horas disponibles
    const availableOvertimeHours = Math.max(0, totalOvertimeHours - usedOvertimeHours);

    const result = {
      total: parseFloat(totalOvertimeHours.toFixed(2)),
      used: parseFloat(usedOvertimeHours.toFixed(2)),
      available: parseFloat(availableOvertimeHours.toFixed(2))
    };

    console.log('✅ RESULTADO HORAS EXTRA:', result);

    res.json(result);
  } catch (error) {
    console.error('❌ ERROR AL OBTENER HORAS EXTRA:', error);
    res.status(500).json({ error: 'Error al obtener horas extra disponibles' });
  }
};

// Validar permiso sin crearlo (para validación en tiempo real)
exports.validatePermission = async (req, res) => {
  try {
    const { type, startTime, endTime, reason, overtimeHoursUsed, overtimeHoursAvailable } = req.body;
    const userId = req.user.id;

    console.log('🔍 Validando permiso en tiempo real:', { type, startTime, endTime, userId });

    // Validar que los campos requeridos estén presentes
    if (!type) {
      return res.json({
        isValid: false,
        errors: ['Tipo de permiso es requerido'],
        warnings: [],
        details: {}
      });
    }

    if (!startTime || !endTime) {
      return res.json({
        isValid: false,
        errors: ['Fechas de inicio y fin son requeridas'],
        warnings: [],
        details: {}
      });
    }

    // Validar la solicitud de permiso
    const validationResult = await permissionValidationService.validatePermissionRequest(
      { type, startTime, endTime, reason, overtimeHoursUsed, overtimeHoursAvailable },
      userId
    );

    console.log('📋 Resultado de validación:', {
      isValid: validationResult.isValid,
      errorsCount: validationResult.errors.length,
      warningsCount: validationResult.warnings.length
    });

    res.json({
      isValid: validationResult.isValid,
      errors: validationResult.errors,
      warnings: validationResult.warnings,
      details: validationResult.details
    });
  } catch (error) {
    console.error('Error al validar permiso:', error);
    res.status(500).json({
      error: 'Error al validar el permiso',
      isValid: false,
      errors: ['Error interno del servidor'],
      warnings: []
    });
  }
};


