
const express = require('express');
const cors = require('cors');
const http = require('http');
const socketService = require('./services/socketService');

// Importar rutas
const authRoutes = require('./routes/authRoutes');
const timeEntryRoutes = require('./routes/timeEntryRoutes');
const permissionRoutes = require('./routes/permissionRoutes');
const reportRoutes = require('./routes/reportRoutes');
const settingsRoutes = require('./routes/settingsRoutes');
const taskRoutes = require('./routes/taskRoutes');
const kanbanRoutes = require('./routes/kanbanRoutes');
const dashboardRoutes = require('./routes/dashboardRoutes');
const holidayRoutes = require('./routes/holidayRoutes');
const backupRoutes = require('./routes/backupRoutes');
const ldapRoutes = require('./routes/ldapRoutes');
const activeTimerRoutes = require('./routes/activeTimerRoutes');
const globalTimerRoutes = require('./routes/globalTimer');
const userRoutes = require('./routes/userRoutes');
const dynamicFieldRoutes = require('./routes/dynamicFields');
const apiIntegrationRoutes = require('./routes/apiIntegrationRoutes');

const app = express();
const server = http.createServer(app);

// Middleware
app.use(cors({
  origin: [
    'http://************:3000',
    'http://localhost:3000',
    'https://kanban.sg.gestionados.pe'
  ],
  credentials: true
}));
app.use(express.json());

// Middleware de logging
app.use((req, res, next) => {
  console.log(`${req.method} ${req.path}`);
  console.log('Body:', req.body);
  console.log('Params:', req.params);
  next();
});

// Rutas
app.use('/api/auth', authRoutes);
app.use('/api/time-entries', timeEntryRoutes);
app.use('/api/permissions', permissionRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/kanban', kanbanRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/holidays', holidayRoutes);
app.use('/api/backups', backupRoutes);
app.use('/api/ldap', ldapRoutes);
app.use('/api/active-timer', activeTimerRoutes);
app.use('/api/global-timer', globalTimerRoutes);
app.use('/api/users', userRoutes);
app.use('/api/dynamic-fields', dynamicFieldRoutes);
app.use('/api/integrations', apiIntegrationRoutes);

// Configurar socket.io
socketService.initialize(server);

// Ruta de prueba
app.get('/', (req, res) => {
  res.json({ message: 'WorkTrack API funcionando correctamente' });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`Servidor corriendo en puerto ${PORT}`);
});
