const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Función para verificar si un TimeEntry está fuera del horario de oficina
async function isOutsideOfficeHours(timeEntry) {
  try {
    // Obtener horarios de trabajo configurados
    const workSchedules = await prisma.workSchedule.findMany();

    if (workSchedules.length === 0) {
      // Si no hay horarios configurados, considerar todo como fuera de horario
      console.log(`🕐 No hay horarios configurados - ${timeEntry.title}: FUERA DE HORARIO`);
      return true;
    }

    // APLICAR ZONA HORARIA DE LIMA, PERÚ (UTC-5) como el resto del sistema
    const entryStartUTC = new Date(timeEntry.startTime);
    const entryEndUTC = new Date(timeEntry.endTime);

    // Ajustar a zona horaria de Lima (UTC-5)
    const limaOffset = -5 * 60; // Lima es UTC-5
    const serverOffset = entryStartUTC.getTimezoneOffset();
    const offsetDiff = limaOffset - serverOffset;

    // Crear fechas ajustadas a hora de Lima
    const entryStartLima = new Date(entryStartUTC.getTime() + offsetDiff * 60000);
    const entryEndLima = new Date(entryEndUTC.getTime() + offsetDiff * 60000);

    const dayOfWeek = entryStartLima.getDay(); // 0=Domingo, 1=Lunes, etc.

    // Buscar horario para el día de la semana del TimeEntry
    const daySchedule = workSchedules.find(schedule => schedule.dayOfWeek === dayOfWeek);

    if (!daySchedule) {
      // Si no hay horario para este día, está fuera del horario de oficina
      console.log(`🕐 Día no laboral - ${timeEntry.title} (${getDayName(dayOfWeek)}): FUERA DE HORARIO`);
      return true;
    }

    // Convertir horarios de oficina a minutos
    const [startHour, startMin] = daySchedule.startTime.split(':').map(Number);
    const [endHour, endMin] = daySchedule.endTime.split(':').map(Number);
    const officeStartMinutes = startHour * 60 + startMin;
    const officeEndMinutes = endHour * 60 + endMin;

    // Convertir horarios del TimeEntry (en hora de Lima) a minutos
    const entryStartMinutes = entryStartLima.getHours() * 60 + entryStartLima.getMinutes();
    const entryEndMinutes = entryEndLima.getHours() * 60 + entryEndLima.getMinutes();

    // Verificar si está fuera del horario de oficina
    // Ahora usando los valores de la configuración
    const isOutside = entryStartMinutes < officeStartMinutes || 
                      entryStartMinutes >= officeEndMinutes || 
                      entryEndMinutes <= officeStartMinutes || 
                      entryEndMinutes > officeEndMinutes;

    console.log(`🕐 Verificando horario - ${timeEntry.title}:`);
    console.log(`  📅 Día: ${dayOfWeek} (${getDayName(dayOfWeek)})`);
    console.log(`  🏢 Horario oficina: ${daySchedule.startTime} - ${daySchedule.endTime}`);
    console.log(`  ⏰ TimeEntry UTC: ${entryStartUTC.toISOString()} - ${entryEndUTC.toISOString()}`);
    console.log(`  🇵🇪 TimeEntry Lima: ${entryStartLima.toLocaleString()} - ${entryEndLima.toLocaleString()}`);
    console.log(`  ⏰ Minutos Lima: ${entryStartMinutes} - ${entryEndMinutes}`);
    console.log(`  🏢 Minutos oficina: ${officeStartMinutes} - ${officeEndMinutes}`);
    console.log(`  🚫 ¿Fuera de horario?: ${isOutside}`);

    return isOutside;
  } catch (error) {
    console.error('Error al verificar horario de oficina:', error);
    // En caso de error, asumir que está dentro del horario
    return false;
  }
}

// Función auxiliar para obtener nombre del día
function getDayName(dayOfWeek) {
  const days = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
  return days[dayOfWeek] || 'Desconocido';
}

// Funciones existentes
// ...

// Obtener tareas completadas por rango de fechas
exports.getCompletedTasksByDate = async (req, res) => {
  try {
    console.log('Recibida solicitud de tareas completadas por fecha:', req.query);
    const { startDate, endDate, userId, outsideOfficeHoursOnly } = req.query;

    // Validar fechas
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Se requieren fechas de inicio y fin' });
    }

    // SIMPLIFICADO: Trabajar directamente en hora peruana sin conversiones
    console.log(`🇵🇪 Trabajando directamente en hora peruana para fechas del calendario`);

    // Las fechas se manejan directamente en hora peruana
    const start = new Date(startDate + 'T00:00:00.000Z');
    const end = new Date(endDate + 'T23:59:59.999Z');

    console.log(`📅 Fechas originales: ${startDate} a ${endDate}`);
    console.log(`🇵🇪 Fechas procesadas: ${start.toISOString()} a ${end.toISOString()}`);

    // CONTROL DE ACCESO POR ROL
    const isAdminOrManager = req.user.role === 'ADMIN' || req.user.role === 'MANAGER';
    let targetUserId;
    let shouldShowAllUsers = false;

    if (isAdminOrManager) {
      // Admin/Manager puede ver todos o filtrar por usuario específico
      if (!userId || userId === 'ALL') {
        shouldShowAllUsers = true;
        console.log(`👨‍💼 ADMIN/MANAGER consultando TODOS los usuarios`);
      } else {
        targetUserId = userId;
        shouldShowAllUsers = false;
        console.log(`👨‍💼 ADMIN/MANAGER consultando usuario específico: ${targetUserId}`);
      }
    } else {
      // Usuario normal SOLO ve sus propias actividades
      targetUserId = req.user.id;
      shouldShowAllUsers = false;
      console.log(`👤 USUARIO NORMAL consultando solo sus propias actividades: ${targetUserId}`);
      console.log(`🔒 Acceso restringido: Usuario ${req.user.email} (${req.user.role}) no puede ver otros usuarios`);
    }

    // Verificar si hay tareas en general para este usuario
    const allUserTasks = await prisma.task.findMany({
      where: { assigneeId: targetUserId },
      select: { id: true, title: true, status: true, updatedAt: true },
      orderBy: { updatedAt: 'desc' },
      take: 5
    });

    console.log(`Tareas totales del usuario: ${allUserTasks.length}`);
    if (allUserTasks.length > 0) {
      console.log('Últimas 5 tareas:');
      allUserTasks.forEach(task => {
        console.log(`- ${task.title} (${task.status}): ${task.updatedAt}`);
      });
    }

    // CONSULTAR DIRECTAMENTE TimeEntry aprobados (sin depender de Task)
    console.log(`🔍 CONSULTANDO DIRECTAMENTE TimeEntry aprobados ${shouldShowAllUsers ? 'de TODOS los usuarios' : `para usuario ${targetUserId}`}`);
    console.log(`📅 Rango de consulta (zona Lima): ${start.toISOString()} a ${end.toISOString()}`);

    const whereClause = {
      startTime: {
        gte: start,
        lte: end
      },
      // Solo registros manuales aprobados
      status: 'APPROVED',
      NOT: {
        title: {
          startsWith: 'Tiempo trabajado en:'
        }
      }
    };

    // Solo agregar filtro de usuario si no queremos mostrar todos
    if (!shouldShowAllUsers) {
      whereClause.userId = targetUserId;
    }

    const timeEntriesInRange = await prisma.timeEntry.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        task: {
          select: {
            id: true,
            title: true,
            description: true,
            priority: true
          }
        }
      },
      orderBy: {
        startTime: 'asc'
      }
    });

    console.log(`🔍 RESULTADO DE CONSULTA DIRECTA A TimeEntry:`);
    console.log(`📊 Total TimeEntries encontradas: ${timeEntriesInRange.length}`);

    // FILTRAR SOLO TimeEntries FUERA DEL HORARIO DE OFICINA (si se solicita)
    let finalTimeEntries = timeEntriesInRange;

    if (outsideOfficeHoursOnly === 'true') {
      console.log(`🕐 Aplicando filtro de horario de oficina...`);
      const outsideOfficeHoursEntries = [];

      for (const entry of timeEntriesInRange) {
        const isOutside = await isOutsideOfficeHours(entry);
        if (isOutside) {
          outsideOfficeHoursEntries.push(entry);
        }
      }

      finalTimeEntries = outsideOfficeHoursEntries;
      console.log(`🚫 TimeEntries FUERA del horario de oficina: ${finalTimeEntries.length}`);
    } else {
      console.log(`📊 Mostrando TODOS los TimeEntries aprobados (sin filtro de horario)`);
    }

    if (finalTimeEntries.length > 0) {
      console.log(`📋 DETALLES DE TimeEntries ${outsideOfficeHoursOnly === 'true' ? 'FUERA DE HORARIO' : 'PROCESADAS'}:`);
      finalTimeEntries.forEach((entry, index) => {
        console.log(`${index + 1}. ID: ${entry.id}`);
        console.log(`   📝 Título: "${entry.title}"`);
        console.log(`   ✅ Estado: ${entry.status}`);
        console.log(`   👤 Usuario: ${entry.user?.name || 'Sin usuario'} (ID: ${entry.userId})`);
        console.log(`   🕐 Inicio: ${entry.startTime}`);
        console.log(`   🕐 Fin: ${entry.endTime}`);
        console.log(`   ⏱️ Horas: ${entry.hoursWorked}`);
        console.log(`   🔗 Task asociada: ${entry.task?.title || 'Sin tarea asociada'}`);
        console.log(`   ---`);
      });
    } else {
      console.log(`❌ NO se encontraron TimeEntries ${outsideOfficeHoursOnly === 'true' ? 'FUERA del horario de oficina' : 'con los criterios especificados'}`);
      console.log('🔍 Criterios aplicados:');
      console.log(`   - Rango de fechas: ${start.toISOString()} a ${end.toISOString()}`);
      console.log(`   - Estado requerido: APPROVED`);
      console.log(`   - Excluir títulos que empiecen con: "Tiempo trabajado en:"`);
      if (outsideOfficeHoursOnly === 'true') {
        console.log(`   - Solo FUERA del horario de oficina configurado`);
      }
      if (!shouldShowAllUsers) {
        console.log(`   - Usuario específico: ${targetUserId}`);
      }
    }

    // Obtener configuraciones de multiplicadores desde settings
    console.log(`⚙️ OBTENIENDO CONFIGURACIONES DE MULTIPLICADORES...`);

    // Obtener multiplicadores de tiempo (horarios específicos)
    const timeMultipliers = await prisma.timeMultiplier.findMany({
      where: { isActive: true }
    });

    // Obtener horarios de trabajo (para determinar horario de oficina)
    const workSchedules = await prisma.workSchedule.findMany();

    // Obtener feriados
    const holidays = await prisma.holiday.findMany();

    console.log(`📊 CONFIGURACIONES CARGADAS:`);
    console.log(`  🕐 Multiplicadores de tiempo: ${timeMultipliers.length}`);
    console.log(`  📅 Horarios de trabajo: ${workSchedules.length}`);
    console.log(`  🎉 Feriados: ${holidays.length}`);

    // Log detallado de multiplicadores
    timeMultipliers.forEach(mult => {
      console.log(`  📋 ${mult.name}: ${mult.value}x (${mult.startTime || 'N/A'} - ${mult.endTime || 'N/A'})`);
    });

    // Función para calcular multiplicador aplicable
    const calculateMultiplier = (entryStartTime, entryEndTime) => {
      // IMPORTANTE: Convertir UTC a hora peruana (UTC-5) ANTES de cualquier cálculo
      const entryDateUTC = new Date(entryStartTime);
      const entryEndUTC = new Date(entryEndTime);

      // Convertir a hora peruana (UTC-5)
      const entryStartLima = new Date(entryDateUTC.getTime() - 5 * 60 * 60 * 1000);
      const entryEndLima = new Date(entryEndUTC.getTime() - 5 * 60 * 60 * 1000);

      const dayOfWeek = entryStartLima.getDay(); // 0 = Domingo, 1 = Lunes, etc.

      const startHour = entryStartLima.getHours();
      const startMinute = entryStartLima.getMinutes();
      const endHour = entryEndLima.getHours();
      const endMinute = entryEndLima.getMinutes();

      let applicableMultiplier = 1.0;
      let multiplierReasons = [];

      // 1. Verificar si es feriado
      const entryDateStr = entryStartLima.toISOString().split('T')[0];
      const holiday = holidays.find(h => {
        const holidayDateStr = new Date(h.date).toISOString().split('T')[0];
        return holidayDateStr === entryDateStr;
      });

      if (holiday) {
        applicableMultiplier = Math.max(applicableMultiplier, holiday.multiplier);
        multiplierReasons.push(`Feriado "${holiday.name}": ${holiday.multiplier}x`);
      }

      // 2. Verificar horario de trabajo para el día de la semana
      const workSchedule = workSchedules.find(ws => ws.dayOfWeek === dayOfWeek);
      if (workSchedule) {
        // Si es fin de semana o tiene multiplicador especial
        if (workSchedule.multiplier > 1.0) {
          applicableMultiplier = Math.max(applicableMultiplier, workSchedule.multiplier);
          const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
          multiplierReasons.push(`${dayNames[dayOfWeek]}: ${workSchedule.multiplier}x`);
        }

        // Verificar si está fuera del horario de oficina
        const officeStart = workSchedule.startTime.split(':');
        const officeEnd = workSchedule.endTime.split(':');
        const officeStartMinutes = parseInt(officeStart[0]) * 60 + parseInt(officeStart[1]);
        const officeEndMinutes = parseInt(officeEnd[0]) * 60 + parseInt(officeEnd[1]);
        const entryStartMinutes = startHour * 60 + startMinute;
        const entryEndMinutes = endHour * 60 + endMinute;

        const isOutsideOfficeHours = entryStartMinutes < officeStartMinutes || entryEndMinutes > officeEndMinutes;

        if (isOutsideOfficeHours) {
          multiplierReasons.push(`Fuera de horario de oficina (${workSchedule.startTime}-${workSchedule.endTime})`);
        }

        // 🔍 DEBUG: Log para verificar la corrección de timezone en calculateMultiplier
        console.log(`🕐 MULTIPLICADOR: isOutsideOfficeHours = ${isOutsideOfficeHours} (Perú: ${startHour}:${startMinute.toString().padStart(2, '0')}) - CÓDIGO CORREGIDO APLICADO ✅`);
      }

      // 3. Verificar multiplicadores de tiempo específicos (ej: nocturno, madrugada)
      timeMultipliers.forEach(mult => {
        if (mult.startTime && mult.endTime) {
          const multStart = mult.startTime.split(':');
          const multEnd = mult.endTime.split(':');
          const multStartMinutes = parseInt(multStart[0]) * 60 + parseInt(multStart[1]);
          const multEndMinutes = parseInt(multEnd[0]) * 60 + parseInt(multEnd[1]);

          // Manejar horarios que cruzan medianoche (ej: 22:00 - 06:00)
          let isInTimeRange = false;
          if (multStartMinutes > multEndMinutes) {
            // Cruza medianoche
            isInTimeRange = (startHour * 60 + startMinute >= multStartMinutes) ||
                           (startHour * 60 + startMinute <= multEndMinutes);
          } else {
            // No cruza medianoche
            isInTimeRange = (startHour * 60 + startMinute >= multStartMinutes) &&
                           (startHour * 60 + startMinute <= multEndMinutes);
          }

          if (isInTimeRange) {
            applicableMultiplier = Math.max(applicableMultiplier, mult.value);
            multiplierReasons.push(`${mult.name} (${mult.startTime}-${mult.endTime}): ${mult.value}x`);
          }
        } else {
          // Multiplicador sin horario específico (ej: solo feriados)
          if (holiday && mult.name.toLowerCase().includes('feriado')) {
            applicableMultiplier = Math.max(applicableMultiplier, mult.value);
            multiplierReasons.push(`${mult.name}: ${mult.value}x`);
          }
        }
      });

      return { multiplier: applicableMultiplier, reasons: multiplierReasons };
    };

    // PASO ADICIONAL: Obtener TimeEntry directos (sin tarea asociada) para mostrar en calendario
    // TEMPORALMENTE DESHABILITADO para evitar errores
    const directTimeEntries = [];
    console.log(`📝 TimeEntries directos encontrados: ${directTimeEntries.length} (temporalmente deshabilitado)`);

    // Crear tareas virtuales desde TimeEntry filtradas
    const virtualTasks = finalTimeEntries.map(entry => {
      const logMessage = outsideOfficeHoursOnly === 'true'
        ? `📝 Procesando TimeEntry FUERA DE HORARIO: ${entry.title}`
        : `📝 Procesando TimeEntry: ${entry.title}`;
      console.log(`${logMessage} - Usuario: ${entry.user?.name} - Estado: ${entry.status}`);

      // CALCULAR CAMPOS DINÁMICAMENTE PARA CADA ENTRADA
      const { multiplier, reasons } = calculateMultiplier(entry.startTime, entry.endTime);
      const weightedHours = entry.hoursWorked * multiplier;

      // Determinar si está fuera de horario de oficina
      // IMPORTANTE: Convertir UTC a hora peruana (UTC-5)
      const entryDateUTC = new Date(entry.startTime);
      const entryEndUTC = new Date(entry.endTime);

      // Convertir a hora peruana (UTC-5)
      const entryDatePeru = new Date(entryDateUTC.getTime() - 5 * 60 * 60 * 1000);
      const entryEndPeru = new Date(entryEndUTC.getTime() - 5 * 60 * 60 * 1000);

      const dayOfWeek = entryDatePeru.getDay();
      const workSchedule = workSchedules.find(ws => ws.dayOfWeek === dayOfWeek);

      let isOutsideOfficeHours = false;
      if (workSchedule) {
        const officeStart = workSchedule.startTime.split(':');
        const officeEnd = workSchedule.endTime.split(':');
        const officeStartMinutes = parseInt(officeStart[0]) * 60 + parseInt(officeStart[1]);
        const officeEndMinutes = parseInt(officeEnd[0]) * 60 + parseInt(officeEnd[1]);
        const entryStartMinutes = entryDatePeru.getHours() * 60 + entryDatePeru.getMinutes();
        const entryEndMinutes = entryEndPeru.getHours() * 60 + entryEndPeru.getMinutes();

        isOutsideOfficeHours = entryStartMinutes < officeStartMinutes || entryEndMinutes > officeEndMinutes;
      } else {
        // Si no hay horario configurado para este día, está fuera de horario
        isOutsideOfficeHours = true;
      }

      console.log(`🕐 ${entry.title}: isOutsideOfficeHours = ${isOutsideOfficeHours} (Perú: ${entryDatePeru.getHours()}:${entryDatePeru.getMinutes().toString().padStart(2, '0')}) - CÓDIGO CORREGIDO APLICADO ✅`);

      return {
        id: `timeentry-${entry.id}`,
        title: entry.task?.title || entry.title,  // Usar título de tarea o del entry
        description: entry.task?.description || entry.description,
        status: 'MANUAL_ENTRY',
        priority: entry.task?.priority || 'MEDIUM',
        estimatedHours: entry.hoursWorked,
        actualHours: entry.hoursWorked,
        createdAt: entry.createdAt,
        updatedAt: entry.updatedAt,
        assignee: entry.user || {
          id: entry.userId,
          name: 'Usuario Desconocido',
          email: ''
        },
        timeEntries: [{
          id: entry.id,
          title: entry.title,
          startTime: entry.startTime,
          endTime: entry.endTime,
          hoursWorked: entry.hoursWorked,
          type: entry.type,
          status: entry.status,
          // AGREGAR CAMPOS CALCULADOS DINÁMICAMENTE
          isOutsideOfficeHours: isOutsideOfficeHours,
          hourMultiplier: multiplier,
          weightedHours: weightedHours,
          multiplierReasons: reasons
        }],
        isManualEntry: true,
        registeredBy: entry.user,
        // AGREGAR CAMPOS CALCULADOS A NIVEL DE TAREA TAMBIÉN
        isOutsideOfficeHours: isOutsideOfficeHours,
        hourMultiplier: multiplier,
        weightedHours: weightedHours
      };
    });

    console.log(`Total de registros manuales a procesar: ${virtualTasks.length}`);

    const formattedTasks = virtualTasks.map(task => {
      // Calcular horas reales desde timeEntries
      const totalHours = task.timeEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);

      // Encontrar el primer y último registro de tiempo para determinar el período de trabajo
      const sortedEntries = task.timeEntries
        .filter(entry => entry.startTime && entry.endTime)
        .sort((a, b) => new Date(a.startTime) - new Date(b.startTime));

      let workStartTime = null;
      let workEndTime = null;

      if (sortedEntries.length > 0) {
        workStartTime = sortedEntries[0].startTime;
        workEndTime = sortedEntries[sortedEntries.length - 1].endTime;
      }

      return {
        id: task.id,
        title: task.title,
        description: task.description,
        status: task.status,
        priority: task.priority,
        estimatedHours: task.estimatedHours,
        actualHours: totalHours || task.actualHours || 0,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
        // Información de tiempo real basada en timeEntries
        workStartTime: workStartTime,
        workEndTime: workEndTime,
        // PRESERVAR CAMPOS CALCULADOS DE LA TAREA ORIGINAL
        isOutsideOfficeHours: task.isOutsideOfficeHours,
        hourMultiplier: task.hourMultiplier,
        weightedHours: task.weightedHours,
        isManualEntry: task.isManualEntry,
        registeredBy: task.registeredBy,
        assignee: task.assignee,
        timeEntries: task.timeEntries.map(entry => {
          // Calcular multiplicador aplicable usando la función completa
          const multiplierResult = calculateMultiplier(entry.startTime, entry.endTime);
          const applicableMultiplier = multiplierResult.multiplier;
          const multiplierReasons = multiplierResult.reasons;

          // Calcular horas ponderadas
          const weightedHours = entry.hoursWorked * applicableMultiplier;
          const isOutsideOfficeHours = applicableMultiplier > 1.0;

          if (isOutsideOfficeHours) {
            console.log(`💰 REGISTRO CON MULTIPLICADOR DETECTADO:`);
            console.log(`   📝 Título: ${entry.title}`);
            console.log(`   🕐 Horario: ${entry.startTime} - ${entry.endTime}`);
            console.log(`   ⏰ Horas normales: ${entry.hoursWorked}h`);
            console.log(`   💰 Multiplicador aplicable: ${applicableMultiplier}x`);
            console.log(`   🎯 Horas ponderadas: ${weightedHours}h`);
            console.log(`   📋 Razones del multiplicador:`);
            multiplierReasons.forEach(reason => {
              console.log(`     - ${reason}`);
            });
          }

          return {
            id: entry.id,
            title: entry.title,
            startTime: entry.startTime,
            endTime: entry.endTime,
            hoursWorked: entry.hoursWorked,
            type: entry.type,
            status: entry.status,
            isOutsideOfficeHours: isOutsideOfficeHours,
            weightedHours: weightedHours,
            hourMultiplier: applicableMultiplier,
            multiplierReasons: multiplierReasons
          };
        }),
        assignee: task.assignee ? {
          id: task.assignee.id,
          name: task.assignee.name,
          email: task.assignee.email
        } : null,
        // Propiedades importantes para el frontend
        isManualEntry: task.isManualEntry,
        registeredBy: task.registeredBy
      };
    });

    // Crear tareas virtuales desde TimeEntry directos (sin tarea asociada)
    const virtualTasksFromDirectEntries = directTimeEntries.map(entry => {
      console.log(`📝 Creando tarea virtual para TimeEntry directo: ${entry.title || 'Sin título'}`);

      // Calcular multiplicador para el TimeEntry directo
      const multiplierResult = calculateMultiplier(entry.startTime, entry.endTime);
      const applicableMultiplier = multiplierResult.multiplier;
      const multiplierReasons = multiplierResult.reasons;
      const weightedHours = entry.hoursWorked * applicableMultiplier;
      const isOutsideOfficeHours = applicableMultiplier > 1.0;

      return {
        id: `direct-entry-${entry.id}`,
        title: entry.title || 'Registro de tiempo directo',
        description: entry.description || '',
        status: 'COMPLETED',
        priority: 'MEDIUM',
        estimatedHours: entry.hoursWorked,
        actualHours: entry.hoursWorked,
        isManualEntry: true,
        assignee: entry.user ? {
          id: entry.user.id,
          name: entry.user.name,
          email: entry.user.email
        } : null,
        timeEntries: [{
          id: entry.id,
          title: entry.title,
          startTime: entry.startTime.toISOString(),
          endTime: entry.endTime.toISOString(),
          hoursWorked: entry.hoursWorked,
          description: entry.description,
          status: entry.status,
          type: entry.type,
          isOutsideOfficeHours: isOutsideOfficeHours,
          weightedHours: weightedHours,
          hourMultiplier: applicableMultiplier,
          multiplierReasons: multiplierReasons
        }],
        updatedAt: entry.updatedAt.toISOString(),
        createdAt: entry.createdAt.toISOString(),
        registeredBy: entry.user
      };
    });

    console.log(`📝 Tareas virtuales creadas desde TimeEntry directos: ${virtualTasksFromDirectEntries.length}`);

    // Combinar todas las tareas virtuales
    const allFormattedTasks = [...formattedTasks, ...virtualTasksFromDirectEntries];

    console.log(`🚀 ENVIANDO AL FRONTEND: ${allFormattedTasks.length} tareas virtuales (${formattedTasks.length} de tareas + ${virtualTasksFromDirectEntries.length} de TimeEntry directos)`);
    if (allFormattedTasks.length > 0) {
      console.log('📤 Primeras tareas que se envían:');
      allFormattedTasks.slice(0, 2).forEach((task, index) => {
        console.log(`${index + 1}. ID: ${task.id}, Título: "${task.title}", isManualEntry: ${task.isManualEntry}`);
      });
    }

    res.json(allFormattedTasks);
  } catch (error) {
    console.error('Error al obtener tareas completadas por fecha:', error);
    res.status(500).json({ error: 'Error al obtener tareas completadas' });
  }
};

// Nuevo endpoint para obtener tareas kanban archivadas con tiempos
exports.getArchivedKanbanTasks = async (req, res) => {
  try {
    const { startDate, endDate, userId, outsideOfficeHoursOnly } = req.query;

    console.log(`🎯 CONSULTANDO TAREAS KANBAN ARCHIVADAS:`);
    console.log(`📅 Rango: ${startDate} a ${endDate}`);
    console.log(`👤 Usuario solicitante: ${req.user.email} (${req.user.role})`);

    // OBTENER CONFIGURACIÓN DE HORARIOS DE TRABAJO
    const workSchedules = await prisma.workSchedule.findMany();
    console.log(`⚙️ Horarios de trabajo configurados: ${workSchedules.length}`);

    // Si no hay horarios configurados, usar valores por defecto
    let defaultSchedule = {
      startTime: '09:00',
      endTime: '18:00',
      lunchBreak: '13:00',
      lunchDuration: 60,
      workDays: [1, 2, 3, 4, 5], // Lunes a Viernes
    };

    if (workSchedules.length > 0) {
      // Usar el primer horario como referencia para el horario estándar
      const firstSchedule = workSchedules[0];
      defaultSchedule = {
        startTime: firstSchedule.startTime,
        endTime: firstSchedule.endTime,
        lunchBreak: firstSchedule.lunchBreak,
        lunchDuration: firstSchedule.lunchDuration,
        workDays: workSchedules.map(ws => ws.dayOfWeek)
      };
      console.log(`⚙️ Usando configuración: ${defaultSchedule.startTime} - ${defaultSchedule.endTime}`);
    } else {
      console.log(`⚠️ No hay horarios configurados, usando valores por defecto: ${defaultSchedule.startTime} - ${defaultSchedule.endTime}`);
    }

    // SIMPLIFICADO: Trabajar directamente en hora peruana sin conversiones
    console.log(`🇵🇪 Trabajando directamente en hora peruana para fechas del calendario`);

    // Las fechas se manejan directamente en hora peruana (agregar Z para formato ISO)
    const start = new Date(startDate + 'T00:00:00.000Z');
    const end = new Date(endDate + 'T23:59:59.999Z');

    console.log(`📅 Fechas originales: ${startDate} a ${endDate}`);
    console.log(`🇵🇪 Fechas procesadas: ${start.toISOString()} a ${end.toISOString()}`);

    // CONTROL DE ACCESO POR ROL
    const isAdminOrManager = req.user.role === 'ADMIN' || req.user.role === 'MANAGER';
    let targetUserId;
    let shouldShowAllUsers = false;

    if (isAdminOrManager) {
      if (!userId || userId === 'ALL') {
        shouldShowAllUsers = true;
        console.log(`👨‍💼 ADMIN/MANAGER consultando tareas kanban de TODOS los usuarios`);
      } else {
        targetUserId = userId;
        shouldShowAllUsers = false;
        console.log(`👨‍💼 ADMIN/MANAGER consultando tareas kanban de usuario específico: ${targetUserId}`);
      }
    } else {
      targetUserId = req.user.id;
      shouldShowAllUsers = false;
      console.log(`👤 USUARIO NORMAL consultando solo sus propias tareas kanban: ${targetUserId}`);
    }

    // Construir la consulta WHERE para active_timer
    const whereClause = {
      startTime: {
        gte: start,
        lte: end
      },
      task: {
        status: 'ARCHIVED' // Solo tareas archivadas
      }
    };

    // Solo agregar filtro de usuario si no queremos mostrar todos
    if (!shouldShowAllUsers) {
      whereClause.userId = targetUserId;
    }

    console.log(`🔍 CONSULTANDO active_timer con tareas archivadas ${shouldShowAllUsers ? 'de TODOS los usuarios' : `para usuario ${targetUserId}`}`);

    // Consultar active_timer con tareas archivadas (sistema legacy)
    const archivedTimers = await prisma.activeTimer.findMany({
      where: whereClause,
      include: {
        task: {
          include: {
            assignee: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        startTime: 'asc'
      }
    });

    // NUEVO: Consultar tareas con GlobalTimer (sin ActiveTimer)
    const globalTimerWhereClause = {
      status: 'ARCHIVED',
      timerAccumulatedSeconds: { gt: 0 },
      OR: [
        // Tareas con timerStartTime válido en el rango
        {
          timerStartTime: {
            gte: start,
            lte: end
          }
        },
        // Tareas sin timerStartTime pero con updatedAt en el rango (fallback)
        {
          timerStartTime: null,
          updatedAt: {
            gte: start,
            lte: end
          }
        }
      ],
      // Excluir tareas que ya tienen ActiveTimer
      activeTimers: { none: {} }
    };

    // Agregar filtro de usuario para GlobalTimer si es necesario
    if (!shouldShowAllUsers) {
      globalTimerWhereClause.assigneeId = targetUserId;
    }

    console.log(`🔍 CONSULTANDO GlobalTimer tareas archivadas ${shouldShowAllUsers ? 'de TODOS los usuarios' : `para usuario ${targetUserId}`}`);

    const globalTimerTasks = await prisma.task.findMany({
      where: globalTimerWhereClause,
      include: {
        assignee: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        timerStartTime: 'asc'
      }
    });

    console.log(`🎯 RESULTADO DE CONSULTA DE TAREAS KANBAN:`);
    console.log(`📊 ActiveTimer: ${archivedTimers.length} tareas encontradas`);
    console.log(`📊 GlobalTimer: ${globalTimerTasks.length} tareas encontradas`);
    console.log(`📊 Total: ${archivedTimers.length + globalTimerTasks.length} tareas`);

    if (archivedTimers.length > 0) {
      console.log(`📋 DETALLES DE TAREAS KANBAN ARCHIVADAS (ActiveTimer):`);
      archivedTimers.forEach((timer, index) => {
        const actualHours = timer.task?.actualHours || 0;
        const elapsedSeconds = timer.elapsedTime || 0;
        const elapsedHours = (elapsedSeconds / 3600).toFixed(2);

        console.log(`${index + 1}. Timer ID: ${timer.id}`);
        console.log(`   📝 Tarea: "${timer.task?.title || 'Sin título'}"`);
        console.log(`   ✅ Estado tarea: ${timer.task?.status}`);
        console.log(`   👤 Usuario: ${timer.user?.name || 'Sin usuario'} (ID: ${timer.userId})`);
        console.log(`   👤 Asignado a: ${timer.task?.assignee?.name || 'Sin asignar'}`);
        console.log(`   🕐 Inicio: ${timer.startTime}`);
        console.log(`   ⏱️ Tiempo del timer: ${elapsedSeconds}s (${elapsedHours}h)`);
        console.log(`   🎯 Horas reales de la tarea: ${actualHours}h`);
        console.log(`   📊 Usando actualHours para cálculo de duración`);
        console.log(`   ---`);
      });
    }

    if (globalTimerTasks.length > 0) {
      console.log(`📋 DETALLES DE TAREAS KANBAN ARCHIVADAS (GlobalTimer):`);
      globalTimerTasks.forEach((task, index) => {
        const actualHours = task.actualHours || 0;
        const globalSeconds = task.timerAccumulatedSeconds || 0;
        const globalHours = (globalSeconds / 3600).toFixed(2);

        console.log(`${index + 1}. Task ID: ${task.id}`);
        console.log(`   📝 Tarea: "${task.title || 'Sin título'}"`);
        console.log(`   ✅ Estado tarea: ${task.status}`);
        console.log(`   👤 Asignado a: ${task.assignee?.name || 'Sin asignar'}`);
        console.log(`   🕐 Inicio: ${task.timerStartTime}`);
        console.log(`   ⏱️ Tiempo del GlobalTimer: ${globalSeconds}s (${globalHours}h)`);
        console.log(`   🎯 Horas reales de la tarea: ${actualHours}h`);
        console.log(`   📊 Usando actualHours para cálculo de duración`);
        console.log(`   ---`);
      });
    }

    if (archivedTimers.length === 0 && globalTimerTasks.length === 0) {
      console.log(`❌ NO se encontraron tareas kanban archivadas con los criterios especificados`);
    }

    // Formatear respuesta combinando ActiveTimer y GlobalTimer
    // 1. Formatear tareas de ActiveTimer (sistema legacy)
    const formattedActiveTimerTasks = archivedTimers.map(timer => {
      const actualHours = timer.task?.actualHours || 0;
      const elapsedSeconds = timer.elapsedTime || 0;

      // Calcular tiempo de fin basado en las horas reales de la tarea
      const startTime = new Date(timer.startTime);
      const durationMilliseconds = actualHours * 60 * 60 * 1000;
      const endTime = new Date(startTime.getTime() + durationMilliseconds);

      console.log(`📊 CÁLCULO DE DURACIÓN PARA TAREA "${timer.task?.title}" (ActiveTimer)`);
      console.log(`   🕐 Inicio: ${startTime.toISOString()}`);
      console.log(`   🎯 Horas reales: ${actualHours}h`);
      console.log(`   ⏱️ Duración en ms: ${durationMilliseconds}ms`);
      console.log(`   🕐 Fin calculado: ${endTime.toISOString()}`);

      return {
        id: timer.task?.id || timer.id,
        title: timer.task?.title || 'Tarea sin título',
        status: timer.task?.status,
        assignee: timer.task?.assignee,
        user: timer.user,
        startTime: timer.startTime,
        endTime: endTime.toISOString(),
        actualHours: actualHours,
        elapsedTime: elapsedSeconds,
        type: 'kanban_task',
        source: 'ActiveTimer'
      };
    });

    // 2. Formatear tareas de GlobalTimer (sistema nuevo)
    const formattedGlobalTimerTasks = globalTimerTasks.map(task => {
      const actualHours = task.actualHours || 0;

      // FALLBACK para startTime si es NULL
      let startTime;
      if (task.timerStartTime) {
        startTime = new Date(task.timerStartTime);
      } else {
        // Usar updatedAt como fallback si no hay timerStartTime
        startTime = new Date(task.updatedAt);
        console.log(`⚠️ FALLBACK: Usando updatedAt como startTime para "${task.title}"`);
      }

      const durationMilliseconds = actualHours * 60 * 60 * 1000;
      const endTime = new Date(startTime.getTime() + durationMilliseconds);

      console.log(`📊 CÁLCULO DE DURACIÓN PARA TAREA "${task.title}" (GlobalTimer)`);
      console.log(`   🕐 Inicio: ${startTime.toISOString()} ${task.timerStartTime ? '(timerStartTime)' : '(fallback: updatedAt)'}`);
      console.log(`   🎯 Horas reales: ${actualHours}h`);
      console.log(`   ⏱️ Duración en ms: ${durationMilliseconds}ms`);
      console.log(`   🕐 Fin calculado: ${endTime.toISOString()}`);

      return {
        id: task.id,
        title: task.title || 'Tarea sin título',
        status: task.status,
        assignee: task.assignee,
        user: task.assignee, // En GlobalTimer, el assignee es quien trabajó
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        actualHours: actualHours,
        elapsedTime: task.timerAccumulatedSeconds,
        type: 'kanban_task',
        source: 'GlobalTimer'
      };
    });

    // 3. Combinar ambos sistemas
    const formattedTasks = [...formattedActiveTimerTasks, ...formattedGlobalTimerTasks];

    // Aplicar filtro de horario de oficina si está habilitado
    if (outsideOfficeHoursOnly === 'true') {
      console.log(`🕐 APLICANDO FILTRO: Solo tareas kanban fuera de horario de oficina`);

      const filteredTasks = formattedTasks.filter(task => {
        const taskStartUTC = new Date(task.startTime);
        const taskEndUTC = new Date(task.endTime);

        // Trabajar directamente en hora peruana
        const taskStartLima = new Date(taskStartUTC);
        const taskEndLima = new Date(taskEndUTC);

        const dayOfWeek = taskStartLima.getDay(); // 0=Domingo, 1=Lunes, etc.

        // Verificar si es un día laborable
        if (!defaultSchedule.workDays.includes(dayOfWeek)) {
          console.log(`✅ TAREA KANBAN FUERA DE HORARIO (día no laboral): ${task.title}`);
          return true; // Día no laborable = fuera de horario
        }

        // Convertir horarios de oficina configurados a minutos
        const [startHour, startMin] = defaultSchedule.startTime.split(':').map(Number);
        const [endHour, endMin] = defaultSchedule.endTime.split(':').map(Number);
        const officeStartMinutes = startHour * 60 + startMin;
        const officeEndMinutes = endHour * 60 + endMin;

        // Convertir horarios de la tarea a minutos
        const taskStartMinutes = taskStartLima.getHours() * 60 + taskStartLima.getMinutes();
        const taskEndMinutes = taskEndLima.getHours() * 60 + taskEndLima.getMinutes();

        // Verificar si está fuera del horario de oficina configurado
        const isOutsideOfficeHours = taskStartMinutes < officeStartMinutes ||
                                     taskStartMinutes >= officeEndMinutes ||
                                     taskEndMinutes <= officeStartMinutes ||
                                     taskEndMinutes > officeEndMinutes;

        if (isOutsideOfficeHours) {
          console.log(`✅ TAREA KANBAN FUERA DE HORARIO: ${task.title} (${taskStartLima.getHours()}:${taskStartLima.getMinutes().toString().padStart(2, '0')} - ${taskEndLima.getHours()}:${taskEndLima.getMinutes().toString().padStart(2, '0')}) - Horario configurado: ${defaultSchedule.startTime}-${defaultSchedule.endTime}`);
        }

        return isOutsideOfficeHours;
      });

      console.log(`📊 Tareas kanban filtradas (fuera de horario): ${filteredTasks.length} de ${formattedTasks.length}`);
      res.json(filteredTasks);
    } else {
      res.json(formattedTasks);
    }

  } catch (error) {
    console.error('Error al obtener tareas kanban archivadas:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
};

// Obtener tareas para el calendario
exports.getTasksForCalendar = async (req, res) => {
  try {
    const { startDate, endDate, userId } = req.query;
    
    // Validar fechas
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Se requieren fechas de inicio y fin' });
    }

    // Obtener horarios de trabajo configurados
    const workSchedules = await prisma.workSchedule.findMany();
    console.log(`📅 Horarios de trabajo cargados: ${workSchedules.length}`);

    // Obtener tareas para el rango de fechas
    const tasks = await prisma.task.findMany({
      where: {
        OR: [
          {
            startTime: {
              gte: new Date(startDate),
              lte: new Date(endDate)
            }
          },
          {
            endTime: {
              gte: new Date(startDate),
              lte: new Date(endDate)
            }
          }
        ],
        userId: userId ? userId : undefined
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        timeEntries: true
      }
    });

    // Formatear tareas para el calendario
    const formattedTasks = tasks.map(task => ({
      id: task.id,
      title: task.title,
      startTime: task.startTime,
      endTime: task.endTime,
      status: task.status,
      priority: task.priority,
      user: task.user,
      description: task.description,
      hoursLogged: task.timeEntries.reduce((sum, entry) => sum + entry.hoursWorked, 0),
      isOutsideOfficeHours: false // Se actualizará a continuación
    }));

    // Verificar cada tarea si está fuera del horario de oficina
    for (let task of formattedTasks) {
      const taskStartUTC = new Date(task.startTime);
      const taskEndUTC = new Date(task.endTime);
      
      // Ajustar a zona horaria de Lima (UTC-5)
      const limaOffset = -5 * 60; // Lima es UTC-5
      const serverOffset = taskStartUTC.getTimezoneOffset();
      const offsetDiff = limaOffset - serverOffset;
      
      const taskStartLima = new Date(taskStartUTC.getTime() + offsetDiff * 60000);
      const taskEndLima = new Date(taskEndUTC.getTime() + offsetDiff * 60000);
      
      const dayOfWeek = taskStartLima.getDay(); // 0=Domingo, 1=Lunes, etc.
      
      // Buscar horario para el día de la semana de la tarea
      const daySchedule = workSchedules.find(schedule => schedule.dayOfWeek === dayOfWeek);
      
      if (!daySchedule) {
        // Si no hay horario para este día, está fuera del horario de oficina
        task.isOutsideOfficeHours = true;
        console.log(`📅 Tarea fuera de horario (día no laboral): ${task.title}`);
        continue;
      }
      
      // Convertir horarios de oficina a minutos
      const [startHour, startMin] = daySchedule.startTime.split(':').map(Number);
      const [endHour, endMin] = daySchedule.endTime.split(':').map(Number);
      const officeStartMinutes = startHour * 60 + startMin;
      const officeEndMinutes = endHour * 60 + endMin;
      
      // Convertir horarios de la tarea a minutos
      const taskStartMinutes = taskStartLima.getHours() * 60 + taskStartLima.getMinutes();
      const taskEndMinutes = taskEndLima.getHours() * 60 + taskEndLima.getMinutes();
      
      // Verificar si está fuera del horario de oficina
      task.isOutsideOfficeHours = taskStartMinutes < officeStartMinutes || 
                                 taskStartMinutes >= officeEndMinutes || 
                                 taskEndMinutes <= officeStartMinutes || 
                                 taskEndMinutes > officeEndMinutes;
      
      if (task.isOutsideOfficeHours) {
        console.log(`📅 Tarea fuera de horario: ${task.title} (${taskStartLima.getHours()}:${taskStartLima.getMinutes().toString().padStart(2, '0')} - ${taskEndLima.getHours()}:${taskEndLima.getMinutes().toString().padStart(2, '0')})`);
        console.log(`   Horario oficina: ${daySchedule.startTime} - ${daySchedule.endTime}`);
      }
    }

    res.json(formattedTasks);
  } catch (error) {
    console.error('Error al obtener tareas para el calendario:', error);
    res.status(500).json({ error: 'Error al obtener tareas para el calendario' });
  }
};


