const express = require('express');
const router = express.Router();
const kanbanController = require('../controllers/kanbanController');
const { authenticateToken } = require('../middleware/auth');

// Todas las rutas requieren autenticación
router.use(authenticateToken);

// Rutas para el tablero Kanban
router.get('/board', kanbanController.getKanbanBoard);
router.post('/tasks', kanbanController.createTask);
router.patch('/tasks/:id/status', kanbanController.updateTaskStatus);
router.put('/tasks/:id', kanbanController.updateTask);
router.delete('/tasks/:id', kanbanController.deleteTask);
router.get('/tasks/archived', authenticateToken, kanbanController.getArchivedTasks);

// Add this route to your kanban routes
router.get('/check-work-hours', authenticateToken, kanbanController.checkWorkHours);

module.exports = router;
