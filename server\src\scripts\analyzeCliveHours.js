const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function analyzeCliveHours() {
  try {
    console.log('🔍 ANALIZANDO HORAS DE CLIVE.NAHUI...\n');

    // Buscar el usuario clive.nahui
    const cliveUser = await prisma.user.findFirst({
      where: {
        OR: [
          { name: { contains: 'clive', mode: 'insensitive' } },
          { email: { contains: 'clive', mode: 'insensitive' } }
        ]
      }
    });

    if (!cliveUser) {
      console.log('❌ Usuario clive.nahui no encontrado');
      return;
    }

    console.log(`👤 Usuario encontrado: ${cliveUser.name} (${cliveUser.email})`);
    console.log(`🆔 ID: ${cliveUser.id}\n`);

    // Obtener la semana actual (como lo hace el dashboard)
    const now = new Date();
    const currentDay = now.getDay();
    const daysToMonday = currentDay === 0 ? 6 : currentDay - 1;
    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - daysToMonday);
    weekStart.setHours(0, 0, 0, 0);
    
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);

    console.log(`📅 SEMANA ANALIZADA: ${weekStart.toISOString().split('T')[0]} - ${weekEnd.toISOString().split('T')[0]}\n`);

    // Obtener todas las entradas de tiempo de la semana
    const timeEntries = await prisma.timeEntry.findMany({
      where: {
        userId: cliveUser.id,
        startTime: { gte: weekStart, lte: weekEnd },
        status: 'APPROVED'
      },
      orderBy: {
        startTime: 'asc'
      }
    });

    console.log(`📊 ENTRADAS DE TIEMPO ENCONTRADAS: ${timeEntries.length}\n`);

    if (timeEntries.length === 0) {
      console.log('❌ No se encontraron entradas de tiempo aprobadas para esta semana');
      return;
    }

    // Obtener configuración de horarios de trabajo
    const workSchedules = await prisma.workSchedule.findMany({
      orderBy: { dayOfWeek: 'asc' }
    });

    console.log('⏰ CONFIGURACIÓN DE HORARIOS:');
    if (workSchedules.length > 0) {
      workSchedules.forEach(schedule => {
        const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
        console.log(`   ${dayNames[schedule.dayOfWeek]}: ${schedule.startTime} - ${schedule.endTime} (Almuerzo: ${schedule.lunchBreak})`);
      });
    } else {
      console.log('   Usando horarios por defecto: 09:00 - 18:00 (Almuerzo: 13:00)');
    }
    console.log('');

    // Analizar cada entrada
    let totalOutsideHours = 0;

    timeEntries.forEach((entry, index) => {
      // APLICAR ZONA HORARIA DE LIMA, PERÚ (UTC-5) como en el controlador
      const entryDateUTC = new Date(entry.startTime);
      const entryEndDateUTC = new Date(entry.endTime);

      // Convertir a hora peruana (UTC-5)
      const entryDate = new Date(entryDateUTC.getTime() - 5 * 60 * 60 * 1000);
      const entryEndDate = new Date(entryEndDateUTC.getTime() - 5 * 60 * 60 * 1000);
      const dayOfWeek = entryDate.getDay();
      const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
      
      console.log(`${index + 1}. 📝 ENTRADA: "${entry.title}"`);
      console.log(`   📅 Fecha: ${dayNames[dayOfWeek]} ${entryDate.toISOString().split('T')[0]}`);
      console.log(`   ⏰ Hora inicio (UTC): ${entryDateUTC.toTimeString().split(' ')[0]}`);
      console.log(`   ⏰ Hora inicio (Perú): ${entryDate.toTimeString().split(' ')[0]}`);
      console.log(`   ⏰ Hora fin (UTC): ${entryEndDateUTC.toTimeString().split(' ')[0]}`);
      console.log(`   ⏰ Hora fin (Perú): ${entryEndDate.toTimeString().split(' ')[0]}`);
      console.log(`   📊 Horas trabajadas: ${entry.hoursWorked}h`);
      console.log(`   📊 Horas multiplicadas: ${entry.multipliedHours || 'N/A'}h`);
      console.log(`   📊 Estado: ${entry.status}`);

      // Calcular horas fuera de oficina para esta entrada
      let entryOutsideHours = 0;

      // Buscar horario para este día de la semana
      const daySchedule = workSchedules.find(s => s.dayOfWeek === dayOfWeek);
      
      if (daySchedule) {
        console.log(`   ⏰ Horario configurado: ${daySchedule.startTime} - ${daySchedule.endTime}`);
        
        // Convertir horarios a minutos
        const [workStartHour, workStartMin] = daySchedule.startTime.split(':').map(Number);
        const [workEndHour, workEndMin] = daySchedule.endTime.split(':').map(Number);
        const workStartMinutes = workStartHour * 60 + workStartMin;
        const workEndMinutes = workEndHour * 60 + workEndMin;

        const entryStartMinutes = entryDate.getHours() * 60 + entryDate.getMinutes();
        const entryEndMinutes = entryEndDate.getHours() * 60 + entryEndDate.getMinutes();

        console.log(`   🕐 Entrada: ${Math.floor(entryStartMinutes/60)}:${(entryStartMinutes%60).toString().padStart(2,'0')} - ${Math.floor(entryEndMinutes/60)}:${(entryEndMinutes%60).toString().padStart(2,'0')}`);
        console.log(`   🏢 Oficina: ${Math.floor(workStartMinutes/60)}:${(workStartMinutes%60).toString().padStart(2,'0')} - ${Math.floor(workEndMinutes/60)}:${(workEndMinutes%60).toString().padStart(2,'0')}`);

        // Calcular horas antes del trabajo
        if (entryStartMinutes < workStartMinutes) {
          const endBeforeWork = Math.min(entryEndMinutes, workStartMinutes);
          const hoursBeforeWork = Math.max(0, (endBeforeWork - entryStartMinutes) / 60);
          entryOutsideHours += hoursBeforeWork;
          console.log(`   🌅 Antes del trabajo: ${hoursBeforeWork.toFixed(2)}h`);
        }

        // Calcular horas después del trabajo
        if (entryEndMinutes > workEndMinutes) {
          const startAfterWork = Math.max(entryStartMinutes, workEndMinutes);
          const hoursAfterWork = Math.max(0, (entryEndMinutes - startAfterWork) / 60);
          entryOutsideHours += hoursAfterWork;
          console.log(`   🌙 Después del trabajo: ${hoursAfterWork.toFixed(2)}h`);
        }

        // Si no hay horario configurado para este día, todas las horas son fuera de oficina
        if (!daySchedule.startTime || !daySchedule.endTime) {
          entryOutsideHours = parseFloat(entry.hoursWorked);
          console.log(`   🚫 Sin horario configurado - todas las horas son fuera de oficina: ${entryOutsideHours}h`);
        }
      } else {
        // Si es fin de semana o no hay configuración, todas las horas son fuera de oficina
        entryOutsideHours = parseFloat(entry.hoursWorked);
        console.log(`   🚫 Sin horario configurado para ${dayNames[dayOfWeek]} - todas las horas son fuera de oficina: ${entryOutsideHours}h`);
      }

      console.log(`   💰 Horas fuera de oficina para esta entrada: ${entryOutsideHours.toFixed(2)}h`);
      totalOutsideHours += entryOutsideHours;
      console.log('');
    });

    console.log(`🎯 RESUMEN FINAL:`);
    console.log(`   Total entradas analizadas: ${timeEntries.length}`);
    console.log(`   Total horas fuera de oficina: ${totalOutsideHours.toFixed(2)}h`);
    console.log(`   Total horas trabajadas: ${timeEntries.reduce((sum, entry) => sum + parseFloat(entry.hoursWorked), 0)}h`);

  } catch (error) {
    console.error('❌ ERROR AL ANALIZAR HORAS:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  analyzeCliveHours();
}

module.exports = { analyzeCliveHours };
