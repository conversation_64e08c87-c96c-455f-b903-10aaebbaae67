import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Chip,
  Stack,
  Card,
  CardContent,
  Typography,
  Autocomplete,
  ButtonGroup,
} from '@mui/material';
import {
  FilterList as FilterIcon,
  Clear as ClearIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { es } from 'date-fns/locale';
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns';

const PermissionFilters = ({
  onFiltersChange,
  currentFilters = {},
  showEmployeeFilter = false,
  employees = [],
  permissionTypes = [
    'Vacaciones',
    'Enfermedad',
    'Personal',
    'Compensación de horas extra',
    'Maternidad/Paternidad',
    'Capacitación',
    'Otros'
  ]
}) => {
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    employeeId: '',
    startDate: null,
    endDate: null,
    permissionStartDate: null,
    permissionEndDate: null,
    searchText: '',
    ...currentFilters
  });

  const statusOptions = [
    { value: '', label: 'Todos los estados' },
    { value: 'PENDING', label: 'Pendiente' },
    { value: 'APPROVED', label: 'Aprobado' },
    { value: 'REJECTED', label: 'Rechazado' },
  ];

  const typeOptions = [
    { value: '', label: 'Todos los tipos' },
    ...permissionTypes.map(type => ({ value: type, label: type }))
  ];

  const employeeOptions = [
    { value: '', label: 'Todos los empleados' },
    ...employees.map(emp => ({ 
      value: emp.id, 
      label: `${emp.firstName} ${emp.lastName}` 
    }))
  ];

  // Aplicar filtros cuando cambien
  useEffect(() => {
    onFiltersChange(filters);
  }, [filters, onFiltersChange]);

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const clearFilters = () => {
    const clearedFilters = {
      status: '',
      type: '',
      employeeId: '',
      startDate: null,
      endDate: null,
      permissionStartDate: null,
      permissionEndDate: null,
      searchText: '',
    };
    setFilters(clearedFilters);
  };

  const applyQuickFilter = (filterType) => {
    const today = new Date();
    let startDate, endDate;

    switch (filterType) {
      case 'today':
        startDate = today;
        endDate = today;
        break;
      case 'week':
        startDate = subDays(today, 7);
        endDate = today;
        break;
      case 'month':
        startDate = startOfMonth(today);
        endDate = endOfMonth(today);
        break;
      case 'last30':
        startDate = subDays(today, 30);
        endDate = today;
        break;
      case 'pending':
        setFilters(prev => ({ ...prev, status: 'PENDING' }));
        return;
      default:
        return;
    }

    setFilters(prev => ({
      ...prev,
      startDate,
      endDate
    }));
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => 
      value !== '' && value !== null && value !== undefined
    ).length;
  };

  const getActiveFiltersChips = () => {
    const chips = [];

    if (filters.status) {
      const statusLabel = statusOptions.find(opt => opt.value === filters.status)?.label;
      chips.push({ key: 'status', label: `Estado: ${statusLabel}`, value: filters.status });
    }

    if (filters.type) {
      chips.push({ key: 'type', label: `Tipo: ${filters.type}`, value: filters.type });
    }

    if (filters.employeeId && showEmployeeFilter) {
      const employeeLabel = employeeOptions.find(opt => opt.value === filters.employeeId)?.label;
      chips.push({ key: 'employeeId', label: `Empleado: ${employeeLabel}`, value: filters.employeeId });
    }

    if (filters.startDate) {
      chips.push({ 
        key: 'startDate', 
        label: `Desde: ${format(filters.startDate, 'dd/MM/yyyy')}`, 
        value: filters.startDate 
      });
    }

    if (filters.endDate) {
      chips.push({ 
        key: 'endDate', 
        label: `Hasta: ${format(filters.endDate, 'dd/MM/yyyy')}`, 
        value: filters.endDate 
      });
    }

    if (filters.searchText) {
      chips.push({ key: 'searchText', label: `Búsqueda: "${filters.searchText}"`, value: filters.searchText });
    }

    return chips;
  };

  const removeFilter = (filterKey) => {
    handleFilterChange(filterKey, filterKey.includes('Date') ? null : '');
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={es}>
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ mb: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <FilterIcon />
              Filtros de Permisos
              {getActiveFiltersCount() > 0 && (
                <Chip 
                  label={`${getActiveFiltersCount()} activo${getActiveFiltersCount() !== 1 ? 's' : ''}`} 
                  size="small" 
                  color="primary" 
                />
              )}
            </Typography>
          </Box>

          {/* Filtros Rápidos */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Filtros Rápidos:
            </Typography>
            <ButtonGroup variant="outlined" size="small" sx={{ flexWrap: 'wrap', gap: 1 }}>
              <Button onClick={() => applyQuickFilter('pending')}>
                Pendientes
              </Button>
              <Button onClick={() => applyQuickFilter('today')}>
                Hoy
              </Button>
              <Button onClick={() => applyQuickFilter('week')}>
                Última Semana
              </Button>
              <Button onClick={() => applyQuickFilter('month')}>
                Este Mes
              </Button>
              <Button onClick={() => applyQuickFilter('last30')}>
                Últimos 30 días
              </Button>
            </ButtonGroup>
          </Box>

          {/* Filtros Principales */}
          <Grid container spacing={2} sx={{ mb: 2 }}>
            {/* Búsqueda por texto */}
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Buscar"
                placeholder="Empleado, razón..."
                value={filters.searchText}
                onChange={(e) => handleFilterChange('searchText', e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
                size="small"
              />
            </Grid>

            {/* Estado */}
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Estado</InputLabel>
                <Select
                  value={filters.status}
                  label="Estado"
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  {statusOptions.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Tipo */}
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Tipo</InputLabel>
                <Select
                  value={filters.type}
                  label="Tipo"
                  onChange={(e) => handleFilterChange('type', e.target.value)}
                >
                  {typeOptions.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Empleado (solo para admins) */}
            {showEmployeeFilter && (
              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Empleado</InputLabel>
                  <Select
                    value={filters.employeeId}
                    label="Empleado"
                    onChange={(e) => handleFilterChange('employeeId', e.target.value)}
                  >
                    {employeeOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}

            {/* Fecha Desde */}
            <Grid item xs={12} md={2}>
              <DatePicker
                label="Desde"
                value={filters.startDate}
                onChange={(date) => handleFilterChange('startDate', date)}
                slotProps={{ 
                  textField: { 
                    size: 'small',
                    fullWidth: true 
                  } 
                }}
              />
            </Grid>

            {/* Fecha Hasta */}
            <Grid item xs={12} md={2}>
              <DatePicker
                label="Hasta"
                value={filters.endDate}
                onChange={(date) => handleFilterChange('endDate', date)}
                slotProps={{ 
                  textField: { 
                    size: 'small',
                    fullWidth: true 
                  } 
                }}
              />
            </Grid>
          </Grid>

          {/* Chips de filtros activos */}
          {getActiveFiltersCount() > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Filtros Activos:
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                {getActiveFiltersChips().map((chip) => (
                  <Chip
                    key={chip.key}
                    label={chip.label}
                    onDelete={() => removeFilter(chip.key)}
                    color="primary"
                    variant="outlined"
                    size="small"
                  />
                ))}
              </Stack>
            </Box>
          )}

          {/* Botón limpiar filtros */}
          {getActiveFiltersCount() > 0 && (
            <Box>
              <Button
                startIcon={<ClearIcon />}
                onClick={clearFilters}
                variant="outlined"
                size="small"
              >
                Limpiar Filtros
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>
    </LocalizationProvider>
  );
};

export default PermissionFilters;
