@echo off
echo Aplicando migración manual a la base de datos Docker...

echo.
echo Buscando contenedores de PostgreSQL...
docker ps | findstr postgres

echo.
echo IMPORTANTE: Ajusta los siguientes valores según tu configuración:
echo - Nombre del contenedor PostgreSQL
echo - Usuario de la base de datos
echo - Nombre de la base de datos
echo.

set /p CONTAINER_NAME=Nombre del contenedor PostgreSQL:
set /p DB_USER=Usuario de la base de datos:
set /p DB_NAME=Nombre de la base de datos:

echo.
echo Aplicando migración...
docker exec -i %CONTAINER_NAME% psql -U %DB_USER% -d %DB_NAME% < server/manual_migration.sql

echo.
echo Ejecutando verificación...
docker exec -i %CONTAINER_NAME% psql -U %DB_USER% -d %DB_NAME% < server/verify_migration.sql

echo.
echo Migración completada. Revisa los resultados de verificación arriba.
echo Si todo está correcto, reinicia el servidor backend y frontend.
pause
