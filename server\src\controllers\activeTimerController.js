const { PrismaClient } = require('@prisma/client');
const socketService = require('../services/socketService');
const prisma = new PrismaClient();

// Iniciar o reanudar el contador de tiempo
exports.startTimer = async (req, res) => {
  try {
    const { taskId } = req.body;
    const userId = req.user.id;

    // Verificar si ya existe un contador activo para esta tarea y usuario
    let activeTimer = await prisma.activeTimer.findFirst({
      where: {
        taskId,
        userId,
      },
    });

    const now = new Date();

    if (activeTimer) {
      // Si existe y no está corriendo, reanudar
      if (!activeTimer.isRunning) {
        activeTimer = await prisma.activeTimer.update({
          where: { id: activeTimer.id },
          data: {
            isRunning: true,
            lastStarted: now,
          },
        });
      }
    } else {
      // Si no existe, crear nuevo
      activeTimer = await prisma.activeTimer.create({
        data: {
          taskId,
          userId,
          startTime: now,
          lastStarted: now,
          isRunning: true,
          accumulatedTime: 0,
        },
      });
    }

    // Notificar a los clientes sobre el inicio del timer
    socketService.notifyTimerUpdate(taskId, {
      action: 'start',
      isRunning: true,
      accumulatedTime: activeTimer.accumulatedTime,
      userId: userId,
      userName: req.user.name
    });

    res.json(activeTimer);
  } catch (error) {
    console.error('Error al iniciar el contador:', error);
    res.status(500).json({ error: 'Error al iniciar el contador' });
  }
};

// Pausar el contador de tiempo
exports.pauseTimer = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    const activeTimer = await prisma.activeTimer.findFirst({
      where: {
        taskId,
        userId,
        isRunning: true,
      },
    });

    if (!activeTimer) {
      return res.status(404).json({ error: 'Contador no encontrado o no está activo' });
    }

    const now = new Date();
    const elapsedSeconds = Math.floor((now - activeTimer.lastStarted) / 1000);
    const newAccumulatedTime = activeTimer.accumulatedTime + elapsedSeconds;

    const updatedTimer = await prisma.activeTimer.update({
      where: { id: activeTimer.id },
      data: {
        isRunning: false,
        accumulatedTime: newAccumulatedTime,
      },
    });

    // Notificar a los clientes sobre la pausa del timer
    socketService.notifyTimerUpdate(taskId, {
      action: 'pause',
      isRunning: false,
      accumulatedTime: newAccumulatedTime,
      userId: userId,
      userName: req.user.name
    });

    res.json(updatedTimer);
  } catch (error) {
    console.error('Error al pausar el contador:', error);
    res.status(500).json({ error: 'Error al pausar el contador' });
  }
};

// Detener el contador y crear una entrada de tiempo
exports.stopTimer = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    const activeTimer = await prisma.activeTimer.findFirst({
      where: {
        taskId,
        userId,
      },
      include: {
        task: true,
      },
    });

    if (!activeTimer) {
      return res.status(404).json({ error: 'Contador no encontrado' });
    }

    const now = new Date();
    let totalSeconds = activeTimer.accumulatedTime;

    // Si el contador está corriendo, agregar el tiempo transcurrido
    if (activeTimer.isRunning) {
      const elapsedSeconds = Math.floor((now - activeTimer.lastStarted) / 1000);
      totalSeconds += elapsedSeconds;
    }

    // Crear una entrada de tiempo
    const timeEntry = await prisma.timeEntry.create({
      data: {
        title: `Tiempo trabajado en: ${activeTimer.task.title}`,
        startTime: activeTimer.startTime,
        endTime: now,
        hoursWorked: Number((totalSeconds / 3600).toFixed(2)), // Convertir segundos a horas
        userId: userId,
        taskId: taskId,
        type: 'REGULAR',
        status: 'PENDING',
      },
    });

    // Eliminar el contador activo
    await prisma.activeTimer.delete({
      where: { id: activeTimer.id },
    });

    // Notificar a los clientes sobre la detención del timer
    socketService.notifyTimerUpdate(taskId, {
      action: 'stop',
      isRunning: false,
      accumulatedTime: 0,
      userId: userId,
      userName: req.user.name,
      timeEntry: timeEntry
    });

    res.json({
      timeEntry,
      totalTimeSeconds: totalSeconds,
    });
  } catch (error) {
    console.error('Error al detener el contador:', error);
    res.status(500).json({ error: 'Error al detener el contador' });
  }
};

// Obtener el estado actual del contador
exports.getTimerStatus = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    const activeTimer = await prisma.activeTimer.findFirst({
      where: {
        taskId,
        userId,
      },
    });

    if (!activeTimer) {
      return res.json({
        isRunning: false,
        accumulatedTime: 0,
      });
    }

    let totalSeconds = activeTimer.accumulatedTime;

    // Si el contador está corriendo, calcular el tiempo total incluyendo el tiempo actual
    if (activeTimer.isRunning) {
      const now = new Date();
      const elapsedSeconds = Math.floor((now - activeTimer.lastStarted) / 1000);
      totalSeconds += elapsedSeconds;
    }

    res.json({
      isRunning: activeTimer.isRunning,
      accumulatedTime: totalSeconds,
      startTime: activeTimer.startTime,
      lastStarted: activeTimer.lastStarted,
    });
  } catch (error) {
    console.error('Error al obtener el estado del contador:', error);
    res.status(500).json({ error: 'Error al obtener el estado del contador' });
  }
};