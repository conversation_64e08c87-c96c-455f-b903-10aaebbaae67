
const { PrismaClient } = require('@prisma/client');
const socketService = require('../services/socketService');

const prisma = new PrismaClient();

// Iniciar temporizador global de una tarea
exports.startGlobalTimer = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    console.log(`[GlobalTimer] Starting timer for task ${taskId} by user ${req.user.name}`);

    // Verificar que la tarea existe y obtener datos actuales
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        assignee: true,
        timerCurrentUser: true
      }
    });

    if (!task) {
      return res.status(404).json({ message: 'Tarea no encontrada' });
    }

    // Verificar permisos: solo el assignee, admins o managers pueden controlar el temporizador
    const canControlTimer = task.assigneeId === userId || 
                           req.user.role === 'ADMIN' || 
                           req.user.role === 'MANAGER';

    if (!canControlTimer) {
      return res.status(403).json({ 
        message: 'No tienes permisos para controlar el temporizador de esta tarea.' 
      });
    }

    // Si ya está corriendo, no hacer nada
    if (task.timerIsRunning) {
      return res.status(400).json({ 
        message: 'El temporizador ya está corriendo',
        currentUser: task.timerCurrentUser?.name || 'Usuario desconocido'
      });
    }

    const now = new Date();

    // Iniciar el temporizador global
    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: {
        timerIsRunning: true,
        timerStartTime: task.timerStartTime || now,
        timerLastStarted: now,
        timerCurrentUserId: userId,
        status: 'IN_PROGRESS'
      },
      include: {
        assignee: true,
        creator: true,
        timerCurrentUser: true
      }
    });

    // Formatear respuesta
    const response = {
      id: updatedTask.id,
      title: updatedTask.title,
      status: updatedTask.status,
      globalTimer: {
        isRunning: updatedTask.timerIsRunning,
        accumulatedTime: updatedTask.timerAccumulatedSeconds,
        startTime: updatedTask.timerStartTime,
        lastStarted: updatedTask.timerLastStarted,
        currentUser: updatedTask.timerCurrentUser?.name || null,
        currentUserId: updatedTask.timerCurrentUserId
      }
    };

    // Notificar a todas las pestañas
    socketService.notifyGlobalTimerUpdate(response);

    console.log(`[GlobalTimer] Timer started successfully for task ${taskId}`);
    res.json(response);

  } catch (error) {
    console.error('Error starting global timer:', error);
    res.status(500).json({ 
      message: 'Error al iniciar el temporizador', 
      error: error.message 
    });
  }
};

// Pausar temporizador global de una tarea
exports.pauseGlobalTimer = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    console.log(`[GlobalTimer] Pausing timer for task ${taskId} by user ${req.user.name}`);

    // Verificar que la tarea existe
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        assignee: true,
        timerCurrentUser: true
      }
    });

    if (!task) {
      return res.status(404).json({ message: 'Tarea no encontrada' });
    }

    // Verificar permisos
    const canControlTimer = task.assigneeId === userId || 
                           req.user.role === 'ADMIN' || 
                           req.user.role === 'MANAGER';

    if (!canControlTimer) {
      return res.status(403).json({ 
        message: 'No tienes permisos para controlar el temporizador de esta tarea.' 
      });
    }

    // Si no está corriendo, no hacer nada
    if (!task.timerIsRunning) {
      return res.status(400).json({ 
        message: 'El temporizador no está corriendo' 
      });
    }

    const now = new Date();
    const elapsedSeconds = Math.floor((now - new Date(task.timerLastStarted)) / 1000);

    // Pausar el temporizador global
    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: {
        timerIsRunning: false,
        timerAccumulatedSeconds: task.timerAccumulatedSeconds + elapsedSeconds,
        timerCurrentUserId: null
      },
      include: {
        assignee: true,
        creator: true,
        timerCurrentUser: true
      }
    });

    // Formatear respuesta
    const response = {
      id: updatedTask.id,
      title: updatedTask.title,
      status: updatedTask.status,
      globalTimer: {
        isRunning: updatedTask.timerIsRunning,
        accumulatedTime: updatedTask.timerAccumulatedSeconds,
        startTime: updatedTask.timerStartTime,
        lastStarted: updatedTask.timerLastStarted,
        currentUser: null,
        currentUserId: null
      }
    };

    // Notificar a todas las pestañas
    socketService.notifyGlobalTimerUpdate(response);

    console.log(`[GlobalTimer] Timer paused successfully for task ${taskId}. Added ${elapsedSeconds} seconds. Total: ${updatedTask.timerAccumulatedSeconds} seconds`);
    res.json(response);

  } catch (error) {
    console.error('Error pausing global timer:', error);
    res.status(500).json({ 
      message: 'Error al pausar el temporizador', 
      error: error.message 
    });
  }
};

// Obtener estado del temporizador global
exports.getGlobalTimerStatus = async (req, res) => {
  try {
    const { taskId } = req.params;

    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        timerCurrentUser: true
      }
    });

    if (!task) {
      return res.status(404).json({ message: 'Tarea no encontrada' });
    }

    let totalSeconds = task.timerAccumulatedSeconds || 0;

    // Si está corriendo, agregar tiempo transcurrido
    if (task.timerIsRunning && task.timerLastStarted) {
      const now = new Date();
      const elapsedSeconds = Math.floor((now - new Date(task.timerLastStarted)) / 1000);
      totalSeconds += elapsedSeconds;
    }

    const response = {
      id: task.id,
      title: task.title,
      status: task.status,
      globalTimer: {
        isRunning: task.timerIsRunning,
        accumulatedTime: totalSeconds,
        startTime: task.timerStartTime,
        lastStarted: task.timerLastStarted,
        currentUser: task.timerCurrentUser?.name || null,
        currentUserId: task.timerCurrentUserId
      }
    };

    res.json(response);

  } catch (error) {
    console.error('Error getting global timer status:', error);
    res.status(500).json({ 
      message: 'Error al obtener estado del temporizador', 
      error: error.message 
    });
  }
};
