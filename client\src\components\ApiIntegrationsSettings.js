import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  CircularProgress,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
  Science as TestIcon,
  CloudDownload as ImportIcon,
  Assessment as StatsIcon
} from '@mui/icons-material';
import { API_URL } from '../config';
import ApiBuilderDialog from './ApiBuilderDialog';
import UserApiKeysManagement from './UserApiKeysManagement';

const ApiIntegrationsSettings = () => {
  const [integrations, setIntegrations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [selectedIntegration, setSelectedIntegration] = useState(null);
  const [showApiBuilder, setShowApiBuilder] = useState(false);
  const [showUserApiKeys, setShowUserApiKeys] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [integrationToDelete, setIntegrationToDelete] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [stats, setStats] = useState(null);

  useEffect(() => {
    fetchIntegrations();
    fetchStats();
  }, []);

  const fetchIntegrations = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/integrations`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setIntegrations(data);
      } else {
        throw new Error('Error al cargar integraciones');
      }
    } catch (error) {
      console.error('Error al cargar integraciones:', error);
      showSnackbar('Error al cargar las integraciones', 'error');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/integrations/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error al cargar estadísticas:', error);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCreateIntegration = () => {
    setSelectedIntegration(null);
    setShowApiBuilder(true);
  };

  const handleEditIntegration = (integration) => {
    setSelectedIntegration(integration);
    setShowApiBuilder(true);
  };

  const handleDeleteIntegration = (integration) => {
    setIntegrationToDelete(integration);
    setShowDeleteDialog(true);
  };

  const confirmDeleteIntegration = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/integrations/${integrationToDelete.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        showSnackbar('Integración eliminada exitosamente');
        fetchIntegrations();
        fetchStats();
      } else {
        throw new Error('Error al eliminar integración');
      }
    } catch (error) {
      console.error('Error al eliminar integración:', error);
      showSnackbar('Error al eliminar la integración', 'error');
    } finally {
      setShowDeleteDialog(false);
      setIntegrationToDelete(null);
    }
  };

  const handleManageApiKeys = (integration) => {
    setSelectedIntegration(integration);
    setShowUserApiKeys(true);
  };

  const handleIntegrationSaved = () => {
    setShowApiBuilder(false);
    setSelectedIntegration(null);
    fetchIntegrations();
    fetchStats();
    showSnackbar('Integración guardada exitosamente');
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="Integraciones" />
          <Tab label="API Keys por Usuario" />
          <Tab label="Estadísticas" />
        </Tabs>
      </Box>

      {/* Tab 0: Lista de Integraciones */}
      {tabValue === 0 && (
        <Paper sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">Integraciones API Configuradas</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateIntegration}
            >
              Nueva Integración
            </Button>
          </Box>

          <Alert severity="info" sx={{ mb: 3 }}>
            Configure aquí las integraciones con sistemas externos como ManageEngine, Jira, ServiceNow, etc. 
            Los usuarios podrán importar automáticamente sus tickets asignados desde estos sistemas.
          </Alert>

          {integrations.length === 0 ? (
            <Alert severity="warning">
              No hay integraciones configuradas. Haga click en "Nueva Integración" para comenzar.
            </Alert>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Nombre</TableCell>
                    <TableCell>Sistema</TableCell>
                    <TableCell>URL Base</TableCell>
                    <TableCell>Endpoints</TableCell>
                    <TableCell>Usuarios</TableCell>
                    <TableCell>Estado</TableCell>
                    <TableCell>Acciones</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {integrations.map((integration) => (
                    <TableRow key={integration.id}>
                      <TableCell>
                        <Typography variant="subtitle2">{integration.name}</Typography>
                        {integration.description && (
                          <Typography variant="caption" color="textSecondary">
                            {integration.description}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={integration.authType} 
                          size="small" 
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {integration.baseUrl}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={`${integration.endpoints?.length || 0} endpoints`}
                          size="small"
                          color="primary"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={`${integration._count?.userApiKeys || 0} usuarios`}
                          size="small"
                          color="secondary"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={integration.isActive ? 'Activo' : 'Inactivo'}
                          color={integration.isActive ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Editar integración">
                          <IconButton 
                            size="small" 
                            onClick={() => handleEditIntegration(integration)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Gestionar API Keys">
                          <IconButton 
                            size="small" 
                            onClick={() => handleManageApiKeys(integration)}
                          >
                            <SettingsIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Eliminar integración">
                          <IconButton 
                            size="small" 
                            onClick={() => handleDeleteIntegration(integration)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Paper>
      )}

      {/* Tab 1: API Keys por Usuario */}
      {tabValue === 1 && selectedIntegration && (
        <UserApiKeysManagement 
          integration={selectedIntegration}
          onClose={() => setSelectedIntegration(null)}
        />
      )}

      {/* Tab 2: Estadísticas */}
      {tabValue === 2 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Estadísticas de Integraciones
          </Typography>
          
          {stats && (
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="primary">{stats.totalIntegrations}</Typography>
                <Typography variant="body2">Total Integraciones</Typography>
              </Paper>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="success.main">{stats.activeIntegrations}</Typography>
                <Typography variant="body2">Activas</Typography>
              </Paper>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="info.main">{stats.usersWithApiKeys}</Typography>
                <Typography variant="body2">Usuarios Configurados</Typography>
              </Paper>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main">{stats.recentImports}</Typography>
                <Typography variant="body2">Importaciones (30d)</Typography>
              </Paper>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="success.main">{stats.successRate}%</Typography>
                <Typography variant="body2">Tasa de Éxito</Typography>
              </Paper>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="secondary.main">{stats.tasksFromIntegrations}</Typography>
                <Typography variant="body2">Tareas Importadas</Typography>
              </Paper>
            </Box>
          )}
        </Paper>
      )}

      {/* Diálogos */}
      <ApiBuilderDialog
        open={showApiBuilder}
        integration={selectedIntegration}
        onClose={() => setShowApiBuilder(false)}
        onSave={handleIntegrationSaved}
      />

      <UserApiKeysManagement
        open={showUserApiKeys}
        integration={selectedIntegration}
        onClose={() => setShowUserApiKeys(false)}
      />

      {/* Diálogo de confirmación de eliminación */}
      <Dialog open={showDeleteDialog} onClose={() => setShowDeleteDialog(false)}>
        <DialogTitle>Confirmar Eliminación</DialogTitle>
        <DialogContent>
          <Typography>
            ¿Está seguro de que desea eliminar la integración "{integrationToDelete?.name}"?
            Esta acción no se puede deshacer y eliminará todas las configuraciones asociadas.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDeleteDialog(false)}>Cancelar</Button>
          <Button onClick={confirmDeleteIntegration} color="error" variant="contained">
            Eliminar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar para notificaciones */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ApiIntegrationsSettings;
