const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkUserRole() {
  try {
    console.log('🔍 VERIFICANDO ROLES DE USUARIOS...\n');

    // Buscar todos los usuarios
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    console.log(`📊 TOTAL USUARIOS ENCONTRADOS: ${users.length}\n`);

    users.forEach(user => {
      console.log(`👤 ${user.name || user.email}`);
      console.log(`   📧 Email: ${user.email}`);
      console.log(`   🏷️ Rol: ${user.role}`);
      console.log(`   🆔 ID: ${user.id}`);
      console.log('');
    });

    // Buscar específicamente clive.nahui
    const cliveUser = users.find(user => 
      user.name?.toLowerCase().includes('clive') || 
      user.email?.toLowerCase().includes('clive')
    );

    if (cliveUser) {
      console.log('🎯 USUARIO CLIVE ENCONTRADO:');
      console.log(`   Nombre: ${cliveUser.name}`);
      console.log(`   Email: ${cliveUser.email}`);
      console.log(`   Rol: ${cliveUser.role}`);
      console.log(`   ID: ${cliveUser.id}`);
      
      if (cliveUser.role === 'ADMIN') {
        console.log('\n⚠️ PROBLEMA IDENTIFICADO:');
        console.log('   El usuario clive.nahui tiene rol ADMIN');
        console.log('   La tabla de horas extra excluye usuarios ADMIN');
        console.log('   Esto explica por qué no aparece en la tabla');
      }
    } else {
      console.log('❌ No se encontró usuario con "clive" en el nombre o email');
    }

    // Verificar entradas de tiempo recientes para clive
    if (cliveUser) {
      console.log('\n📝 VERIFICANDO ENTRADAS DE TIEMPO RECIENTES...');
      
      const recentEntries = await prisma.timeEntry.findMany({
        where: {
          userId: cliveUser.id,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Últimos 7 días
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 5
      });

      console.log(`📊 Entradas de tiempo en los últimos 7 días: ${recentEntries.length}`);
      
      recentEntries.forEach((entry, index) => {
        console.log(`\n${index + 1}. ${entry.title}`);
        console.log(`   📅 Fecha: ${entry.startTime.toISOString().split('T')[0]}`);
        console.log(`   ⏰ Horas: ${entry.hoursWorked}h`);
        console.log(`   📊 Estado: ${entry.status}`);
        console.log(`   💰 Horas multiplicadas: ${entry.multipliedHours || 'N/A'}h`);
      });
    }

  } catch (error) {
    console.error('❌ ERROR AL VERIFICAR USUARIOS:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  checkUserRole();
}

module.exports = { checkUserRole };
