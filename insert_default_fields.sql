-- Insertar campos por defecto para el sistema de campos dinámicos
INSERT INTO "DynamicField" ("id", "name", "label", "type", "required", "placeholder", "order", "isActive", "appliesTo") VALUES
('default-title', 'title', 'Título', 'text', true, 'Ingrese el título de la tarea', 1, true, 'both'),
('default-description', 'description', 'Descripción', 'textarea', false, 'Descripción detallada (opcional)', 2, true, 'both'),
('default-task', 'task', 'Tarea Relacionada', 'select', false, 'Seleccione una tarea (opcional)', 3, true, 'timeEntry'),
('default-hours', 'hoursWorked', 'Horas Trabajadas', 'number', true, 'Ej: 2.5', 4, true, 'timeEntry'),
('default-start-time', 'startTime', '<PERSON>cha y Hora de Inicio', 'datetime-local', true, '', 5, true, 'timeEntry'),
('default-end-time', 'endTime', 'Fecha y Hora de Fin', 'datetime-local', true, '', 6, true, 'timeEntry'),
('default-estimated-hours', 'estimatedHours', 'Horas Estimadas', 'number', false, 'Ej: 4', 7, true, 'task'),
('default-due-date', 'dueDate', 'Fecha Límite', 'date', false, '', 8, true, 'task'),
('default-priority', 'priority', 'Prioridad', 'select', false, 'Seleccione prioridad', 9, true, 'task')
ON CONFLICT ("name") DO NOTHING;

-- Actualizar el campo de prioridad con opciones
UPDATE "DynamicField" SET "options" = 'LOW,MEDIUM,HIGH,URGENT' WHERE "name" = 'priority';
