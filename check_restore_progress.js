const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkProgress() {
  try {
    console.log('🔍 Verificando progreso de restauración...');
    
    // Verificar usuarios
    const userCount = await prisma.user.count();
    console.log(`👥 Usuarios: ${userCount}`);
    
    if (userCount > 0) {
      const users = await prisma.user.findMany({
        select: { email: true, name: true, role: true },
        take: 5
      });
      console.log('📋 Usuarios encontrados:');
      users.forEach(user => {
        console.log(`   - ${user.email} (${user.name}) - ${user.role}`);
      });
    }
    
    // Verificar otras tablas importantes
    const timeEntryCount = await prisma.timeEntry.count();
    console.log(`⏰ Registros de tiempo: ${timeEntryCount}`);
    
    const taskCount = await prisma.task.count();
    console.log(`📝 Tareas: ${taskCount}`);
    
    const permissionCount = await prisma.permission.count();
    console.log(`🏖️ Permisos: ${permissionCount}`);
    
    const workScheduleCount = await prisma.workSchedule.count();
    console.log(`📅 Horarios de trabajo: ${workScheduleCount}`);
    
    console.log('\n✅ Verificación completada');
    
  } catch (error) {
    console.error('❌ Error al verificar:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkProgress();
