const axios = require('axios');

async function testRealCalendarAPI() {
  try {
    console.log('🔍 PROBANDO API REAL DEL CALENDARIO');
    console.log('='.repeat(60));

    // Simular la llamada que hace el frontend
    const startDate = '2025-07-04';
    const endDate = '2025-07-04';
    
    console.log(`📅 Llamando API: /api/tasks/completed-by-date`);
    console.log(`   startDate: ${startDate}`);
    console.log(`   endDate: ${endDate}`);

    const response = await axios.get('http://localhost:5000/api/tasks/completed-by-date', {
      params: {
        startDate,
        endDate
      },
      headers: {
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJjbTJhZGNqZGcwMDAwMTNkdGZhcGNqZGNlIiwiaWF0IjoxNzM1OTk5NzE5LCJleHAiOjE3MzYwODYxMTl9.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8' // Token de ejemplo
      }
    });

    console.log(`\n📊 RESPUESTA DE LA API:`);
    console.log(`   Status: ${response.status}`);
    console.log(`   Tareas encontradas: ${response.data.length}`);

    // Analizar cada tarea
    response.data.forEach((task, index) => {
      console.log(`\n${index + 1}. ${task.title}`);
      console.log(`   ID: ${task.id}`);
      console.log(`   Tipo: ${task.status || task.type}`);
      console.log(`   Inicio: ${task.startTime}`);
      console.log(`   Fin: ${task.endTime}`);
      console.log(`   ¿Fuera de horario? ${task.isOutsideOfficeHours ? '🔴 SÍ' : '🟢 NO'}`);
      
      if (task.title.includes('prueba')) {
        console.log(`   ⭐ TAREA DE PRUEBA ENCONTRADA: ${task.isOutsideOfficeHours ? '🔴 ROJO' : '🟢 VERDE'}`);
      }
    });

  } catch (error) {
    console.error('❌ Error al llamar la API:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

testRealCalendarAPI();
