# Instrucciones para Aplicar la Separación de Tareas Manuales

## Cambios Implementados

Se ha implementado la separación entre tareas del kanban y tareas de registros manuales:

### 1. **Cambios en la Base de Datos**
- Nuevo estado `COMPLETED` para tareas manuales
- Nuevo estado `DONE` para el flujo normal del kanban
- Nuevo campo `source` en la tabla Task (`KANBAN` | `MANUAL_ENTRY`)

### 2. **Cambios en el Comportamiento**
- **Tareas del Kanban**: `TODO` → `IN_PROGRESS` → `REVIEW` → `DONE` → `ARCHIVED`
- **Tareas Manuales**: Se crean directamente con estado `COMPLETED` y `source = 'MANUAL_ENTRY'`
- **Kanban**: Solo muestra tareas con `source = 'KANBAN'`
- **Tareas Completadas**: Muestra tanto tareas archivadas del kanban como tareas manuales completadas

## Aplicar la Migración

### Opción 1: Usando Docker (Recomendado)

1. **Identificar tu contenedor de PostgreSQL:**
```bash
docker ps | grep postgres
```

2. **Aplicar la migración:**
```bash
# Para Linux/Mac
chmod +x apply_migration.sh
./apply_migration.sh

# Para Windows
apply_migration.bat
```

3. **O manualmente:**
```bash
# Reemplaza 'tu_contenedor_postgres' con el nombre real de tu contenedor
docker exec -i tu_contenedor_postgres psql -U tu_usuario -d tu_base_datos < server/manual_migration.sql
```

### Opción 2: Usando Prisma (Si funciona)

```bash
cd server
npx prisma db push
npx prisma generate
```

## Verificar la Migración

Después de aplicar la migración, verifica que:

1. **Los nuevos estados existen:**
```sql
SELECT enumlabel FROM pg_enum WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'TaskStatus');
```

2. **El campo source existe:**
```sql
SELECT column_name, data_type, column_default FROM information_schema.columns WHERE table_name = 'Task' AND column_name = 'source';
```

3. **Las tareas existentes tienen source = 'KANBAN':**
```sql
SELECT COUNT(*) as total_tasks, source FROM "Task" GROUP BY source;
```

## Reiniciar la Aplicación

Después de aplicar la migración:

1. **Reinicia el servidor backend:**
```bash
cd server
npm start
```

2. **Reinicia el frontend:**
```bash
cd client
npm start
```

## Comportamiento Esperado

### Antes de la Migración:
- Todas las tareas aparecían tanto en kanban como en tareas completadas
- No había distinción entre tareas del flujo normal y registros manuales

### Después de la Migración:
- **Kanban**: Solo muestra tareas del flujo normal (source = 'KANBAN')
- **Tareas Completadas**: Muestra ambos tipos con etiquetas distintivas
- **Registros Manuales**: Van directamente a "Tareas Completadas" con estado COMPLETED
- **Flujo Kanban**: Mantiene su flujo normal hasta ARCHIVED

## Solución de Problemas

### Si la migración falla:
1. Verifica que Docker esté corriendo
2. Confirma el nombre del contenedor de PostgreSQL
3. Verifica las credenciales de la base de datos
4. Aplica el SQL manualmente conectándote a la base de datos

### Si hay errores en el frontend:
1. Limpia la caché del navegador
2. Reinicia el servidor de desarrollo
3. Verifica que no hay errores en la consola del navegador

### Si las tareas no aparecen correctamente:
1. Verifica que la migración se aplicó correctamente
2. Revisa los logs del servidor backend
3. Confirma que el campo `source` tiene valores correctos en la base de datos
