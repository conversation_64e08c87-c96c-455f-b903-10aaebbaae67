const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const socketService = require('../services/socketService');

// Iniciar o reanudar un temporizador para una tarea
exports.startTimer = async (req, res) => {
  try {
    const { taskId } = req.body;
    const userId = req.user.id;

    if (!taskId) {
      return res.status(400).json({ error: 'El ID de la tarea es requerido' });
    }

    // Verificar que la tarea existe
    const task = await prisma.task.findUnique({
      where: { id: taskId }
    });

    if (!task) {
      return res.status(404).json({ error: 'Tarea no encontrada' });
    }

    // Verificar si ya existe un temporizador activo para esta tarea y usuario
    let activeTimer = await prisma.activeTimer.findFirst({
      where: {
        taskId,
        userId
      }
    });

    const now = new Date();

    if (activeTimer) {
      // Si el temporizador existe pero no está corriendo, lo reanudamos
      if (!activeTimer.isRunning) {
        activeTimer = await prisma.activeTimer.update({
          where: { id: activeTimer.id },
          data: {
            isRunning: true,
            lastStarted: now
          }
        });
        return res.json({
          message: 'Temporizador reanudado',
          timer: activeTimer
        });
      } else {
        // Si ya está corriendo, simplemente devolvemos el temporizador actual
        return res.json({
          message: 'El temporizador ya está activo',
          timer: activeTimer
        });
      }
    }

    // Si no existe un temporizador, creamos uno nuevo
    activeTimer = await prisma.activeTimer.create({
      data: {
        taskId,
        userId,
        startTime: now,
        lastStarted: now,
        isRunning: true,
        accumulatedTime: 0
      }
    });

    // Calculate current accumulated time if timer was running
    if (activeTimer && activeTimer.isRunning) {
      const now = new Date();
      const elapsedSeconds = Math.floor((now - activeTimer.lastStarted) / 1000);
      activeTimer.accumulatedTime += elapsedSeconds;
    }

    // Notify all clients about timer start with accurate time
    socketService.notifyTimerUpdate(taskId, {
      action: 'start',
      isRunning: true,
      accumulatedTime: activeTimer.accumulatedTime,
      lastStarted: new Date()
    });

    res.status(201).json({
      message: 'Temporizador iniciado',
      timer: activeTimer
    });
  } catch (error) {
    console.error('Error al iniciar temporizador:', error);
    res.status(500).json({
      error: 'Error al iniciar el temporizador',
      details: error.message
    });
  }
};

// Pausar un temporizador activo
exports.pauseTimer = async (req, res) => {
  try {
    const { taskId } = req.body;
    const userId = req.user.id;

    if (!taskId) {
      return res.status(400).json({ error: 'El ID de la tarea es requerido' });
    }

    // Buscar el temporizador activo
    const activeTimer = await prisma.activeTimer.findFirst({
      where: {
        taskId,
        userId,
        isRunning: true
      }
    });

    if (!activeTimer) {
      return res.status(404).json({ error: 'No hay un temporizador activo para esta tarea' });
    }

    const now = new Date();
    const elapsedSeconds = Math.floor((now - activeTimer.lastStarted) / 1000);
    const newAccumulatedTime = activeTimer.accumulatedTime + elapsedSeconds;

    // Actualizar el temporizador
    const updatedTimer = await prisma.activeTimer.update({
      where: { id: activeTimer.id },
      data: {
        isRunning: false,
        accumulatedTime: newAccumulatedTime
      }
    });

    // Notify all clients about timer pause
    socketService.notifyTimerUpdate(taskId, {
      action: 'pause',
      timer: updatedTimer
    });

    res.json({
      message: 'Temporizador pausado',
      timer: updatedTimer
    });
  } catch (error) {
    console.error('Error al pausar temporizador:', error);
    res.status(500).json({
      error: 'Error al pausar el temporizador',
      details: error.message
    });
  }
};

// Detener un temporizador y crear una entrada de tiempo
exports.stopTimer = async (req, res) => {
  try {
    const { taskId, description } = req.body;
    const userId = req.user.id;

    if (!taskId) {
      return res.status(400).json({ error: 'El ID de la tarea es requerido' });
    }

    // Buscar el temporizador
    const activeTimer = await prisma.activeTimer.findFirst({
      where: {
        taskId,
        userId
      }
    });

    if (!activeTimer) {
      return res.status(404).json({ error: 'No hay un temporizador para esta tarea' });
    }

    // Calcular el tiempo total
    let totalSeconds = activeTimer.accumulatedTime;
    if (activeTimer.isRunning) {
      const now = new Date();
      const elapsedSeconds = Math.floor((now - activeTimer.lastStarted) / 1000);
      totalSeconds += elapsedSeconds;
    }

    // Convertir segundos a horas con dos decimales
    const hoursWorked = parseFloat((totalSeconds / 3600).toFixed(2));

    // Obtener la tarea para el título
    const task = await prisma.task.findUnique({
      where: { id: taskId }
    });

    // Crear una entrada de tiempo
    const startTime = activeTimer.startTime;
    const endTime = new Date();

    const timeEntry = await prisma.timeEntry.create({
      data: {
        title: `Trabajo en: ${task.title}`,
        description: description || `Tiempo registrado automáticamente para la tarea: ${task.title}`,
        startTime,
        endTime,
        hoursWorked,
        status: 'PENDING',
        type: 'REGULAR',
        userId,
        taskId
      },
      include: {
        user: true,
        task: true
      }
    });

    // Eliminar el temporizador activo
    await prisma.activeTimer.delete({
      where: { id: activeTimer.id }
    });

    res.json({
      message: 'Temporizador detenido y tiempo registrado',
      timeEntry
    });
  } catch (error) {
    console.error('Error al detener temporizador:', error);
    res.status(500).json({
      error: 'Error al detener el temporizador',
      details: error.message
    });
  }
};

// Obtener el temporizador activo del usuario para una tarea específica
exports.getTaskTimer = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    const activeTimer = await prisma.activeTimer.findFirst({
      where: {
        taskId,
        userId
      }
    });

    if (!activeTimer) {
      return res.json(null);
    }

    // Si el temporizador está corriendo, calculamos el tiempo acumulado hasta ahora
    let currentAccumulatedTime = activeTimer.accumulatedTime;
    if (activeTimer.isRunning) {
      const now = new Date();
      const elapsedSeconds = Math.floor((now - activeTimer.lastStarted) / 1000);
      currentAccumulatedTime += elapsedSeconds;
    }

    res.json({
      ...activeTimer,
      currentAccumulatedTime
    });
  } catch (error) {
    console.error('Error al obtener temporizador de tarea:', error);
    res.status(500).json({
      error: 'Error al obtener el temporizador',
      details: error.message
    });
  }
};

// Obtener todos los temporizadores activos del usuario
exports.getUserTimers = async (req, res) => {
  try {
    const userId = req.user.id;

    const activeTimers = await prisma.activeTimer.findMany({
      where: {
        userId
      },
      include: {
        task: true
      }
    });

    // Calcular el tiempo acumulado actual para cada temporizador
    const timersWithCurrentTime = activeTimers.map(timer => {
      let currentAccumulatedTime = timer.accumulatedTime;
      if (timer.isRunning) {
        const now = new Date();
        const elapsedSeconds = Math.floor((now - timer.lastStarted) / 1000);
        currentAccumulatedTime += elapsedSeconds;
      }

      return {
        ...timer,
        currentAccumulatedTime
      };
    });

    res.json(timersWithCurrentTime);
  } catch (error) {
    console.error('Error al obtener temporizadores del usuario:', error);
    res.status(500).json({
      error: 'Error al obtener los temporizadores',
      details: error.message
    });
  }
};