import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  But<PERSON>,
  Stepper,
  Step,
  StepLabel,
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Paper,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { API_URL } from '../config';

const steps = [
  'Información Básica',
  'Configurar Endpoints',
  'Mapeo de Campos',
  'Probar Conexión'
];

const ApiBuilderDialog = ({ open, integration, onClose, onSave }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [testing, setTesting] = useState(false);

  // Estado del formulario
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    baseUrl: '',
    authType: 'api_key',
    authConfig: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    endpoints: [],
    isActive: true,
    skipSslVerification: false
  });

  const [currentEndpoint, setCurrentEndpoint] = useState({
    name: '',
    method: 'GET',
    path: '',
    targetMenu: 'kanban',
    queryParams: {},
    bodyParams: {},
    fieldMapping: {
      title: 'subject',
      description: 'description',
      priority: 'priority.name',
      status: 'status.name',
      externalId: 'id',
      externalUrl: 'display_id'
    },
    statusMapping: {
      'Open': 'TODO',
      'In Progress': 'IN_PROGRESS',
      'Pending': 'TODO',
      'Resolved': 'REVIEW',
      'Closed': 'DONE'
    },
    priorityMapping: {
      'Low': 'LOW',
      'Medium': 'MEDIUM',
      'High': 'HIGH',
      'Urgent': 'URGENT'
    }
  });

  const [testApiKey, setTestApiKey] = useState('');

  useEffect(() => {
    if (integration) {
      setFormData({
        name: integration.name || '',
        description: integration.description || '',
        baseUrl: integration.baseUrl || '',
        authType: integration.authType || 'api_key',
        authConfig: integration.authConfig || {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        endpoints: integration.endpoints || [],
        isActive: integration.isActive !== undefined ? integration.isActive : true,
        skipSslVerification: integration.skipSslVerification || false
      });
    } else {
      // Reset para nueva integración
      setFormData({
        name: '',
        description: '',
        baseUrl: '',
        authType: 'api_key',
        authConfig: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        endpoints: [],
        isActive: true,
        skipSslVerification: false
      });
    }
    setActiveStep(0);
    setTestResult(null);
  }, [integration, open]);

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAuthConfigChange = (key, value) => {
    setFormData(prev => ({
      ...prev,
      authConfig: {
        ...prev.authConfig,
        [key]: value
      }
    }));
  };

  const handleEndpointChange = (field, value) => {
    setCurrentEndpoint(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addEndpoint = () => {
    if (currentEndpoint.name && currentEndpoint.path) {
      setFormData(prev => ({
        ...prev,
        endpoints: [...prev.endpoints, { ...currentEndpoint }]
      }));
      
      // Reset current endpoint
      setCurrentEndpoint({
        name: '',
        method: 'GET',
        path: '',
        targetMenu: 'kanban',
        queryParams: {},
        bodyParams: {},
        fieldMapping: {
          title: 'subject',
          description: 'description',
          priority: 'priority.name',
          status: 'status.name',
          externalId: 'id',
          externalUrl: 'display_id'
        },
        statusMapping: {
          'Open': 'TODO',
          'In Progress': 'IN_PROGRESS',
          'Pending': 'TODO',
          'Resolved': 'REVIEW',
          'Closed': 'DONE'
        },
        priorityMapping: {
          'Low': 'LOW',
          'Medium': 'MEDIUM',
          'High': 'HIGH',
          'Urgent': 'URGENT'
        }
      });
    }
  };

  const removeEndpoint = (index) => {
    setFormData(prev => ({
      ...prev,
      endpoints: prev.endpoints.filter((_, i) => i !== index)
    }));
  };

  const testConnection = async () => {
    if (!testApiKey) {
      alert('Por favor ingrese una API key de prueba');
      return;
    }

    if (formData.endpoints.length === 0) {
      alert('Por favor configure al menos un endpoint');
      return;
    }

    setTesting(true);
    setTestResult(null);

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/integrations/test-connection`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          baseUrl: formData.baseUrl,
          authType: formData.authType,
          authConfig: formData.authConfig,
          endpoint: formData.endpoints[0], // Usar el primer endpoint para la prueba
          testApiKey
        })
      });

      const result = await response.json();
      setTestResult(result);

    } catch (error) {
      console.error('Error al probar conexión:', error);
      setTestResult({
        success: false,
        message: 'Error al conectar con el servidor',
        error: error.message
      });
    } finally {
      setTesting(false);
    }
  };

  const saveIntegration = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const url = integration 
        ? `${API_URL}/api/integrations/${integration.id}`
        : `${API_URL}/api/integrations`;
      
      const method = integration ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        onSave();
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Error al guardar integración');
      }

    } catch (error) {
      console.error('Error al guardar integración:', error);
      alert(`Error al guardar: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Box sx={{ mt: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Nombre de la Integración"
                  placeholder="ManageEngine ServiceDesk Plus"
                  value={formData.name}
                  onChange={(e) => handleFormChange('name', e.target.value)}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Descripción"
                  placeholder="Integración con sistema de tickets corporativo"
                  value={formData.description}
                  onChange={(e) => handleFormChange('description', e.target.value)}
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="URL Base"
                  placeholder="https://company.manageengine.com/api/v3"
                  value={formData.baseUrl}
                  onChange={(e) => handleFormChange('baseUrl', e.target.value)}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Tipo de Autenticación</InputLabel>
                  <Select
                    value={formData.authType}
                    onChange={(e) => handleFormChange('authType', e.target.value)}
                  >
                    <MenuItem value="api_key">API Key</MenuItem>
                    <MenuItem value="bearer_token">Bearer Token</MenuItem>
                    <MenuItem value="basic_auth">Basic Auth</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Headers Adicionales (JSON)"
                  multiline
                  rows={4}
                  value={JSON.stringify(formData.authConfig, null, 2)}
                  onChange={(e) => {
                    try {
                      const parsed = JSON.parse(e.target.value);
                      handleFormChange('authConfig', parsed);
                    } catch (error) {
                      // Ignorar errores de parsing mientras se escribe
                    }
                  }}
                  helperText="Headers que se enviarán con cada petición (formato JSON)"
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formData.skipSslVerification}
                      onChange={(e) => handleFormChange('skipSslVerification', e.target.checked)}
                    />
                  }
                  label="Desactivar verificación SSL (para certificados autofirmados)"
                />
                <Typography variant="caption" color="textSecondary" display="block">
                  Activa esta opción si tu API usa HTTPS con certificados autofirmados o no válidos
                </Typography>
              </Grid>
            </Grid>
          </Box>
        );

      case 1:
        return (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>Configurar Endpoints</Typography>
            
            {/* Formulario para nuevo endpoint */}
            <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
              <Typography variant="subtitle1" gutterBottom>Agregar Nuevo Endpoint</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Nombre del Endpoint"
                    placeholder="Obtener Tickets Asignados"
                    value={currentEndpoint.name}
                    onChange={(e) => handleEndpointChange('name', e.target.value)}
                  />
                </Grid>
                <Grid item xs={6} sm={3}>
                  <FormControl fullWidth>
                    <InputLabel>Método</InputLabel>
                    <Select
                      value={currentEndpoint.method}
                      onChange={(e) => handleEndpointChange('method', e.target.value)}
                    >
                      <MenuItem value="GET">GET</MenuItem>
                      <MenuItem value="POST">POST</MenuItem>
                      <MenuItem value="PUT">PUT</MenuItem>
                      <MenuItem value="DELETE">DELETE</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <FormControl fullWidth>
                    <InputLabel>Menú Destino</InputLabel>
                    <Select
                      value={currentEndpoint.targetMenu}
                      onChange={(e) => handleEndpointChange('targetMenu', e.target.value)}
                    >
                      <MenuItem value="kanban">Kanban</MenuItem>
                      <MenuItem value="time-tracking">Registro de Tiempo</MenuItem>
                      <MenuItem value="permissions">Permisos</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Ruta del Endpoint"
                    placeholder="/requests"
                    value={currentEndpoint.path}
                    onChange={(e) => handleEndpointChange('path', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Parámetros de Query (JSON)"
                    multiline
                    rows={6}
                    placeholder='{"input_data": "{\\"list_info\\":{\\"row_count\\":50}}"}'
                    value={JSON.stringify(currentEndpoint.queryParams, null, 2)}
                    onChange={(e) => {
                      try {
                        const parsed = JSON.parse(e.target.value);
                        handleEndpointChange('queryParams', parsed);
                      } catch (error) {
                        // Ignorar errores de parsing mientras se escribe
                      }
                    }}
                    helperText="Parámetros que se enviarán como query string. Usa {{USER_EMAIL}}, {{USER_NAME}} para variables dinámicas."
                  />
                </Grid>
                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={addEndpoint}
                    disabled={!currentEndpoint.name || !currentEndpoint.path}
                  >
                    Agregar Endpoint
                  </Button>
                </Grid>
              </Grid>
            </Paper>

            {/* Lista de endpoints configurados */}
            {formData.endpoints.length > 0 && (
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Nombre</TableCell>
                      <TableCell>Método</TableCell>
                      <TableCell>Ruta</TableCell>
                      <TableCell>Menú</TableCell>
                      <TableCell>Acciones</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {formData.endpoints.map((endpoint, index) => (
                      <TableRow key={index}>
                        <TableCell>{endpoint.name}</TableCell>
                        <TableCell>
                          <Chip label={endpoint.method} size="small" />
                        </TableCell>
                        <TableCell>{endpoint.path}</TableCell>
                        <TableCell>
                          <Chip label={endpoint.targetMenu} size="small" color="primary" />
                        </TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            onClick={() => removeEndpoint(index)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Box>
        );

      case 2:
        return (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>Mapeo de Campos</Typography>
            <Alert severity="info" sx={{ mb: 3 }}>
              Configure cómo mapear los campos de la respuesta API a los campos del sistema.
              Use notación de punto para campos anidados (ej: "priority.name").
            </Alert>
            
            <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
              Esta configuración se aplicará a todos los endpoints. Puede personalizar mapeos específicos más adelante.
            </Typography>
            
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>Mapeo Básico de Campos</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Campo Título en API"
                    placeholder="subject"
                    helperText="Ruta al campo título en la respuesta"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Campo Descripción en API"
                    placeholder="description"
                    helperText="Ruta al campo descripción"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Campo Estado en API"
                    placeholder="status.name"
                    helperText="Ruta al campo estado"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Campo Prioridad en API"
                    placeholder="priority.name"
                    helperText="Ruta al campo prioridad"
                  />
                </Grid>
              </Grid>
            </Paper>
          </Box>
        );

      case 3:
        return (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>Probar Integración</Typography>
            
            <Alert severity="info" sx={{ mb: 3 }}>
              Ingrese una API key de prueba para verificar que la configuración funciona correctamente.
              Esta API key no se guardará, solo se usa para la prueba.
            </Alert>
            
            <TextField
              fullWidth
              label="API Key de Prueba"
              type="password"
              value={testApiKey}
              onChange={(e) => setTestApiKey(e.target.value)}
              sx={{ mb: 3 }}
            />
            
            <Button
              variant="contained"
              onClick={testConnection}
              disabled={testing || !testApiKey || formData.endpoints.length === 0}
              sx={{ mb: 3 }}
            >
              {testing ? <CircularProgress size={20} /> : 'Probar Conexión'}
            </Button>
            
            {testResult && (
              <Paper sx={{ 
                p: 2, 
                bgcolor: testResult.success ? 'success.light' : 'error.light',
                color: testResult.success ? 'success.contrastText' : 'error.contrastText'
              }}>
                <Typography variant="h6">
                  {testResult.success ? '✅ Conexión Exitosa' : '❌ Error de Conexión'}
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {testResult.message}
                </Typography>
                
                {testResult.success && testResult.data && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2">Datos de Muestra:</Typography>
                    <Box sx={{ 
                      bgcolor: 'background.paper', 
                      p: 1, 
                      borderRadius: 1, 
                      mt: 1,
                      maxHeight: 200,
                      overflow: 'auto'
                    }}>
                      <pre style={{ fontSize: '12px', margin: 0, color: 'black' }}>
                        {JSON.stringify(testResult.data, null, 2)}
                      </pre>
                    </Box>
                  </Box>
                )}
              </Paper>
            )}
          </Box>
        );

      default:
        return 'Paso desconocido';
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        {integration ? 'Editar Integración API' : 'Nueva Integración API'}
      </DialogTitle>
      
      <DialogContent>
        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {renderStepContent(activeStep)}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Cancelar</Button>
        <Button 
          onClick={handleBack} 
          disabled={activeStep === 0}
        >
          Anterior
        </Button>
        <Button 
          onClick={handleNext} 
          disabled={activeStep === steps.length - 1}
        >
          Siguiente
        </Button>
        <Button 
          variant="contained" 
          onClick={saveIntegration}
          disabled={activeStep !== steps.length - 1 || !testResult?.success || loading}
        >
          {loading ? <CircularProgress size={20} /> : (integration ? 'Actualizar' : 'Crear')} Integración
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ApiBuilderDialog;
