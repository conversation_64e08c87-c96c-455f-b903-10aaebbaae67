import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Container,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Chip,
  Avatar,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Switch,
  FormControlLabel,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format } from 'date-fns';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import axios from 'axios';
import { API_URL } from '../config';
import GlobalTimeTracker from '../components/GlobalTimeTracker';
import { socket } from '../services/socket';
import globalTimerService from '../services/globalTimerService';
import DynamicFieldRenderer from '../components/DynamicFieldRenderer';
import { useDynamicFields } from '../hooks/useDynamicFields';

const COLUMN_ORDER = ['TODO', 'IN_PROGRESS', 'REVIEW', 'DONE'];

const COLUMN_TITLES = {
  'TODO': 'Por Hacer',
  'IN_PROGRESS': 'En Progreso',
  'REVIEW': 'Revisión',
  'DONE': 'Completado'
};

const INITIAL_COLUMNS = {
  'TODO': {
    title: COLUMN_TITLES.TODO,
    tasks: []
  },
  'IN_PROGRESS': {
    title: COLUMN_TITLES.IN_PROGRESS,
    tasks: []
  },
  'REVIEW': {
    title: COLUMN_TITLES.REVIEW,
    tasks: []
  },
  'DONE': {
    title: COLUMN_TITLES.DONE,
    tasks: []
  }
};

const KanbanBoard = () => {
  const [columns, setColumns] = useState(INITIAL_COLUMNS);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [user, setUser] = useState(null);
  const [taskForm, setTaskForm] = useState({
    title: '',
    description: '',
    assigneeId: localStorage.getItem('userId'),
    estimatedHours: '',
    dueDate: null,
    priority: ''
  });
  // Add state for filtering tasks
  const [showOnlyMyTasks, setShowOnlyMyTasks] = useState(false);
  const [originalColumns, setOriginalColumns] = useState(null);

  // Estado para importación de APIs
  const [importing, setImporting] = useState(false);
  const [availableIntegrations, setAvailableIntegrations] = useState([]);

  // Hook para campos dinámicos
  const {
    fields: dynamicFields,
    values: dynamicValues,
    errors: dynamicErrors,
    updateValue: updateDynamicValue,
    validateFields: validateDynamicFields,
    resetValues: resetDynamicValues
  } = useDynamicFields('kanban');

  useEffect(() => {
    fetchBoard();
    fetchUserInfo();
    // checkAvailableIntegrations(); // TEMPORALMENTE DESHABILITADO

    // Listen for task movements from other clients
    socket.on('taskMoved', ({ task }) => {
      console.log('Received task movement update:', task);
      fetchBoard(); // Refresh the board when a task is moved
    });

    // Cleanup socket listener
    return () => {
      socket.off('taskMoved');
    };
  }, []);

  // Add effect to filter tasks when showOnlyMyTasks changes
  useEffect(() => {
    if (originalColumns) {
      filterTasks();
    }
  }, [showOnlyMyTasks, originalColumns]);

  // Add periodic sync for all global timers
  useEffect(() => {
    const syncGlobalTimers = () => {
      Object.values(columns).forEach(column => {
        column.tasks.forEach(task => {
          if (task.globalTimer && task.globalTimer.isRunning) {
            // Emit global timer sync event
            socket.emit('syncGlobalTimer', task.id);
          }
        });
      });
    };

    const syncInterval = setInterval(syncGlobalTimers, 30000); // Sync every 30 seconds
    return () => clearInterval(syncInterval);
  }, [columns]);

  // Listen for global timer updates
  useEffect(() => {
    socket.on('globalTimerUpdate', (data) => {
      console.log('Received global timer update:', data);
      fetchBoard(); // Refresh the board when global timer updates
    });

    return () => {
      socket.off('globalTimerUpdate');
    };
  }, []);

  const fetchUserInfo = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await axios.get(`${API_URL}/api/auth/profile`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      setUser(response.data);
    } catch (error) {
      console.error('Error al obtener información del usuario:', error);
    }
  };

  // Verificar integraciones disponibles para el usuario - TEMPORALMENTE DESHABILITADO
  /*
  const checkAvailableIntegrations = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await axios.get(`${API_URL}/api/integrations/available?targetMenu=kanban`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      setAvailableIntegrations(response.data);
    } catch (error) {
      console.error('Error al verificar integraciones disponibles:', error);
    }
  };
  */

  // Importar tickets del usuario desde APIs externas - TEMPORALMENTE DESHABILITADO
  /*
  const handleImportMyTickets = async () => {
    setImporting(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/api/integrations/import-user-data`, {
        targetMenu: 'kanban'
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.data.success) {
        // Mostrar mensaje de éxito
        setError(null);
        alert(`${response.data.tasksCreated} tickets importados exitosamente`);

        // Refrescar el tablero
        await fetchBoard();
      } else {
        throw new Error(response.data.message || 'Error al importar tickets');
      }
    } catch (error) {
      console.error('Error al importar tickets:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Error al importar tickets';
      setError(`Error al importar tickets: ${errorMessage}`);
    } finally {
      setImporting(false);
    }
  };
  */

  const fetchBoard = async () => {
    try {
      console.log('Obteniendo tablero Kanban...');
      setLoading(true);
      
      const response = await axios.get(`${API_URL}/api/kanban/board`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      console.log('Respuesta del servidor:', response.data);

      const updatedColumns = { ...INITIAL_COLUMNS };
      COLUMN_ORDER.forEach(columnId => {
        const tasks = (response.data[columnId]?.tasks || []).map(task => ({
          ...task,
          id: String(task.id)
        }));
        
        updatedColumns[columnId] = {
          ...INITIAL_COLUMNS[columnId],
          tasks
        };
      });

      console.log('Columnas actualizadas:', updatedColumns);
      setOriginalColumns(JSON.parse(JSON.stringify(updatedColumns))); // Store a deep copy
      
      // Apply filter if needed
      if (showOnlyMyTasks) {
        const filteredColumns = filterTasksByUser(updatedColumns);
        setColumns(filteredColumns);
      } else {
        setColumns(updatedColumns);
      }
      
      setError(null);
    } catch (err) {
      console.error('Error al obtener el tablero:', err);
      setError('Error al cargar el tablero');
    } finally {
      setLoading(false);
    }
  };

  // Add function to filter tasks by current user
  const filterTasks = () => {
    if (!originalColumns) return;
    
    if (showOnlyMyTasks) {
      const filteredColumns = filterTasksByUser(originalColumns);
      setColumns(filteredColumns);
    } else {
      setColumns(JSON.parse(JSON.stringify(originalColumns))); // Use deep copy
    }
  };

  const filterTasksByUser = (columnsToFilter) => {
    const userId = localStorage.getItem('userId');
    const filteredColumns = { ...columnsToFilter };
    
    COLUMN_ORDER.forEach(columnId => {
      filteredColumns[columnId] = {
        ...filteredColumns[columnId],
        tasks: filteredColumns[columnId].tasks.filter(task => 
          task.assigneeId === userId
        )
      };
    });
    
    return filteredColumns;
  };

  const moveTaskBack = async (task, currentColumnIndex) => {
    if (currentColumnIndex <= 0) return; // Ya está en la primera columna
    
    const sourceColumnId = COLUMN_ORDER[currentColumnIndex];
    const destColumnId = COLUMN_ORDER[currentColumnIndex - 1];
    
    await moveTask(task, sourceColumnId, destColumnId);
  };
  
  const moveTaskForward = async (task, currentColumnIndex) => {
    if (currentColumnIndex >= COLUMN_ORDER.length - 1) return; // Ya está en la última columna
    
    const sourceColumnId = COLUMN_ORDER[currentColumnIndex];
    const destColumnId = COLUMN_ORDER[currentColumnIndex + 1];
    
    await moveTask(task, sourceColumnId, destColumnId);
  };
  
  // In the moveTask function, add code to start the timer when a task is moved to IN_PROGRESS
  const moveTask = async (task, sourceColumnId, destColumnId) => {
    try {
      // Check if user is trying to move a task to IN_PROGRESS
      if (destColumnId === 'IN_PROGRESS') {
        // Check if user already has a task in progress
        const hasTaskInProgress = columns['IN_PROGRESS'].tasks.some(
          t => t.assigneeId === localStorage.getItem('userId')
        );

        if (hasTaskInProgress) {
          setError('No puedes tener más de una tarea en progreso al mismo tiempo');
          return;
        }
      }

      console.log(`Moviendo tarea ${task.id} de ${sourceColumnId} a ${destColumnId}`);
      
      // Actualizar el estado local primero para UI responsiva
      const sourceColumn = columns[sourceColumnId];
      const destColumn = columns[destColumnId];
      
      if (!sourceColumn || !destColumn) {
        console.error('Columna no encontrada:', { 
          sourceColumnId, 
          destColumnId,
          columnas: Object.keys(columns)
        });
        return;
      }
      
      const sourceTasks = [...sourceColumn.tasks];
      const destTasks = [...destColumn.tasks];
      
      // Encontrar la tarea que se está moviendo
      const taskIndex = sourceTasks.findIndex(t => String(t.id) === String(task.id));
      
      if (taskIndex === -1) {
        console.error('No se encontró la tarea con ID:', task.id);
        return;
      }
      
      // Crear una copia de la tarea con el nuevo estado
      const movedTask = { ...sourceTasks[taskIndex], status: destColumnId };
      
      // Eliminar de la columna origen y añadir a la columna destino
      sourceTasks.splice(taskIndex, 1);
      destTasks.push(movedTask);
      
      // Actualizar el estado
      const updatedColumns = {
        ...columns,
        [sourceColumnId]: {
          ...sourceColumn,
          tasks: sourceTasks
        },
        [destColumnId]: {
          ...destColumn,
          tasks: destTasks
        }
      };
      
      setColumns(updatedColumns);
      
      // Actualizar en el backend
      const response = await axios.patch(
        `${API_URL}/api/kanban/tasks/${task.id}/status`,
        { status: destColumnId },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );
  
      console.log('Respuesta del servidor:', response.data);
  
      // Start global timer automatically when task is moved to IN_PROGRESS
      if (destColumnId === 'IN_PROGRESS' && sourceColumnId !== 'IN_PROGRESS') {
        try {
          await globalTimerService.startTimer(task.id);
          console.log('Global timer started automatically for task:', task.id);
        } catch (timerError) {
          console.error('Error starting global timer automatically:', timerError);
          // No mostrar error al usuario, ya que la tarea se movió correctamente
        }
      }

      // Pause global timer when task is moved out of IN_PROGRESS
      if (sourceColumnId === 'IN_PROGRESS' && destColumnId !== 'IN_PROGRESS') {
        try {
          await globalTimerService.pauseTimer(task.id);
          console.log('Global timer paused automatically for task:', task.id);
        } catch (timerError) {
          console.error('Error pausing global timer automatically:', timerError);
          // No mostrar error al usuario, ya que la tarea se movió correctamente
        }
      }
      
    } catch (error) {
      console.error('Error al mover la tarea:', error);
      fetchBoard(); // Recargar en caso de error
    }
  };

  const handleDebugClick = () => {
    console.log('Estado actual de columnas:', columns);
    console.log('Columnas disponibles:', COLUMN_ORDER);
  };

  const handleOpenDialog = (task) => {
    if (task) {
      setSelectedTask(task);
      setTaskForm({
        title: task.title,
        description: task.description,
        assigneeId: task.assigneeId,
        estimatedHours: task.estimatedHours,
        dueDate: task.dueDate ? new Date(task.dueDate) : null,
        priority: task.priority || ''
      });
    }
    setOpenDialog(true);
  };

  const handleDeleteTask = async (taskId) => {
    if (!window.confirm('¿Estás seguro de que deseas eliminar esta tarea?')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/api/kanban/tasks/${taskId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      const updatedColumns = { ...columns };
      
      for (const columnId of COLUMN_ORDER) {
        const taskIndex = updatedColumns[columnId].tasks.findIndex(t => String(t.id) === String(taskId));
        if (taskIndex !== -1) {
          updatedColumns[columnId].tasks.splice(taskIndex, 1);
          break;
        }
      }
      
      setColumns(updatedColumns);
    } catch (error) {
      console.error('Error al eliminar la tarea:', error);
      setError('Error al eliminar la tarea');
    }
  };

  const handleSaveTask = async () => {
    try {
      // Validar campos dinámicos
      const dynamicValidationErrors = validateDynamicFields();
      if (Object.keys(dynamicValidationErrors).length > 0) {
        setError('Por favor complete todos los campos requeridos');
        return;
      }

      const token = localStorage.getItem('token');
      if (!token) {
        setError('No hay token de autenticación');
        return;
      }

      const taskData = {
        ...taskForm,
        status: selectedTask ? selectedTask.status : 'TODO',
        creatorId: localStorage.getItem('userId'),
        assigneeId: taskForm.assigneeId || localStorage.getItem('userId'),
        dynamicFields: dynamicValues // Agregar campos dinámicos
      };

      console.log('Enviando datos de tarea:', taskData);

      let response;
      if (selectedTask) {
        response = await axios.put(
          `${API_URL}/api/kanban/tasks/${String(selectedTask.id)}`,
          taskData,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );
      } else {
        response = await axios.post(
          `${API_URL}/api/kanban/tasks`,
          taskData,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );
      }

      if (response.data) {
        fetchBoard();
        setOpenDialog(false);
        resetDynamicValues(); // Limpiar campos dinámicos
      }
    } catch (error) {
      console.error('Error al guardar la tarea:', error);
      setError(error.response?.data?.message || 'Error al guardar la tarea');
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="xl">
        <Box sx={{ py: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h4">Tablero Kanban</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {/* Add filter toggle switch */}
              <FormControlLabel
                control={
                  <Switch
                    checked={showOnlyMyTasks}
                    onChange={(e) => setShowOnlyMyTasks(e.target.checked)}
                    color="primary"
                  />
                }
                label="Solo mis tareas"
                sx={{ mr: 2 }}
              />
              <Button 
                variant="outlined" 
                color="secondary" 
                onClick={handleDebugClick}
                sx={{ mr: 2 }}
              >
                Debug
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => {
                  setSelectedTask(null);
                  setTaskForm({
                    title: '',
                    description: '',
                    assigneeId: localStorage.getItem('userId'),
                    estimatedHours: '',
                    dueDate: null,
                    priority: ''
                  });
                  resetDynamicValues(); // Limpiar campos dinámicos
                  setOpenDialog(true);
                }}
                sx={{ mr: 2 }}
              >
                Nueva Tarea
              </Button>

              {/* Botón para importar tickets desde APIs externas - TEMPORALMENTE DESHABILITADO */}
              {/* {availableIntegrations.length > 0 && (
                <Button
                  variant="outlined"
                  startIcon={importing ? <CircularProgress size={16} /> : <CloudDownloadIcon />}
                  onClick={handleImportMyTickets}
                  disabled={importing}
                  color="primary"
                >
                  {importing ? 'Importando...' : 'Mis Tickets'}
                </Button>
              )} */}
            </Box>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          )}

          {!loading && (
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              {COLUMN_ORDER.map((columnId, columnIndex) => {
                if (!columns[columnId]) {
                  console.error(`Columna ${columnId} no encontrada`);
                  return null;
                }
                
                console.log(`Renderizando columna ${columnId} con ${columns[columnId].tasks.length} tareas`);
                
                return (
                  <Box
                    key={columnId}
                    sx={{
                      bgcolor: 'background.paper',
                      p: 2,
                      borderRadius: 1,
                      boxShadow: 1,
                      width: { xs: '100%', sm: '45%', md: '30%', lg: '22%' },
                      minWidth: '250px',
                    }}
                  >
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      {columns[columnId].title}
                    </Typography>
                    <Box
                      sx={{
                        minHeight: '100px',
                        padding: 1
                      }}
                    >
                      {columns[columnId].tasks.map((task) => {
                        console.log(`Renderizando tarea ${task.id}`);
                        return (
                          <Card
                            key={task.id}
                            sx={{
                              mb: 1,
                              '&:hover': {
                                bgcolor: 'action.hover',
                              }
                            }}
                          >
                            <CardContent>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                <Typography variant="h6" component="div">
                                  {task.title}
                                </Typography>
                                <Box>
                                  <IconButton
                                    size="small"
                                    onClick={() => handleOpenDialog(task)}
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                  <IconButton
                                    size="small"
                                    onClick={() => handleDeleteTask(task.id)}
                                  >
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </Box>
                              </Box>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                gutterBottom
                              >
                                {task.description}
                              </Typography>
                              <Box
                                sx={{
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                  alignItems: 'center',
                                  mt: 2,
                                }}
                              >
                                <Chip
                                  avatar={task.assignee ? <Avatar>{task.assignee[0]}</Avatar> : null}
                                  label={task.assignee || 'Sin asignar'}
                                  size="small"
                                />
                                <Box>
                                  <Typography variant="caption" sx={{ mr: 1 }}>
                                    Est: {task.estimatedHours}h
                                  </Typography>
                                  {task.actualHours > 0 && (
                                    <Typography variant="caption" color="primary">
                                      Real: {task.actualHours.toFixed(5)}h
                                    </Typography>
                                  )}
                                </Box>
                              </Box>
                              {task.dueDate && (
                                <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                                  Vence: {format(new Date(task.dueDate), 'dd/MM/yyyy')}
                                </Typography>
                              )}
                              {columnId === 'IN_PROGRESS' && (
                                <Box sx={{ mt: 2 }}>
                                  <GlobalTimeTracker
                                    taskId={task.id}
                                    taskOwnerId={task.assigneeId}
                                    userRole={user?.role}
                                  />
                                </Box>
                              )}
                              
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                                <Button
                                  size="small"
                                  startIcon={<ArrowBackIcon />}
                                  disabled={columnIndex === 0}
                                  onClick={() => moveTaskBack(task, columnIndex)}
                                >
                                  Anterior
                                </Button>
                                <Button
                                  size="small"
                                  endIcon={<ArrowForwardIcon />}
                                  disabled={columnIndex === COLUMN_ORDER.length - 1}
                                  onClick={() => moveTaskForward(task, columnIndex)}
                                >
                                  Siguiente
                                </Button>
                              </Box>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </Box>
                  </Box>
                );
              })}
            </Box>
          )}

          <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
            <DialogTitle>
              {selectedTask ? 'Editar Tarea' : 'Nueva Tarea'}
            </DialogTitle>
            <DialogContent>
              <Box sx={{ mt: 2 }}>
                {/* Renderizar campos dinámicos ordenados */}
                {dynamicFields
                  .filter(field => field.isActive)
                  .sort((a, b) => a.order - b.order)
                  .map((field) => {
                    // Manejar campos especiales que necesitan lógica personalizada
                    if (field.name === 'dueDate') {
                      return (
                        <DatePicker
                          key={field.name}
                          label={field.label}
                          value={taskForm.dueDate ? new Date(taskForm.dueDate) : null}
                          onChange={(newValue) => setTaskForm({ ...taskForm, dueDate: newValue })}
                          slotProps={{
                            textField: {
                              fullWidth: true,
                              sx: { mb: 2 },
                              required: field.required,
                              error: !!dynamicErrors[field.name],
                              helperText: dynamicErrors[field.name]
                            }
                          }}
                        />
                      );
                    }

                    // Campos que usan taskForm en lugar de dynamicValues
                    if (['title', 'description', 'estimatedHours'].includes(field.name)) {
                      return (
                        <TextField
                          key={field.name}
                          fullWidth
                          label={field.label}
                          type={field.type === 'number' ? 'number' : 'text'}
                          multiline={field.type === 'textarea'}
                          rows={field.type === 'textarea' ? 4 : 1}
                          value={taskForm[field.name]}
                          onChange={(e) => setTaskForm({ ...taskForm, [field.name]: e.target.value })}
                          required={field.required}
                          placeholder={field.placeholder}
                          error={!!dynamicErrors[field.name]}
                          helperText={dynamicErrors[field.name]}
                          sx={{ mb: 2 }}
                        />
                      );
                    }

                    // Campo de prioridad como select
                    if (field.name === 'priority') {
                      const priorityOptions = field.options ? field.options.split(',').map(opt => opt.trim()) : [];
                      return (
                        <TextField
                          key={field.name}
                          select
                          fullWidth
                          label={field.label}
                          value={taskForm.priority || ''}
                          onChange={(e) => setTaskForm({ ...taskForm, priority: e.target.value })}
                          required={field.required}
                          error={!!dynamicErrors[field.name]}
                          helperText={dynamicErrors[field.name]}
                          sx={{ mb: 2 }}
                        >
                          <MenuItem value="">
                            <em>Seleccionar prioridad</em>
                          </MenuItem>
                          {priorityOptions.map((option) => (
                            <MenuItem key={option} value={option}>
                              {option}
                            </MenuItem>
                          ))}
                        </TextField>
                      );
                    }

                    // Campos dinámicos personalizados
                    return (
                      <Box key={field.name} sx={{ mb: 2 }}>
                        <DynamicFieldRenderer
                          field={field}
                          value={dynamicValues[field.name]}
                          onChange={updateDynamicValue}
                          error={dynamicErrors[field.name]}
                        />
                      </Box>
                    );
                  })}
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOpenDialog(false)}>
                Cancelar
              </Button>
              <Button
                variant="contained"
                onClick={handleSaveTask}
              >
                Guardar
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </Container>
    </LocalizationProvider>
  );
};

export default KanbanBoard;
