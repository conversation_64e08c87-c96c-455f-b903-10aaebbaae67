import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  IconButton,
  Chip,
  Alert,
  Snackbar
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIcon
} from '@mui/icons-material';
import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

const DynamicFieldsSettings = () => {
  const [fields, setFields] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingField, setEditingField] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  const [formData, setFormData] = useState({
    name: '',
    label: '',
    type: 'text',
    required: false,
    placeholder: '',
    options: '',
    defaultValue: '',
    order: 0,
    isActive: true,
    appliesTo: 'both'
  });

  const fieldTypes = [
    { value: 'text', label: 'Texto' },
    { value: 'textarea', label: 'Área de Texto' },
    { value: 'number', label: 'Número' },
    { value: 'date', label: 'Fecha' },
    { value: 'datetime-local', label: 'Fecha y Hora' },
    { value: 'select', label: 'Lista Desplegable' },
    { value: 'text-with-suggestions', label: 'Texto con Sugerencias' },
    { value: 'checkbox', label: 'Casilla de Verificación' }
  ];

  const appliesOptions = [
    { value: 'both', label: 'Ambos (Tiempo Manual y Tareas)' },
    { value: 'timeEntry', label: 'Solo Registro de Tiempo' },
    { value: 'task', label: 'Solo Tareas Kanban' }
  ];

  useEffect(() => {
    fetchFields();
  }, []);

  const fetchFields = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/dynamic-fields/all`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setFields(response.data);
    } catch (error) {
      console.error('Error al cargar campos:', error);
      showSnackbar('Error al cargar campos dinámicos', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleOpenDialog = (field = null) => {
    if (field) {
      setEditingField(field);
      setFormData({
        name: field.name,
        label: field.label,
        type: field.type,
        required: field.required,
        placeholder: field.placeholder || '',
        options: field.options || '',
        defaultValue: field.defaultValue || '',
        order: field.order,
        isActive: field.isActive,
        appliesTo: field.appliesTo
      });
    } else {
      setEditingField(null);
      setFormData({
        name: '',
        label: '',
        type: 'text',
        required: false,
        placeholder: '',
        options: '',
        defaultValue: '',
        order: fields.length,
        isActive: true,
        appliesTo: 'both'
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingField(null);
  };

  const handleSave = async () => {
    try {
      const token = localStorage.getItem('token');
      
      if (editingField) {
        await axios.put(`${API_URL}/api/dynamic-fields/${editingField.id}`, formData, {
          headers: { Authorization: `Bearer ${token}` }
        });
        showSnackbar('Campo actualizado correctamente');
      } else {
        await axios.post(`${API_URL}/api/dynamic-fields`, formData, {
          headers: { Authorization: `Bearer ${token}` }
        });
        showSnackbar('Campo creado correctamente');
      }
      
      fetchFields();
      handleCloseDialog();
    } catch (error) {
      console.error('Error al guardar campo:', error);
      showSnackbar(error.response?.data?.error || 'Error al guardar campo', 'error');
    }
  };

  const handleDelete = async (fieldId, fieldName) => {
    if (!window.confirm(`¿Está seguro de eliminar el campo "${fieldName}"?`)) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/dynamic-fields/${fieldId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      showSnackbar('Campo eliminado correctamente');
      fetchFields();
    } catch (error) {
      console.error('Error al eliminar campo:', error);
      showSnackbar(error.response?.data?.error || 'Error al eliminar campo', 'error');
    }
  };

  const getTypeLabel = (type) => {
    return fieldTypes.find(t => t.value === type)?.label || type;
  };

  const getAppliesLabel = (appliesTo) => {
    return appliesOptions.find(a => a.value === appliesTo)?.label || appliesTo;
  };

  if (loading) {
    return <Typography>Cargando campos dinámicos...</Typography>;
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Configuración de Campos Dinámicos</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Nuevo Campo
        </Button>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        Configure aquí los campos que aparecerán en los formularios de registro manual de tiempo y creación de tareas Kanban.
        Los campos marcados como "requeridos" serán obligatorios en los formularios.
      </Alert>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Orden</TableCell>
              <TableCell>Nombre</TableCell>
              <TableCell>Etiqueta</TableCell>
              <TableCell>Tipo</TableCell>
              <TableCell>Aplica a</TableCell>
              <TableCell>Requerido</TableCell>
              <TableCell>Estado</TableCell>
              <TableCell>Acciones</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {fields.map((field) => (
              <TableRow key={field.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <DragIcon sx={{ mr: 1, color: 'grey.400' }} />
                    {field.order}
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" fontFamily="monospace">
                    {field.name}
                  </Typography>
                </TableCell>
                <TableCell>{field.label}</TableCell>
                <TableCell>{getTypeLabel(field.type)}</TableCell>
                <TableCell>
                  <Chip 
                    label={getAppliesLabel(field.appliesTo)} 
                    size="small"
                    color={field.appliesTo === 'both' ? 'primary' : 'default'}
                  />
                </TableCell>
                <TableCell>
                  {field.required ? (
                    <Chip label="Sí" color="warning" size="small" />
                  ) : (
                    <Chip label="No" color="default" size="small" />
                  )}
                </TableCell>
                <TableCell>
                  {field.isActive ? (
                    <Chip label="Activo" color="success" size="small" />
                  ) : (
                    <Chip label="Inactivo" color="default" size="small" />
                  )}
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => handleOpenDialog(field)}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                  {!field.id.startsWith('default-') && (
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(field.id, field.label)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog para crear/editar campo */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingField ? 'Editar Campo' : 'Nuevo Campo'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              label="Nombre del Campo"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              disabled={editingField?.id?.startsWith('default-')}
              helperText="Nombre técnico del campo (sin espacios, solo letras y guiones)"
              fullWidth
            />
            
            <TextField
              label="Etiqueta"
              value={formData.label}
              onChange={(e) => setFormData({ ...formData, label: e.target.value })}
              helperText="Texto que aparecerá en el formulario"
              fullWidth
            />

            <FormControl fullWidth>
              <InputLabel>Tipo de Campo</InputLabel>
              <Select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                label="Tipo de Campo"
              >
                {fieldTypes.map((type) => (
                  <MenuItem key={type.value} value={type.value}>
                    {type.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Aplica a</InputLabel>
              <Select
                value={formData.appliesTo}
                onChange={(e) => setFormData({ ...formData, appliesTo: e.target.value })}
                label="Aplica a"
              >
                {appliesOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              label="Placeholder"
              value={formData.placeholder}
              onChange={(e) => setFormData({ ...formData, placeholder: e.target.value })}
              helperText="Texto de ayuda que aparece en el campo"
              fullWidth
            />

            {formData.type === 'select' && (
              <TextField
                label="Opciones"
                value={formData.options}
                onChange={(e) => setFormData({ ...formData, options: e.target.value })}
                helperText="Opciones separadas por comas (ej: Opción1,Opción2,Opción3)"
                fullWidth
                multiline
                rows={2}
              />
            )}

            <TextField
              label="Valor por Defecto"
              value={formData.defaultValue}
              onChange={(e) => setFormData({ ...formData, defaultValue: e.target.value })}
              fullWidth
            />

            <TextField
              label="Orden"
              type="number"
              value={formData.order}
              onChange={(e) => setFormData({ ...formData, order: parseInt(e.target.value) || 0 })}
              helperText="Orden de aparición en el formulario"
              fullWidth
            />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.required}
                  onChange={(e) => setFormData({ ...formData, required: e.target.checked })}
                />
              }
              label="Campo Requerido"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                />
              }
              label="Campo Activo"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancelar</Button>
          <Button onClick={handleSave} variant="contained">
            {editingField ? 'Actualizar' : 'Crear'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DynamicFieldsSettings;
