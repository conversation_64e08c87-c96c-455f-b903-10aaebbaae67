-- CreateTable
CREATE TABLE "DynamicField" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "label" TEXT NOT NULL,
    "type" TEXT NOT NULL DEFAULT 'text',
    "required" BOOLEAN NOT NULL DEFAULT false,
    "placeholder" TEXT,
    "options" TEXT,
    "defaultValue" TEXT,
    "order" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "appliesTo" TEXT NOT NULL DEFAULT 'both',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DynamicField_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DynamicFieldValue" (
    "id" TEXT NOT NULL,
    "fieldId" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "value" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DynamicFieldValue_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "DynamicField_name_key" ON "DynamicField"("name");

-- CreateIndex
CREATE INDEX "DynamicFieldValue_fieldId_idx" ON "DynamicFieldValue"("fieldId");

-- CreateIndex
CREATE INDEX "DynamicFieldValue_entityType_entityId_idx" ON "DynamicFieldValue"("entityType", "entityId");

-- CreateIndex
CREATE UNIQUE INDEX "DynamicFieldValue_fieldId_entityType_entityId_key" ON "DynamicFieldValue"("fieldId", "entityType", "entityId");

-- AddForeignKey
ALTER TABLE "DynamicFieldValue" ADD CONSTRAINT "DynamicFieldValue_fieldId_fkey" FOREIGN KEY ("fieldId") REFERENCES "DynamicField"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Insert default fields for backward compatibility
INSERT INTO "DynamicField" ("id", "name", "label", "type", "required", "placeholder", "order", "isActive", "appliesTo") VALUES
('default-title', 'title', 'Título', 'text', true, 'Ingrese el título de la tarea', 1, true, 'both'),
('default-description', 'description', 'Descripción', 'textarea', false, 'Descripción detallada (opcional)', 2, true, 'both'),
('default-task', 'task', 'Tarea Relacionada', 'select', false, 'Seleccione una tarea (opcional)', 3, true, 'timeEntry'),
('default-hours', 'hoursWorked', 'Horas Trabajadas', 'number', true, 'Ej: 2.5', 4, true, 'timeEntry'),
('default-start-time', 'startTime', 'Fecha y Hora de Inicio', 'datetime-local', true, '', 5, true, 'timeEntry'),
('default-end-time', 'endTime', 'Fecha y Hora de Fin', 'datetime-local', true, '', 6, true, 'timeEntry'),
('default-estimated-hours', 'estimatedHours', 'Horas Estimadas', 'number', false, 'Ej: 4', 7, true, 'task'),
('default-due-date', 'dueDate', 'Fecha Límite', 'date', false, '', 8, true, 'task'),
('default-priority', 'priority', 'Prioridad', 'select', false, 'Seleccione prioridad', 9, true, 'task');

-- Insert default options for priority field
UPDATE "DynamicField" SET "options" = 'LOW,MEDIUM,HIGH,URGENT' WHERE "name" = 'priority';
