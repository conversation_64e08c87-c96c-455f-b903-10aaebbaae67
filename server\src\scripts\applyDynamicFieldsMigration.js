const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function applyDynamicFieldsMigration() {
  try {
    console.log('🔄 Aplicando migración de campos dinámicos...');

    // Verificar si las tablas ya existen
    const existingTables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('DynamicField', 'DynamicFieldValue')
    `;

    if (existingTables.length > 0) {
      console.log('✅ Las tablas de campos dinámicos ya existen');
      return;
    }

    // Crear tabla DynamicField
    await prisma.$executeRaw`
      CREATE TABLE "DynamicField" (
          "id" TEXT NOT NULL,
          "name" TEXT NOT NULL,
          "label" TEXT NOT NULL,
          "type" TEXT NOT NULL DEFAULT 'text',
          "required" BOOLEAN NOT NULL DEFAULT false,
          "placeholder" TEXT,
          "options" TEXT,
          "defaultValue" TEXT,
          "order" INTEGER NOT NULL DEFAULT 0,
          "isActive" BOOLEAN NOT NULL DEFAULT true,
          "appliesTo" TEXT NOT NULL DEFAULT 'both',
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

          CONSTRAINT "DynamicField_pkey" PRIMARY KEY ("id")
      )
    `;

    // Crear tabla DynamicFieldValue
    await prisma.$executeRaw`
      CREATE TABLE "DynamicFieldValue" (
          "id" TEXT NOT NULL,
          "fieldId" TEXT NOT NULL,
          "entityType" TEXT NOT NULL,
          "entityId" TEXT NOT NULL,
          "value" TEXT,
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

          CONSTRAINT "DynamicFieldValue_pkey" PRIMARY KEY ("id")
      )
    `;

    // Crear índices
    await prisma.$executeRaw`CREATE UNIQUE INDEX "DynamicField_name_key" ON "DynamicField"("name")`;
    await prisma.$executeRaw`CREATE INDEX "DynamicFieldValue_fieldId_idx" ON "DynamicFieldValue"("fieldId")`;
    await prisma.$executeRaw`CREATE INDEX "DynamicFieldValue_entityType_entityId_idx" ON "DynamicFieldValue"("entityType", "entityId")`;
    await prisma.$executeRaw`CREATE UNIQUE INDEX "DynamicFieldValue_fieldId_entityType_entityId_key" ON "DynamicFieldValue"("fieldId", "entityType", "entityId")`;

    // Agregar foreign key
    await prisma.$executeRaw`ALTER TABLE "DynamicFieldValue" ADD CONSTRAINT "DynamicFieldValue_fieldId_fkey" FOREIGN KEY ("fieldId") REFERENCES "DynamicField"("id") ON DELETE CASCADE ON UPDATE CASCADE`;

    console.log('✅ Tablas creadas exitosamente');

    // Insertar campos por defecto
    const defaultFields = [
      {
        id: 'default-title',
        name: 'title',
        label: 'Título',
        type: 'text',
        required: true,
        placeholder: 'Ingrese el título de la tarea',
        order: 1,
        isActive: true,
        appliesTo: 'both'
      },
      {
        id: 'default-description',
        name: 'description',
        label: 'Descripción',
        type: 'textarea',
        required: false,
        placeholder: 'Descripción detallada (opcional)',
        order: 2,
        isActive: true,
        appliesTo: 'both'
      },
      {
        id: 'default-task',
        name: 'task',
        label: 'Tarea Relacionada',
        type: 'select',
        required: false,
        placeholder: 'Seleccione una tarea (opcional)',
        order: 3,
        isActive: true,
        appliesTo: 'timeEntry'
      },
      {
        id: 'default-hours',
        name: 'hoursWorked',
        label: 'Horas Trabajadas',
        type: 'number',
        required: true,
        placeholder: 'Ej: 2.5',
        order: 4,
        isActive: true,
        appliesTo: 'timeEntry'
      },
      {
        id: 'default-start-time',
        name: 'startTime',
        label: 'Fecha y Hora de Inicio',
        type: 'datetime-local',
        required: true,
        placeholder: '',
        order: 5,
        isActive: true,
        appliesTo: 'timeEntry'
      },
      {
        id: 'default-end-time',
        name: 'endTime',
        label: 'Fecha y Hora de Fin',
        type: 'datetime-local',
        required: true,
        placeholder: '',
        order: 6,
        isActive: true,
        appliesTo: 'timeEntry'
      },
      {
        id: 'default-estimated-hours',
        name: 'estimatedHours',
        label: 'Horas Estimadas',
        type: 'number',
        required: false,
        placeholder: 'Ej: 4',
        order: 7,
        isActive: true,
        appliesTo: 'task'
      },
      {
        id: 'default-due-date',
        name: 'dueDate',
        label: 'Fecha Límite',
        type: 'date',
        required: false,
        placeholder: '',
        order: 8,
        isActive: true,
        appliesTo: 'task'
      },
      {
        id: 'default-priority',
        name: 'priority',
        label: 'Prioridad',
        type: 'select',
        required: false,
        placeholder: 'Seleccione prioridad',
        options: 'LOW,MEDIUM,HIGH,URGENT',
        order: 9,
        isActive: true,
        appliesTo: 'task'
      }
    ];

    for (const field of defaultFields) {
      await prisma.dynamicField.create({
        data: field
      });
    }

    console.log('✅ Campos por defecto insertados exitosamente');
    console.log('🎉 Migración de campos dinámicos completada');

  } catch (error) {
    console.error('❌ Error al aplicar migración:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  applyDynamicFieldsMigration()
    .then(() => {
      console.log('✅ Migración completada exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Error en migración:', error);
      process.exit(1);
    });
}

module.exports = { applyDynamicFieldsMigration };
