const express = require('express');
const router = express.Router();
const permissionController = require('../controllers/permissionController');
const { authenticateToken } = require('../middleware/auth');

// Aplicar middleware de autenticación a todas las rutas
router.use(authenticateToken);

// Rutas básicas para permisos
router.post('/', permissionController.createPermission);
router.get('/', permissionController.getUserPermissions);
router.get('/pending', permissionController.getPendingPermissions);
router.patch('/:id/status', permissionController.updatePermissionStatus);
router.post('/check-availability', permissionController.checkAvailability);
router.post('/validate', permissionController.validatePermission);

// Ruta para obtener permisos aprobados por rango de fechas
router.get('/approved-by-date', permissionController.getApprovedPermissionsByDate);
router.get('/overtime-hours', permissionController.getOvertimeHours);

module.exports = router;


