const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { authenticateToken, isAdmin } = require('../middleware/auth');

// Aplicar middleware de autenticación a todas las rutas
router.use(authenticateToken);

// Rutas para usuarios
router.get('/', userController.getAllUsers);
router.get('/:id', userController.getUser);
router.post('/', isAdmin, userController.createUser);
router.put('/:id', isAdmin, userController.updateUser);
router.patch('/:id/deactivate', isAdmin, userController.deactivateUser);

// Nuevas rutas para gestión de usuarios
router.post('/:id/reset-password', isAdmin, userController.resetPassword);
router.put('/:id/toggle-active', isAdmin, userController.toggleUserActive);
router.delete('/:id', isAdmin, userController.deleteUser);

module.exports = router;
