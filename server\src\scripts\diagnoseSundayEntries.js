const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function diagnoseSundayEntries() {
  try {
    console.log('🔍 DIAGNOSTICANDO ENTRADAS DEL DOMINGO...\n');

    // 1. <PERSON><PERSON> todas las entradas de tiempo aprobadas
    const allEntries = await prisma.timeEntry.findMany({
      where: {
        status: 'APPROVED',
        startTime: {
          gte: new Date('2024-01-01'),
        }
      },
      include: {
        user: {
          select: { name: true, email: true }
        }
      },
      orderBy: {
        startTime: 'desc'
      }
    });

    console.log(`📊 TOTAL ENTRADAS APROBADAS ENCONTRADAS: ${allEntries.length}`);

    // 2. Filtrar entradas del domingo
    const sundayEntries = allEntries.filter(entry => {
      const entryDate = new Date(entry.startTime);
      return entryDate.getDay() === 0; // Domingo
    });

    console.log(`🌅 ENTRADAS DEL DOMINGO: ${sundayEntries.length}`);

    if (sundayEntries.length === 0) {
      console.log('❌ NO SE ENCONTRARON ENTRADAS DEL DOMINGO');
      console.log('💡 SUGERENCIA: Crear una entrada manual del domingo para probar');
      
      // Mostrar algunas entradas recientes para referencia
      console.log('\n📝 ENTRADAS RECIENTES (para referencia):');
      allEntries.slice(0, 5).forEach(entry => {
        const entryDate = new Date(entry.startTime);
        const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
        console.log(`  - ${entry.user.name}: "${entry.title}" (${dayNames[entryDate.getDay()]} ${entryDate.toISOString().split('T')[0]}) - ${entry.hoursWorked}h`);
      });
    } else {
      console.log('\n📋 DETALLES DE ENTRADAS DEL DOMINGO:');
      sundayEntries.forEach((entry, index) => {
        const entryDate = new Date(entry.startTime);
        const entryEnd = new Date(entry.endTime);
        console.log(`\n${index + 1}. ENTRADA DEL DOMINGO:`);
        console.log(`   👤 Usuario: ${entry.user.name} (${entry.user.email})`);
        console.log(`   📝 Título: "${entry.title}"`);
        console.log(`   📅 Fecha: ${entryDate.toISOString().split('T')[0]}`);
        console.log(`   ⏰ Horario: ${entryDate.toTimeString().split(' ')[0]} - ${entryEnd.toTimeString().split(' ')[0]}`);
        console.log(`   🕐 Horas trabajadas: ${entry.hoursWorked}h`);
        console.log(`   ✅ Estado: ${entry.status}`);
        console.log(`   🆔 ID: ${entry.id}`);
      });

      // 3. Simular cálculo del multiplicador para estas entradas
      console.log('\n🧮 SIMULACIÓN DE CÁLCULO DE MULTIPLICADOR:');
      
      // Obtener multiplicadores activos
      const timeMultipliers = await prisma.timeMultiplier.findMany({
        where: { isActive: true }
      });

      console.log(`📊 Multiplicadores activos: ${timeMultipliers.length}`);
      timeMultipliers.forEach(mult => {
        console.log(`  - ${mult.name}: ${mult.value}x ${mult.startTime ? `(${mult.startTime}-${mult.endTime})` : '(sin horario específico)'}`);
      });

      // Función de cálculo de multiplicador (copiada del dashboard)
      const calculateMultiplier = (entryStartTime, entryEndTime) => {
        const entryDate = new Date(entryStartTime);
        const dayOfWeek = entryDate.getDay();
        const startHour = entryDate.getHours();
        const startMinute = entryDate.getMinutes();

        let applicableMultiplier = 1.0;
        let multiplierReasons = [];

        // 1. Domingos automáticamente tienen multiplicador 2.0x
        if (dayOfWeek === 0) {
          applicableMultiplier = Math.max(applicableMultiplier, 2.0);
          multiplierReasons.push(`Domingo (considerado feriado): 2.0x`);
        }

        // 2. Verificar multiplicadores por horario
        timeMultipliers.forEach(mult => {
          if (mult.startTime && mult.endTime) {
            const [multStartHour, multStartMin] = mult.startTime.split(':').map(Number);
            const [multEndHour, multEndMin] = mult.endTime.split(':').map(Number);

            const multStartMinutes = multStartHour * 60 + multStartMin;
            const multEndMinutes = multEndHour * 60 + multEndMin;
            const entryStartMinutes = startHour * 60 + startMinute;

            let isInTimeRange = false;
            if (multStartMinutes > multEndMinutes) {
              isInTimeRange = (entryStartMinutes >= multStartMinutes) || (entryStartMinutes <= multEndMinutes);
            } else {
              isInTimeRange = (entryStartMinutes >= multStartMinutes) && (entryStartMinutes <= multEndMinutes);
            }

            if (isInTimeRange) {
              applicableMultiplier = Math.max(applicableMultiplier, mult.value);
              multiplierReasons.push(`${mult.name} (${mult.startTime}-${mult.endTime}): ${mult.value}x`);
            }
          }
        });

        return { multiplier: applicableMultiplier, reasons: multiplierReasons };
      };

      sundayEntries.forEach((entry, index) => {
        const { multiplier, reasons } = calculateMultiplier(entry.startTime, entry.endTime);
        const hoursToMultiply = entry.hoursWorked; // Para domingo, todas las horas
        const weightedHours = hoursToMultiply * multiplier;

        console.log(`\n🧮 CÁLCULO PARA ENTRADA ${index + 1}:`);
        console.log(`   📝 "${entry.title}" - ${entry.user.name}`);
        console.log(`   ⚖️ Horas a multiplicar: ${hoursToMultiply}h (todas las horas del domingo)`);
        console.log(`   📊 Multiplicador aplicado: ${multiplier}x`);
        console.log(`   💎 Horas ponderadas: ${weightedHours}h`);
        console.log(`   📋 Razones: ${reasons.join(', ')}`);
        console.log(`   🎯 Columna destino: ${multiplier >= 2.0 ? 'Horas × 2.0' : multiplier >= 1.5 ? 'Horas × 1.5' : 'Sin multiplicador'}`);
      });
    }

    // 4. Verificar configuración de multiplicadores
    console.log('\n🔧 VERIFICACIÓN DE CONFIGURACIÓN:');
    const holidayMultiplier = timeMultipliers.find(m => 
      m.name.toLowerCase().includes('feriado') || 
      m.name.toLowerCase().includes('holiday') ||
      m.value === 2.0
    );

    if (holidayMultiplier) {
      console.log(`✅ Multiplicador de feriados encontrado: ${holidayMultiplier.name} (${holidayMultiplier.value}x)`);
    } else {
      console.log(`❌ No se encontró multiplicador de feriados con valor 2.0x`);
    }

    console.log('\n✅ DIAGNÓSTICO COMPLETADO');

  } catch (error) {
    console.error('❌ ERROR EN DIAGNÓSTICO:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  diagnoseSundayEntries();
}

module.exports = { diagnoseSundayEntries };
