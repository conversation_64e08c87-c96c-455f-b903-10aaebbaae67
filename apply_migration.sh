#!/bin/bash

echo "Aplicando migración manual a la base de datos Docker..."

# Buscar contenedores de PostgreSQL
echo ""
echo "Buscando contenedores de PostgreSQL..."
docker ps | grep postgres

echo ""
echo "IMPORTANTE: Ajusta los siguientes valores según tu configuración:"
echo "- Nombre del contenedor PostgreSQL"
echo "- Usuario de la base de datos"
echo "- Nombre de la base de datos"
echo ""

# Solicitar información del usuario
read -p "Nombre del contenedor PostgreSQL: " CONTAINER_NAME
read -p "Usuario de la base de datos: " DB_USER
read -p "Nombre de la base de datos: " DB_NAME

echo ""
echo "Aplicando migración..."
docker exec -i $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < server/manual_migration.sql

echo ""
echo "Ejecutando verificación..."
docker exec -i $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < server/verify_migration.sql

echo ""
echo "Migración completada. Revisa los resultados de verificación arriba."
echo "Si todo está correcto, reinicia el servidor backend y frontend."
