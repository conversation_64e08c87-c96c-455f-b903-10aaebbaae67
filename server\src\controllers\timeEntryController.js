const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Crear un nuevo registro de tiempo
exports.createTimeEntry = async (req, res) => {
  try {
    console.log('Creando registro de tiempo. Datos recibidos:', JSON.stringify(req.body, null, 2));
    console.log('Usuario autenticado:', req.user.id, req.user.email);

    const { startTime, endTime, description, taskId, title, hoursWorked, type = 'REGULAR', dynamicFields } = req.body;

    // Validar datos requeridos
    if (!startTime || !endTime || !title) {
      console.log('Faltan campos requeridos:', { startTime, endTime, title });
      return res.status(400).json({ error: 'Faltan campos requeridos (startTime, endTime, title)' });
    }

    // Validar que hoursWorked sea un número o calcular basado en las fechas
    let parsedHours = 0;
    if (hoursWorked !== null && hoursWorked !== undefined) {
      parsedHours = parseFloat(hoursWorked);
      if (isNaN(parsedHours)) {
        // Si no es un número válido, calcular basado en las fechas
        const start = new Date(startTime);
        const end = new Date(endTime);
        const diffMs = end - start;
        parsedHours = Number((diffMs / (1000 * 60 * 60)).toFixed(2));
      }
    } else {
      // Calcular basado en las fechas
      const start = new Date(startTime);
      const end = new Date(endTime);
      const diffMs = end - start;
      parsedHours = Number((diffMs / (1000 * 60 * 60)).toFixed(2));
    }

    console.log('Horas calculadas:', parsedHours);

    // SIMPLIFICADO: Trabajar directamente en hora peruana sin conversiones
    const startTimePeru = new Date(startTime);
    const endTimePeru = new Date(endTime);

    console.log(`🇵🇪 HORA PERUANA DIRECTA:`);
    console.log(`   📅 Inicio: ${startTimePeru.toISOString()}`);
    console.log(`   📅 Fin: ${endTimePeru.toISOString()}`);

    // Crear objeto de datos base
    const timeEntryData = {
      title,
      description,
      startTime: startTimePeru,
      endTime: endTimePeru,
      hoursWorked: parsedHours,
      status: 'PENDING',
      type,
      user: {
        connect: {
          id: req.user.id
        }
      }
    };

    // Buscar una tarea existente con el mismo título o usar el taskId proporcionado
    let existingTask = null;
    if (!taskId) {
      existingTask = await prisma.task.findFirst({
        where: {
          title: title,
          assigneeId: req.user.id,
          source: 'KANBAN' // Solo buscar en tareas del kanban
        }
      });
    }

    if (taskId) {
      timeEntryData.task = {
        connect: {
          id: taskId
        }
      };
    } else if (existingTask) {
      timeEntryData.task = {
        connect: {
          id: existingTask.id
        }
      };
    } else {
      // Si no hay taskId ni tarea existente, crear una nueva tarea manual
      console.log('Creando nueva tarea automáticamente para el registro de tiempo manual');
      timeEntryData.task = {
        create: {
          title: title,
          description: description || '',
          status: 'COMPLETED', // Nuevo estado para tareas manuales
          source: 'MANUAL_ENTRY', // Marcar como entrada manual
          assigneeId: req.user.id,
          creatorId: req.user.id,
          estimatedHours: parsedHours,
          actualHours: parsedHours,
          priority: 'MEDIUM'
        }
      };
      console.log('Nueva tarea manual será creada con estado COMPLETED');
    }

    console.log('Datos a guardar en la base de datos:', JSON.stringify(timeEntryData, null, 2));

    const timeEntry = await prisma.timeEntry.create({
      data: timeEntryData,
      include: {
        user: true,
        task: true
      }
    });

    // Guardar valores de campos dinámicos si existen
    if (dynamicFields && Object.keys(dynamicFields).length > 0) {
      // Obtener los IDs de los campos dinámicos
      const fieldNames = Object.keys(dynamicFields);
      const fields = await prisma.dynamicField.findMany({
        where: {
          name: {
            in: fieldNames
          }
        },
        select: {
          id: true,
          name: true
        }
      });

      const dynamicFieldValues = Object.entries(dynamicFields)
        .map(([fieldName, value]) => {
          const field = fields.find(f => f.name === fieldName);
          if (!field || !value) return null;

          return {
            fieldId: field.id,
            entityType: 'timeEntry',
            entityId: timeEntry.id,
            value: String(value)
          };
        })
        .filter(Boolean);

      if (dynamicFieldValues.length > 0) {
        await prisma.dynamicFieldValue.createMany({
          data: dynamicFieldValues,
          skipDuplicates: true
        });
      }
    }

    res.status(201).json(timeEntry);
  } catch (error) {
    console.error('Error al crear registro de tiempo:', error);
    res.status(500).json({ error: 'Error al crear el registro de tiempo' });
  }
};

// Obtener todos los registros de tiempo del usuario
exports.getTimeEntries = async (req, res) => {
  try {
    const timeEntries = await prisma.timeEntry.findMany({
      where: {
        user: {
          id: req.user.id
        }
      },
      include: {
        task: true,
        user: true
      },
      orderBy: {
        startTime: 'desc'
      }
    });

    res.json(timeEntries);
  } catch (error) {
    console.error('Error al obtener registros de tiempo:', error);
    res.status(500).json({ error: 'Error al obtener los registros de tiempo' });
  }
};

// Obtener un registro de tiempo específico
exports.getTimeEntry = async (req, res) => {
  try {
    const { id } = req.params;
    const timeEntry = await prisma.timeEntry.findUnique({
      where: { id },
      include: {
        task: true,
        user: true
      }
    });

    if (!timeEntry) {
      return res.status(404).json({ error: 'Registro de tiempo no encontrado' });
    }

    if (timeEntry.user.id !== req.user.id && req.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'No autorizado' });
    }

    res.json(timeEntry);
  } catch (error) {
    console.error('Error al obtener registro de tiempo:', error);
    res.status(500).json({ error: 'Error al obtener el registro de tiempo' });
  }
};

// Actualizar un registro de tiempo
exports.updateTimeEntry = async (req, res) => {
  try {
    const { id } = req.params;
    const { startTime, endTime, description, task } = req.body;

    const timeEntry = await prisma.timeEntry.findUnique({
      where: { id }
    });

    if (!timeEntry) {
      return res.status(404).json({ error: 'Registro de tiempo no encontrado' });
    }

    if (timeEntry.user.id !== req.user.id) {
      return res.status(403).json({ error: 'No autorizado' });
    }

    // SIMPLIFICADO: Trabajar directamente en hora peruana sin conversiones
    let startTimePeru, endTimePeru;
    if (startTime) {
      startTimePeru = new Date(startTime);
      console.log(`🇵🇪 ACTUALIZACIÓN - Start: ${startTimePeru.toISOString()}`);
    }
    if (endTime) {
      endTimePeru = new Date(endTime);
      console.log(`🇵🇪 ACTUALIZACIÓN - End: ${endTimePeru.toISOString()}`);
    }

    const updatedTimeEntry = await prisma.timeEntry.update({
      where: { id },
      data: {
        startTime: startTimePeru,
        endTime: endTimePeru,
        description,
        task: task ? {
          connect: {
            id: task
          }
        } : undefined,
        status: 'PENDING'
      },
      include: {
        task: true,
        user: true
      }
    });

    res.json(updatedTimeEntry);
  } catch (error) {
    console.error('Error al actualizar registro de tiempo:', error);
    res.status(500).json({ error: 'Error al actualizar el registro de tiempo' });
  }
};

// Eliminar un registro de tiempo
exports.deleteTimeEntry = async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`🗑️ Solicitud para eliminar registro de tiempo ${id} por usuario ${req.user.id} (${req.user.email})`);

    // Verificar si el registro existe
    const timeEntry = await prisma.timeEntry.findUnique({
      where: { id },
      include: { 
        user: true,
        task: true 
      }
    });

    if (!timeEntry) {
      console.log(`❌ Registro de tiempo ${id} no encontrado`);
      return res.status(404).json({ error: 'Registro de tiempo no encontrado' });
    }

    // Permitir que administradores y managers eliminen cualquier registro
    const canDelete = timeEntry.user.id === req.user.id || 
                     req.user.role === 'ADMIN' || 
                     req.user.role === 'MANAGER';
                     
    if (!canDelete) {
      console.log(`🚫 Usuario ${req.user.id} no autorizado para eliminar registro ${id}`);
      return res.status(403).json({ error: 'No autorizado para eliminar este registro' });
    }

    // Si el registro tiene una tarea asociada que fue creada automáticamente (MANUAL_ENTRY),
    // también podríamos eliminarla, pero solo si no tiene otros registros de tiempo asociados
    if (timeEntry.taskId && timeEntry.task && timeEntry.task.source === 'MANUAL_ENTRY') {
      // Verificar si hay otros registros de tiempo asociados a esta tarea
      const otherTimeEntries = await prisma.timeEntry.count({
        where: {
          taskId: timeEntry.taskId,
          id: { not: id }
        }
      });

      if (otherTimeEntries === 0) {
        console.log(`🗑️ Eliminando tarea manual asociada ${timeEntry.taskId}`);
        try {
          await prisma.task.delete({
            where: { id: timeEntry.taskId }
          });
        } catch (taskError) {
          console.error('⚠️ Error al eliminar tarea asociada, continuando con eliminación del registro:', taskError);
          // No interrumpimos el proceso si falla la eliminación de la tarea
        }
      }
    }

    // Eliminar el registro de tiempo
    console.log(`🗑️ Eliminando registro de tiempo ${id}`);
    await prisma.timeEntry.delete({
      where: { id }
    });

    console.log(`✅ Registro de tiempo ${id} eliminado exitosamente`);
    res.json({ message: 'Registro de tiempo eliminado exitosamente' });
  } catch (error) {
    console.error('❌ Error al eliminar registro de tiempo:', error);
    
    // Manejar errores específicos
    let errorMessage = 'Error al eliminar el registro de tiempo';
    let statusCode = 500;
    
    if (error.code === 'P2025') {
      errorMessage = 'Registro no encontrado o ya fue eliminado';
      statusCode = 404;
    } else if (error.code === 'P2003') {
      errorMessage = 'No se puede eliminar debido a restricciones de integridad referencial';
      statusCode = 400;
    }
    
    res.status(statusCode).json({ 
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Aprobar o rechazar un registro de tiempo
exports.approveTimeEntry = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    console.log(`Actualizando estado de registro ${id} a ${status}`);

    // Verificar que el usuario sea administrador o manager
    if (req.user.role !== 'ADMIN' && req.user.role !== 'MANAGER') {
      return res.status(403).json({ error: 'No autorizado para aprobar registros de tiempo' });
    }

    // Validar el estado
    if (!['PENDING', 'APPROVED', 'REJECTED'].includes(status)) {
      return res.status(400).json({ error: 'Estado inválido' });
    }

    const timeEntry = await prisma.timeEntry.findUnique({
      where: { id },
      include: { task: true }
    });

    if (!timeEntry) {
      return res.status(404).json({ error: 'Registro de tiempo no encontrado' });
    }

    const updatedTimeEntry = await prisma.timeEntry.update({
      where: { id },
      data: {
        status,
        approver: {
          connect: {
            id: req.user.id
          }
        },
        approvedAt: new Date()
      },
      include: {
        user: true,
        task: true,
        approver: true
      }
    });

    // Actualizar el estado de la tarea si el registro de tiempo es aprobado y tiene una tarea asociada
    if (status === 'APPROVED' && updatedTimeEntry.taskId) {
      // Solo actualizar tareas del kanban, las manuales ya están en COMPLETED
      if (updatedTimeEntry.task && updatedTimeEntry.task.source === 'KANBAN') {
        await prisma.task.update({
          where: { id: updatedTimeEntry.taskId },
          data: { status: 'ARCHIVED' }
        });
        console.log(`Tarea del kanban ${updatedTimeEntry.taskId} movida a ARCHIVED`);
      } else {
        console.log(`Tarea manual ${updatedTimeEntry.taskId} mantiene estado COMPLETED`);
      }
    }

    console.log(`Registro ${id} actualizado a ${status} por ${req.user.email}`);
    res.json(updatedTimeEntry);
  } catch (error) {
    console.error('Error al aprobar registro de tiempo:', error);
    res.status(500).json({
      error: 'Error al aprobar el registro de tiempo',
      details: error.message
    });
  }
};

// Obtener todos los registros de tiempo (solo para administradores)
exports.getAllTimeEntries = async (req, res) => {
  try {
    console.log('Obteniendo todos los registros de tiempo como administrador');

    if (req.user.role !== 'ADMIN' && req.user.role !== 'MANAGER') {
      return res.status(403).json({ error: 'No autorizado' });
    }

    const timeEntries = await prisma.timeEntry.findMany({
      include: {
        user: true,
        task: true,
        approver: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`Se encontraron ${timeEntries.length} registros de tiempo en total`);
    res.json(timeEntries);
  } catch (error) {
    console.error('Error al obtener todos los registros de tiempo:', error);
    res.status(500).json({
      error: 'Error al obtener los registros de tiempo',
      details: error.message
    });
  }
};

// Obtener registros de tiempo para aprobación (con filtros)
exports.getTimeEntriesForApproval = async (req, res) => {
  try {
    const { status } = req.query;

    console.log('Obteniendo registros de tiempo para aprobación');
    console.log('Usuario solicitante:', req.user.id, req.user.email, req.user.role);
    console.log('Filtro de estado:', status);

    if (req.user.role !== 'ADMIN' && req.user.role !== 'MANAGER') {
      console.log('Usuario no autorizado para ver registros:', req.user.role);
      return res.status(403).json({ error: 'No autorizado' });
    }

    // Construir filtro dinámico
    const whereClause = {};

    if (status && status !== 'ALL') {
      whereClause.status = status;
    }

    const timeEntries = await prisma.timeEntry.findMany({
      where: whereClause,
      include: {
        user: true,
        task: true,
        approver: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`Se encontraron ${timeEntries.length} registros de tiempo con filtro: ${status || 'ALL'}`);

    // Registrar algunos detalles para depuración
    if (timeEntries.length > 0) {
      console.log('Primeros 3 registros:');
      timeEntries.slice(0, 3).forEach((entry, index) => {
        console.log(`${index + 1}. ${entry.title} - Estado: ${entry.status} - Usuario: ${entry.user?.name}`);
      });
    } else {
      console.log('No se encontraron registros de tiempo');
    }

    res.json(timeEntries);
  } catch (error) {
    console.error('Error al obtener registros de tiempo:', error);
    res.status(500).json({
      error: 'Error al obtener los registros de tiempo',
      details: error.message
    });
  }
};

// Mantener el endpoint anterior para compatibilidad
exports.getPendingTimeEntries = async (req, res) => {
  req.query.status = 'PENDING';
  return exports.getTimeEntriesForApproval(req, res);
};

// NUEVO: Recalcular multiplicadores para todas las tareas aprobadas
exports.recalculateMultipliers = async (req, res) => {
  try {
    console.log('🔄 INICIANDO RECÁLCULO DE MULTIPLICADORES PARA TAREAS APROBADAS');
    console.log(`👤 Solicitado por: ${req.user.email} (${req.user.role})`);

    // Verificar permisos de administrador
    if (req.user.role !== 'ADMIN' && req.user.role !== 'MANAGER') {
      return res.status(403).json({ error: 'No autorizado para recalcular multiplicadores' });
    }

    // Obtener configuración actual de multiplicadores
    const multipliers = await prisma.setting.findMany({
      where: {
        key: {
          startsWith: 'multiplier_'
        }
      }
    });

    console.log(`⚙️ Multiplicadores configurados: ${multipliers.length}`);

    // Función para calcular multiplicador (copiada de taskController.js)
    const calculateMultiplier = (entryStartTime, entryEndTime) => {
      const entryDate = new Date(entryStartTime);
      const dayOfWeek = entryDate.getDay(); // 0 = Domingo, 1 = Lunes, etc.

      const startHour = entryDate.getHours();
      const startMinute = entryDate.getMinutes();

      let applicableMultiplier = 1.0;
      let multiplierReasons = [];

      // Verificar cada multiplicador configurado
      multipliers.forEach(mult => {
        const multConfig = JSON.parse(mult.value);

        // Verificar días aplicables
        if (multConfig.days && multConfig.days.includes(dayOfWeek)) {
          if (multConfig.startTime && multConfig.endTime) {
            // Convertir horarios a minutos para comparación
            const [multStartHour, multStartMin] = multConfig.startTime.split(':').map(Number);
            const [multEndHour, multEndMin] = multConfig.endTime.split(':').map(Number);
            const multStartMinutes = multStartHour * 60 + multStartMin;
            const multEndMinutes = multEndHour * 60 + multEndMin;
            const entryStartMinutes = startHour * 60 + startMinute;

            // Manejar horarios que cruzan medianoche
            let isInTimeRange = false;
            if (multStartMinutes > multEndMinutes) {
              // Cruza medianoche
              isInTimeRange = (entryStartMinutes >= multStartMinutes) || (entryStartMinutes <= multEndMinutes);
            } else {
              // No cruza medianoche
              isInTimeRange = (entryStartMinutes >= multStartMinutes) && (entryStartMinutes <= multEndMinutes);
            }

            if (isInTimeRange) {
              applicableMultiplier = Math.max(applicableMultiplier, multConfig.value);
              multiplierReasons.push(`${multConfig.name} (${multConfig.startTime}-${multConfig.endTime}): ${multConfig.value}x`);
            }
          }
        }
      });

      return { multiplier: applicableMultiplier, reasons: multiplierReasons };
    };

    // Obtener todas las tareas aprobadas
    const approvedEntries = await prisma.timeEntry.findMany({
      where: {
        status: 'APPROVED'
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    console.log(`📊 Tareas aprobadas encontradas: ${approvedEntries.length}`);

    let updatedCount = 0;
    let unchangedCount = 0;

    // Recalcular cada tarea
    for (const entry of approvedEntries) {
      const { multiplier, reasons } = calculateMultiplier(entry.startTime, entry.endTime);
      const newWeightedHours = entry.hoursWorked * multiplier;
      const newIsOutsideOfficeHours = multiplier > 1.0;

      // Verificar si hay cambios
      const hasChanges =
        entry.hourMultiplier !== multiplier ||
        entry.weightedHours !== newWeightedHours ||
        entry.isOutsideOfficeHours !== newIsOutsideOfficeHours;

      if (hasChanges) {
        await prisma.timeEntry.update({
          where: { id: entry.id },
          data: {
            hourMultiplier: multiplier,
            weightedHours: newWeightedHours,
            isOutsideOfficeHours: newIsOutsideOfficeHours,
            multiplierReasons: reasons
          }
        });

        console.log(`✅ ACTUALIZADO: ${entry.user.name} - "${entry.title}"`);
        console.log(`   🔄 Multiplicador: ${entry.hourMultiplier || 1.0}x → ${multiplier}x`);
        console.log(`   🔄 Horas ponderadas: ${entry.weightedHours || entry.hoursWorked}h → ${newWeightedHours}h`);
        console.log(`   🔄 Fuera de horario: ${entry.isOutsideOfficeHours || false} → ${newIsOutsideOfficeHours}`);

        updatedCount++;
      } else {
        unchangedCount++;
      }
    }

    console.log(`🎯 RECÁLCULO COMPLETADO:`);
    console.log(`   ✅ Actualizadas: ${updatedCount} tareas`);
    console.log(`   ➡️ Sin cambios: ${unchangedCount} tareas`);

    res.json({
      message: 'Recálculo de multiplicadores completado',
      totalEntries: approvedEntries.length,
      updatedEntries: updatedCount,
      unchangedEntries: unchangedCount
    });

  } catch (error) {
    console.error('Error al recalcular multiplicadores:', error);
    res.status(500).json({
      error: 'Error al recalcular multiplicadores',
      details: error.message
    });
  }
};

// Endpoint de diagnóstico para verificar datos
exports.getDiagnosticData = async (req, res) => {
  try {
    console.log('🔍 DIAGNÓSTICO DE BASE DE DATOS');

    // 1. Contar todos los TimeEntry
    const totalTimeEntries = await prisma.timeEntry.count();
    console.log(`Total TimeEntries: ${totalTimeEntries}`);

    // 2. Contar por estado
    const pendingCount = await prisma.timeEntry.count({ where: { status: 'PENDING' } });
    const approvedCount = await prisma.timeEntry.count({ where: { status: 'APPROVED' } });
    const rejectedCount = await prisma.timeEntry.count({ where: { status: 'REJECTED' } });

    console.log(`PENDING: ${pendingCount}, APPROVED: ${approvedCount}, REJECTED: ${rejectedCount}`);

    // 3. Obtener últimos 5 TimeEntry aprobados
    const recentApproved = await prisma.timeEntry.findMany({
      where: { status: 'APPROVED' },
      include: { user: true, task: true },
      orderBy: { approvedAt: 'desc' },
      take: 5
    });

    console.log('Últimos 5 aprobados:');
    recentApproved.forEach(entry => {
      console.log(`- ${entry.title} (${entry.startTime}) - Usuario: ${entry.user?.name}`);
    });

    // 4. Verificar registros manuales vs automáticos
    const manualEntries = await prisma.timeEntry.count({
      where: {
        status: 'APPROVED',
        NOT: { title: { startsWith: 'Tiempo trabajado en:' } }
      }
    });

    const autoEntries = await prisma.timeEntry.count({
      where: {
        status: 'APPROVED',
        title: { startsWith: 'Tiempo trabajado en:' }
      }
    });

    console.log(`Registros manuales aprobados: ${manualEntries}`);
    console.log(`Registros automáticos aprobados: ${autoEntries}`);

    // 5. Verificar rango de fechas reciente
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const recentApprovedInRange = await prisma.timeEntry.findMany({
      where: {
        status: 'APPROVED',
        startTime: { gte: weekAgo, lte: now }
      },
      include: { user: true },
      orderBy: { startTime: 'desc' }
    });

    console.log(`Registros aprobados en última semana: ${recentApprovedInRange.length}`);

    const diagnosticData = {
      totalTimeEntries,
      statusCounts: { pending: pendingCount, approved: approvedCount, rejected: rejectedCount },
      manualApproved: manualEntries,
      autoApproved: autoEntries,
      recentApprovedCount: recentApprovedInRange.length,
      recentApproved: recentApprovedInRange.map(entry => ({
        id: entry.id,
        title: entry.title,
        startTime: entry.startTime,
        endTime: entry.endTime,
        user: entry.user?.name,
        status: entry.status
      }))
    };

    res.json(diagnosticData);
  } catch (error) {
    console.error('Error en diagnóstico:', error);
    res.status(500).json({ error: 'Error en diagnóstico', details: error.message });
  }
};

