// In your moveTask function
try {
  const response = await axios.patch(
    `${API_URL}/api/kanban/tasks/${taskId}/status`,
    { status: newStatus },
    {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    }
  );
  return response.data;
} catch (error) {
  // Check for work hour restriction error
  if (error.response && error.response.status === 403) {
    // Display the error message from the server
    const errorMessage = error.response.data.message || error.response.data.error || 'No se puede mover la tarea fuera del horario laboral';
    
    // Use a simple alert for now
    window.alert(errorMessage);
    
    console.log('Work hour restriction:', error.response.data);
  } else {
    console.error('Error al mover la tarea:', error);
    window.alert('Error al mover la tarea');
  }
  throw error;
}

// Update the moveTaskForward function
const moveTaskForward = async (taskId, currentStatus) => {
  try {
    const nextStatus = getNextStatus(currentStatus);
    if (nextStatus) {
      await moveTask(taskId, nextStatus);
      // Refresh the board after successful move
      fetchTasks();
    }
  } catch (error) {
    // Error is already handled in moveTask function
    console.log('Error moving task forward:', error);
  }
};

// Update the moveTaskBackward function
const moveTaskBackward = async (taskId, currentStatus) => {
  try {
    const prevStatus = getPreviousStatus(currentStatus);
    if (prevStatus) {
      await moveTask(taskId, prevStatus);
      // Refresh the board after successful move
      fetchTasks();
    }
  } catch (error) {
    // Error is already handled in moveTask function
    console.log('Error moving task backward:', error);
  }
};

// Update the moveTask function with proper error handling
const moveTask = async (taskId, newStatus) => {
  try {
    const response = await axios.patch(
      `${API_URL}/api/kanban/tasks/${taskId}/status`,
      { status: newStatus },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    if (error.response && 
        error.response.status === 403 && 
        error.response.data.message && 
        error.response.data.message.includes('horario laboral')) {
      
      // Simple alert with the error message
      alert(error.response.data.message);
      
      console.log('Work hour restriction:', error.response.data);
    } else {
      console.error('Error al mover la tarea:', error);
      alert('Error al mover la tarea');
    }
    throw error;
  }
};