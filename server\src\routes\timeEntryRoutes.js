const express = require('express');
const router = express.Router();
const timeEntryController = require('../controllers/timeEntryController');
const { authenticateToken } = require('../middleware/auth');

// Aplicar middleware de autenticación a todas las rutas
router.use(authenticateToken);

// Rutas para time entries
router.post('/', timeEntryController.createTimeEntry);
router.get('/', timeEntryController.getTimeEntries);
router.get('/my-entries', timeEntryController.getTimeEntries);
router.get('/all', timeEntryController.getAllTimeEntries);
router.get('/pending', timeEntryController.getPendingTimeEntries);
router.get('/for-approval', timeEntryController.getTimeEntriesForApproval);
router.get('/diagnostic', timeEntryController.getDiagnosticData);
router.get('/:id', timeEntryController.getTimeEntry);
router.put('/:id', timeEntryController.updateTimeEntry);
router.delete('/:id', timeEntryController.deleteTimeEntry);
router.patch('/:id/approve', timeEntryController.approveTimeEntry);
router.post('/recalculate-multipliers', timeEntryController.recalculateMultipliers);

module.exports = router;
