const util = require('util');
const exec = util.promisify(require('child_process').exec);
const execAsync = util.promisify(require('child_process').exec);
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { PrismaClient } = require('@prisma/client');
const winston = require('winston');
const socketService = require('./socketService');

class BackupService {
  constructor() {
    this.backupDir = path.join(__dirname, '../../backups');

    // Asegurar que el directorio de backups existe
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }

    // Configurar logger específico para backups
    this.logger = winston.createLogger({
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.File({
          filename: path.join(this.backupDir, 'backup.log')
        })
      ]
    });
  }

  async createBackup() {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `backup-${timestamp}.sql`;
      const backupPath = path.join(this.backupDir, backupFileName);

      await this.executeBackup(backupPath);

      const backupInfo = {
        fileName: backupFileName,
        path: backupPath,
        size: fs.statSync(backupPath).size,
        createdAt: new Date()
      };

      // Notificar backup completado
      socketService.notifyBackupComplete(backupInfo);

      this.logger.info(`Backup creado exitosamente: ${backupFileName}`);

      // Limpiar backups antiguos
      await this.cleanOldBackups();

      return backupInfo;
    } catch (error) {
      this.logger.error('Error al crear backup:', error);
      throw new Error('Error al crear backup de la base de datos');
    }
  }

  async restoreBackup(backupFileName) {
    const backupPath = path.join(this.backupDir, backupFileName);

    try {
      if (!fs.existsSync(backupPath)) {
        throw new Error('Archivo de backup no encontrado');
      }

      const { DATABASE_URL } = process.env;
      const dbConfig = this.parseDatabaseUrl(DATABASE_URL);

      // Restaurar backup usando psql
      const command = `psql -h ${dbConfig.host} -U ${dbConfig.user} -d ${dbConfig.database} -f ${backupPath}`;

      await execAsync(command, {
        env: { ...process.env, PGPASSWORD: dbConfig.password }
      });

      this.logger.info(`Backup restaurado exitosamente: ${backupFileName}`);

      return {
        success: true,
        message: 'Backup restaurado exitosamente'
      };
    } catch (error) {
      this.logger.error('Error al restaurar backup:', error);
      throw new Error('Error al restaurar backup de la base de datos');
    }
  }

  async listBackups() {
    try {
      const files = await fs.promises.readdir(this.backupDir);
      const backups = files
        .filter(file => file.endsWith('.sql'))
        .map(file => {
          const filePath = path.join(this.backupDir, file);
          const stats = fs.statSync(filePath);

          // Usar mtime (modification time) si birthtime no está disponible
          const createdTime = stats.birthtime && stats.birthtime.getTime() > 0
            ? stats.birthtime
            : stats.mtime;

          console.log(`📁 ARCHIVO: ${file}`);
          console.log(`  📅 birthtime: ${stats.birthtime}`);
          console.log(`  📅 mtime: ${stats.mtime}`);
          console.log(`  📅 usando: ${createdTime}`);

          return {
            filename: file,
            path: filePath,
            created: createdTime,
            size: stats.size
          };
        })
        .sort((a, b) => b.created - a.created);

      return backups;
    } catch (error) {
      this.logger.error('Error al listar backups:', error);
      throw new Error('Error al listar backups');
    }
  }

  async cleanOldBackups() {
    try {
      const MAX_BACKUPS = 5; // Mantener solo los últimos 5 backups
      const backups = await this.listBackups();

      if (backups.length > MAX_BACKUPS) {
        const oldBackups = backups.slice(MAX_BACKUPS);
        for (const backup of oldBackups) {
          await fs.promises.unlink(backup.path);
          this.logger.info(`Backup antiguo eliminado: ${backup.filename}`);
        }
      }
    } catch (error) {
      this.logger.error('Error al limpiar backups antiguos:', error);
    }
  }

  async executeBackup(backupPath) {
    try {
      // Obtener variables de entorno de la base de datos
      const { DATABASE_URL } = process.env;
      if (!DATABASE_URL) {
        throw new Error('DATABASE_URL no está definida en las variables de entorno');
      }

      console.log('DATABASE_URL:', DATABASE_URL.replace(/:[^:]*@/, ':****@')); // Ocultar contraseña en logs

      const dbConfig = this.parseDatabaseUrl(DATABASE_URL);
      console.log('Configuración de conexión a la base de datos:', {
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database,
        user: dbConfig.user
      });

      // Verificar que pg_dump está instalado
      try {
        const { stdout: pgDumpVersion } = await execAsync('pg_dump --version');
        console.log('Versión de pg_dump:', pgDumpVersion.trim());
      } catch (pgDumpError) {
        console.error('Error al verificar pg_dump:', pgDumpError);
        throw new Error(`pg_dump no está instalado o no está en el PATH: ${pgDumpError.message}`);
      }

      // Verificar que el directorio de backups existe
      const backupDir = path.dirname(backupPath);
      console.log('Directorio de backups:', backupDir);

      if (!fs.existsSync(backupDir)) {
        console.log(`Creando directorio de backups: ${backupDir}`);
        try {
          fs.mkdirSync(backupDir, { recursive: true });
          console.log('Directorio creado exitosamente');
        } catch (mkdirError) {
          console.error('Error al crear directorio:', mkdirError);
          throw new Error(`No se pudo crear el directorio de backups: ${mkdirError.message}`);
        }
      }

      // Verificar permisos de escritura
      try {
        const testFile = path.join(backupDir, '.test_write_permission');
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        console.log('Permisos de escritura verificados correctamente');
      } catch (permError) {
        console.error('Error de permisos:', permError);
        throw new Error(`No hay permisos de escritura en el directorio de backups: ${permError.message}`);
      }

      // Crear backup usando pg_dump con manejo de errores mejorado
      const command = `pg_dump -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.user} -d ${dbConfig.database} -f ${backupPath}`;
      console.log('Ejecutando comando:', command.replace(new RegExp(`-U ${dbConfig.user}`), '-U ****'));

      try {
        const result = await execAsync(command, {
          env: { ...process.env, PGPASSWORD: dbConfig.password },
          timeout: 30000 // 30 segundos de timeout
        });

        if (result.stderr && result.stderr.length > 0) {
          console.warn('Advertencias de pg_dump:', result.stderr);
        }

        // Verificar que el archivo se creó correctamente
        if (!fs.existsSync(backupPath)) {
          throw new Error('El archivo de backup no se creó correctamente');
        }

        const fileSize = fs.statSync(backupPath).size;
        if (fileSize === 0) {
          throw new Error('El archivo de backup está vacío');
        }

        console.log(`Backup creado exitosamente en ${backupPath} (${fileSize} bytes)`);
      } catch (execError) {
        console.error('Error al ejecutar pg_dump:', execError);
        if (execError.stderr) {
          console.error('Error estándar de pg_dump:', execError.stderr);
        }
        throw new Error(`Error al ejecutar pg_dump: ${execError.message}`);
      }
    } catch (error) {
      console.error('Error detallado en executeBackup:', error);
      throw error; // Re-lanzar para manejo en createBackup
    }
  }

  parseDatabaseUrl(url) {
    console.log('🔍 PARSEANDO DATABASE_URL:', url.replace(/:[^:]*@/, ':****@')); // Ocultar contraseña

    // Intentar diferentes formatos de URL de PostgreSQL
    let matches;

    // Formato 1: ******************************************* (con o sin parámetros)
    matches = url.match(/postgres:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/([^?]+)(?:\?.*)?/);
    if (matches) {
      console.log('✅ Formato postgres:// detectado');
      return {
        user: matches[1],
        password: matches[2],
        host: matches[3],
        port: matches[4],
        database: matches[5]
      };
    }

    // Formato 2: ********************************************* (con o sin parámetros)
    matches = url.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/([^?]+)(?:\?.*)?/);
    if (matches) {
      console.log('✅ Formato postgresql:// detectado');
      return {
        user: matches[1],
        password: matches[2],
        host: matches[3],
        port: matches[4],
        database: matches[5]
      };
    }

    // Formato 3: ************************************** (sin puerto, con o sin parámetros)
    matches = url.match(/postgres:\/\/([^:]+):([^@]+)@([^\/]+)\/([^?]+)(?:\?.*)?/);
    if (matches) {
      console.log('✅ Formato postgres:// sin puerto detectado');
      return {
        user: matches[1],
        password: matches[2],
        host: matches[3],
        port: '5432', // Puerto por defecto
        database: matches[4]
      };
    }

    // Formato 4: **************************************** (sin puerto, con o sin parámetros)
    matches = url.match(/postgresql:\/\/([^:]+):([^@]+)@([^\/]+)\/([^?]+)(?:\?.*)?/);
    if (matches) {
      console.log('✅ Formato postgresql:// sin puerto detectado');
      return {
        user: matches[1],
        password: matches[2],
        host: matches[3],
        port: '5432', // Puerto por defecto
        database: matches[4]
      };
    }



    console.error('❌ FORMATO DE URL NO RECONOCIDO');
    console.error('URL recibida (censurada):', url.replace(/:[^:]*@/, ':****@'));
    console.error('Formatos soportados:');
    console.error('  - *******************************************');
    console.error('  - *********************************************');
    console.error('  - **************************************');
    console.error('  - ****************************************');

    throw new Error('URL de base de datos inválida. Formatos soportados: ******************************************* o *********************************************');
  }

  async diagnoseBackupIssues() {
    const results = {
      environment: {},
      filesystem: {},
      database: {},
      pgdump: {}
    };

    try {
      // 1. Verificar variables de entorno
      results.environment.NODE_ENV = process.env.NODE_ENV || 'no definido';
      results.environment.DATABASE_URL = process.env.DATABASE_URL ? 'definido' : 'no definido';

      // 2. Verificar sistema de archivos
      const backupDir = path.join(__dirname, '../../backups');
      results.filesystem.backupDirPath = backupDir;

      try {
        results.filesystem.backupDirExists = fs.existsSync(backupDir);
        if (!results.filesystem.backupDirExists) {
          try {
            fs.mkdirSync(backupDir, { recursive: true });
            results.filesystem.backupDirCreated = true;
          } catch (mkdirError) {
            results.filesystem.backupDirCreated = false;
            results.filesystem.mkdirError = mkdirError.message;
          }
        }

        // Verificar permisos
        try {
          const testFile = path.join(backupDir, '.test_write_permission');
          fs.writeFileSync(testFile, 'test');
          fs.unlinkSync(testFile);
          results.filesystem.writePermission = true;
        } catch (permError) {
          results.filesystem.writePermission = false;
          results.filesystem.permissionError = permError.message;
        }

        // Verificar espacio en disco
        try {
          const { stdout } = await execAsync('df -h .');
          results.filesystem.diskSpace = stdout.trim();
        } catch (dfError) {
          results.filesystem.diskSpaceError = dfError.message;
        }
      } catch (fsError) {
        results.filesystem.error = fsError.message;
      }

      // 3. Verificar pg_dump
      try {
        const { stdout: pgDumpVersion } = await execAsync('pg_dump --version');
        results.pgdump.installed = true;
        results.pgdump.version = pgDumpVersion.trim();
      } catch (pgDumpError) {
        results.pgdump.installed = false;
        results.pgdump.error = pgDumpError.message;
      }

      // 4. Verificar conexión a la base de datos
      if (process.env.DATABASE_URL) {
        try {
          const dbConfig = this.parseDatabaseUrl(process.env.DATABASE_URL);
          results.database.config = {
            host: dbConfig.host,
            port: dbConfig.port,
            database: dbConfig.database,
            user: dbConfig.user
          };

          // Intentar una conexión simple
          try {
            const { stdout } = await execAsync(
              `PGPASSWORD="${dbConfig.password}" psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.user} -d ${dbConfig.database} -c "SELECT 1 as connection_test"`,
              { timeout: 5000 }
            );
            results.database.connectionTest = 'exitoso';
            results.database.connectionOutput = stdout.trim();
          } catch (connError) {
            results.database.connectionTest = 'fallido';
            results.database.connectionError = connError.message;
            if (connError.stderr) {
              results.database.connectionStderr = connError.stderr;
            }
          }
        } catch (parseError) {
          results.database.error = `Error al parsear DATABASE_URL: ${parseError.message}`;
        }
      } else {
        results.database.error = 'DATABASE_URL no está definida';
      }

      return {
        status: 'completed',
        timestamp: new Date().toISOString(),
        results
      };
    } catch (error) {
      console.error('Error en diagnóstico de backup:', error);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
        stack: error.stack
      };
    }
  }

  async downloadBackup(filename) {
    try {
      console.log('🔽 PREPARANDO DESCARGA DE BACKUP:', filename);

      // Validar que el archivo existe
      const filePath = path.join(this.backupDir, filename);
      if (!fs.existsSync(filePath)) {
        throw new Error(`El archivo de backup ${filename} no existe`);
      }

      // Obtener información del archivo
      const stats = fs.statSync(filePath);

      console.log('📁 INFORMACIÓN DEL ARCHIVO:', {
        path: filePath,
        size: stats.size,
        created: stats.birthtime || stats.mtime
      });

      return {
        path: path.resolve(filePath), // Ruta absoluta para res.sendFile
        size: stats.size,
        filename: filename
      };
    } catch (error) {
      console.error('Error al preparar descarga de backup:', error);
      throw new Error(`Error al descargar backup: ${error.message}`);
    }
  }

  async uploadBackup(file) {
    try {
      console.log('🔼 PROCESANDO CARGA DE BACKUP:', file.originalname);

      // Validar extensión del archivo
      if (!file.originalname.endsWith('.sql')) {
        throw new Error('Solo se permiten archivos .sql');
      }

      // Generar nombre único para evitar conflictos
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const originalName = file.originalname.replace('.sql', '');
      const newFilename = `uploaded-${originalName}-${timestamp}.sql`;
      const destinationPath = path.join(this.backupDir, newFilename);

      console.log('📁 MOVIENDO ARCHIVO:', {
        from: file.path,
        to: destinationPath,
        originalName: file.originalname,
        newFilename: newFilename
      });

      // Asegurar que el directorio de backups existe
      if (!fs.existsSync(this.backupDir)) {
        fs.mkdirSync(this.backupDir, { recursive: true });
      }

      // Mover archivo desde temp a directorio de backups
      fs.renameSync(file.path, destinationPath);

      // Verificar que el archivo se movió correctamente
      if (!fs.existsSync(destinationPath)) {
        throw new Error('Error al mover el archivo al directorio de backups');
      }

      const stats = fs.statSync(destinationPath);

      console.log('✅ BACKUP CARGADO EXITOSAMENTE:', {
        filename: newFilename,
        size: stats.size,
        path: destinationPath
      });

      return {
        filename: newFilename,
        originalName: file.originalname,
        size: stats.size,
        created: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error al cargar backup:', error);

      // Limpiar archivo temporal si existe
      if (file.path && fs.existsSync(file.path)) {
        try {
          fs.unlinkSync(file.path);
        } catch (cleanupError) {
          console.error('Error al limpiar archivo temporal:', cleanupError);
        }
      }

      throw new Error(`Error al cargar backup: ${error.message}`);
    }
  }
}

module.exports = new BackupService();



