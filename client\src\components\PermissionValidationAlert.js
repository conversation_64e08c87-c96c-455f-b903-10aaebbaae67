import React, { useState, useEffect } from 'react';
import {
  <PERSON>ert,
  AlertTitle,
  Box,
  Collapse,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Chip,
  Stack,
  CircularProgress
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon
} from '@mui/icons-material';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

/**
 * Componente para mostrar validaciones de permisos en tiempo real
 */
const PermissionValidationAlert = ({ 
  permissionData, 
  onValidationChange,
  showDetails = true,
  autoValidate = true 
}) => {
  const [validationResult, setValidationResult] = useState(null);
  const [isValidating, setIsValidating] = useState(false);
  const [showDetailsExpanded, setShowDetailsExpanded] = useState(false);
  const [lastValidationData, setLastValidationData] = useState(null);

  // Función para validar el permiso
  const validatePermission = async (data) => {
    if (!data || !data.type || !data.startTime || !data.endTime) {
      setValidationResult(null);
      return;
    }

    // Evitar validaciones duplicadas
    const dataString = JSON.stringify(data);
    if (dataString === lastValidationData) {
      return;
    }

    setIsValidating(true);
    setLastValidationData(dataString);

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/permissions/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();
      setValidationResult(result);
      
      // Notificar al componente padre sobre el cambio de validación
      if (onValidationChange) {
        onValidationChange(result);
      }
    } catch (error) {
      console.error('Error al validar permiso:', error);
      const errorResult = {
        isValid: false,
        errors: ['Error al conectar con el servidor'],
        warnings: [],
        details: {}
      };
      setValidationResult(errorResult);
      
      if (onValidationChange) {
        onValidationChange(errorResult);
      }
    } finally {
      setIsValidating(false);
    }
  };

  // Efecto para validación automática
  useEffect(() => {
    if (autoValidate && permissionData) {
      const timeoutId = setTimeout(() => {
        validatePermission(permissionData);
      }, 500); // Debounce de 500ms

      return () => clearTimeout(timeoutId);
    }
  }, [permissionData, autoValidate]);

  // Función para obtener el tipo de alerta
  const getAlertSeverity = () => {
    if (!validationResult) return 'info';
    if (!validationResult.isValid) return 'error';
    if (validationResult.warnings.length > 0) return 'warning';
    return 'success';
  };

  // Función para obtener el título de la alerta
  const getAlertTitle = () => {
    if (isValidating) return 'Validando permiso...';
    if (!validationResult) return 'Ingresa los datos del permiso';
    if (!validationResult.isValid) return 'Permiso inválido';
    if (validationResult.warnings.length > 0) return 'Permiso válido con advertencias';
    return 'Permiso válido';
  };

  // Función para obtener el ícono apropiado
  const getStatusIcon = () => {
    if (isValidating) return <CircularProgress size={20} />;
    if (!validationResult) return <InfoIcon />;
    if (!validationResult.isValid) return <ErrorIcon />;
    if (validationResult.warnings.length > 0) return <WarningIcon />;
    return <CheckCircleIcon />;
  };

  // Si no hay datos para mostrar, no renderizar nada
  if (!validationResult && !isValidating) {
    return null;
  }

  return (
    <Box sx={{ mb: 2 }}>
      <Alert 
        severity={getAlertSeverity()}
        icon={getStatusIcon()}
        action={
          showDetails && validationResult && (validationResult.errors.length > 0 || validationResult.warnings.length > 0) ? (
            <IconButton
              aria-label="toggle details"
              color="inherit"
              size="small"
              onClick={() => setShowDetailsExpanded(!showDetailsExpanded)}
            >
              {showDetailsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          ) : null
        }
      >
        <AlertTitle>{getAlertTitle()}</AlertTitle>
        
        {/* Resumen rápido */}
        {validationResult && !isValidating && (
          <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
            {validationResult.errors.length > 0 && (
              <Chip 
                size="small" 
                color="error" 
                label={`${validationResult.errors.length} error${validationResult.errors.length > 1 ? 'es' : ''}`}
              />
            )}
            {validationResult.warnings.length > 0 && (
              <Chip 
                size="small" 
                color="warning" 
                label={`${validationResult.warnings.length} advertencia${validationResult.warnings.length > 1 ? 's' : ''}`}
              />
            )}
            {validationResult.isValid && validationResult.warnings.length === 0 && (
              <Chip 
                size="small" 
                color="success" 
                label="Todo correcto"
              />
            )}
          </Stack>
        )}

        {/* Detalles expandibles */}
        {showDetails && validationResult && (
          <Collapse in={showDetailsExpanded}>
            <Box sx={{ mt: 2 }}>
              {/* Errores */}
              {validationResult.errors.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="error" gutterBottom>
                    ❌ Errores que deben corregirse:
                  </Typography>
                  <List dense>
                    {validationResult.errors.map((error, index) => (
                      <ListItem key={index} sx={{ py: 0 }}>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <ErrorIcon color="error" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={error}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}

              {/* Advertencias */}
              {validationResult.warnings.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="warning.main" gutterBottom>
                    ⚠️ Advertencias importantes:
                  </Typography>
                  <List dense>
                    {validationResult.warnings.map((warning, index) => (
                      <ListItem key={index} sx={{ py: 0 }}>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <WarningIcon color="warning" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={warning}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}

              {/* Información adicional de límites */}
              {validationResult.details?.limitValidation && (
                <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    📊 Información de límites:
                  </Typography>
                  <Stack direction="row" spacing={2} flexWrap="wrap">
                    {validationResult.details.limitValidation.maxDaysPerYear > 0 && (
                      <>
                        <Chip 
                          size="small" 
                          label={`Límite anual: ${validationResult.details.limitValidation.maxDaysPerYear} días`}
                          variant="outlined"
                        />
                        <Chip 
                          size="small" 
                          label={`Ya utilizados: ${validationResult.details.limitValidation.daysUsed || 0} días`}
                          variant="outlined"
                        />
                        <Chip 
                          size="small" 
                          label={`Disponibles: ${validationResult.details.limitValidation.remainingDays || 0} días`}
                          variant="outlined"
                          color={validationResult.details.limitValidation.remainingDays > 0 ? 'success' : 'error'}
                        />
                      </>
                    )}
                  </Stack>
                </Box>
              )}
            </Box>
          </Collapse>
        )}
      </Alert>
    </Box>
  );
};

export default PermissionValidationAlert;
