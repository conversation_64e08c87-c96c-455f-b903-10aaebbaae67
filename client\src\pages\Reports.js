import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Grid,
  Box,
  TextField,
  Button,
  MenuItem,
  Tab,
  Tabs,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  FormControl,
  InputLabel,
  Select,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Pie } from 'react-chartjs-2';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { es } from 'date-fns/locale';
import {
  FileDownload as FileDownloadIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  AttachMoney as AttachMoneyIcon,
  People as PeopleIcon,
  Assignment as AssignmentIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../config';

// Registrar componentes de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const reportTypes = [
  // Reportes Ejecutivos
  { value: 'executive-dashboard', label: '📊 Dashboard Ejecutivo', category: 'executive' },
  { value: 'productivity', label: '⚡ Productividad por Equipo', category: 'executive' },
  { value: 'financial', label: '💰 Análisis Financiero', category: 'executive' },
  { value: 'performance', label: '🎯 Rendimiento General', category: 'executive' },

  // Reportes Técnicos
  { value: 'attendance', label: '⏰ Cumplimiento de Horarios', category: 'technical' },
  { value: 'efficiency', label: '📈 Calidad y Eficiencia', category: 'technical' },
  { value: 'workload', label: '⚖️ Distribución de Carga', category: 'technical' },
  { value: 'predictive', label: '🔮 Análisis Predictivo', category: 'technical' },

  // Reportes Básicos (existentes)
  { value: 'user', label: '👤 Por Usuario', category: 'basic' },
  { value: 'task', label: '📋 Por Tarea', category: 'basic' },
  { value: 'status', label: '📊 Por Estado', category: 'basic' },
  { value: 'time', label: '⏱️ Registro de Tiempo', category: 'basic' },

  // Reportes de Permisos
  { value: 'permissions-summary', label: '📋 Resumen de Permisos', category: 'basic' },
  { value: 'permissions-by-type', label: '📊 Permisos por Tipo', category: 'basic' },
  { value: 'permissions-by-user', label: '👤 Permisos por Usuario', category: 'basic' },
  { value: 'permissions-trends', label: '📈 Tendencias de Permisos', category: 'basic' },
];

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div role="tabpanel" hidden={value !== index} {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

// Componente para mostrar KPIs
const KPICard = ({ title, value, trend, icon, color = 'primary' }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography color="textSecondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h4" component="div">
            {value}
          </Typography>
          {trend && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              {trend > 0 ? (
                <TrendingUpIcon color="success" fontSize="small" />
              ) : (
                <TrendingDownIcon color="error" fontSize="small" />
              )}
              <Typography
                variant="body2"
                color={trend > 0 ? 'success.main' : 'error.main'}
                sx={{ ml: 0.5 }}
              >
                {Math.abs(trend)}%
              </Typography>
            </Box>
          )}
        </Box>
        <Box sx={{ color: `${color}.main` }}>
          {icon}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

// Componente para mostrar alertas
const AlertsPanel = ({ alerts }) => (
  <Card>
    <CardContent>
      <Typography variant="h6" gutterBottom>
        🚨 Alertas y Notificaciones
      </Typography>
      <List>
        {alerts?.map((alert, index) => (
          <ListItem key={index}>
            <ListItemIcon>
              {alert.type === 'warning' ? (
                <WarningIcon color="warning" />
              ) : (
                <CheckCircleIcon color="success" />
              )}
            </ListItemIcon>
            <ListItemText
              primary={alert.title}
              secondary={alert.description}
            />
            <Chip
              label={alert.priority}
              color={alert.priority === 'high' ? 'error' : 'warning'}
              size="small"
            />
          </ListItem>
        )) || (
          <ListItem>
            <ListItemText primary="No hay alertas activas" />
          </ListItem>
        )}
      </List>
    </CardContent>
  </Card>
);

const Reports = () => {
  const [tabValue, setTabValue] = useState(0);
  const [reportType, setReportType] = useState('executive-dashboard');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().setDate(1)), // Primer día del mes actual
    endDate: new Date(),
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [reportData, setReportData] = useState({
    barChart: null,
    pieChart: null,
    kpis: null,
    metrics: null,
    alerts: null,
    trends: null,
  });
  const [exportFormat, setExportFormat] = useState('pdf');
  const [selectedCategory, setSelectedCategory] = useState('executive');

  useEffect(() => {
    fetchReportData();
  }, [reportType, dateRange]);

  const fetchReportData = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.get(
        `${API_URL}/api/reports/${reportType}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
          params: {
            startDate: dateRange.startDate.toISOString(),
            endDate: dateRange.endDate.toISOString(),
          },
        }
      );

      const processedData = processReportData(response.data);
      setReportData(processedData);
    } catch (err) {
      console.error('Error al obtener datos del reporte:', err);
      console.error('Error details:', err.response?.data || err.message);
      console.error('Report type:', reportType);
      console.error('Request URL:', `${API_URL}/api/reports/${reportType}`);
      setError(`Error al cargar los datos del reporte: ${err.response?.data?.error || err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const processReportData = (data) => {
    console.log('🔍 PROCESANDO REPORTE TIPO:', reportType);
    console.log('📊 DATOS RECIBIDOS:', data);
    switch (reportType) {
      case 'executive-dashboard':
        return {
          kpis: data.kpis || {},
          metrics: data.metrics || {},
          alerts: data.alerts || [],
          trends: data.trends || {},
          barChart: data.productivityChart || null,
          pieChart: data.distributionChart || null,
        };
      case 'productivity':
        return {
          barChart: {
            labels: data.teams?.map(t => t.name) || [],
            datasets: [
              {
                label: 'Eficiencia (%)',
                data: data.teams?.map(t => t.efficiency) || [],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
              },
              {
                label: 'Cumplimiento (%)',
                data: data.teams?.map(t => t.compliance) || [],
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
              },
            ],
          },
          pieChart: {
            labels: data.teams?.map(t => t.name) || [],
            datasets: [{
              data: data.teams?.map(t => t.totalHours) || [],
              backgroundColor: [
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 99, 132, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(153, 102, 255, 0.5)',
              ],
            }],
          },
          metrics: data.metrics || {},
        };
      case 'financial':
        return {
          barChart: {
            labels: data.projects?.map(p => p.name) || [],
            datasets: [
              {
                label: 'Costo Real ($)',
                data: data.projects?.map(p => p.actualCost) || [],
                backgroundColor: 'rgba(255, 99, 132, 0.5)',
              },
              {
                label: 'Costo Estimado ($)',
                data: data.projects?.map(p => p.estimatedCost) || [],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
              },
            ],
          },
          pieChart: {
            labels: data.projects?.map(p => p.name) || [],
            datasets: [{
              data: data.projects?.map(p => p.roi) || [],
              backgroundColor: [
                'rgba(75, 192, 192, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(153, 102, 255, 0.5)',
                'rgba(255, 159, 64, 0.5)',
              ],
            }],
          },
          metrics: data.financial || {},
        };
      case 'performance':
        return {
          barChart: {
            labels: data.performance?.map(p => p.name) || [],
            datasets: [
              {
                label: 'Puntuación General',
                data: data.performance?.map(p => parseFloat(p.score)) || [],
                backgroundColor: 'rgba(153, 102, 255, 0.5)',
              },
              {
                label: 'Eficiencia (%)',
                data: data.performance?.map(p => parseFloat(p.efficiency)) || [],
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
              },
            ],
          },
          pieChart: {
            labels: data.performance?.map(p => p.name) || [],
            datasets: [{
              data: data.performance?.map(p => parseFloat(p.totalHours)) || [],
              backgroundColor: [
                'rgba(255, 99, 132, 0.5)',
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(153, 102, 255, 0.5)',
              ],
            }],
          },
          metrics: data.summary || {},
        };
      case 'attendance':
        return {
          barChart: {
            labels: data.attendance?.map(a => a.name) || [],
            datasets: [
              {
                label: 'Horas Regulares',
                data: data.attendance?.map(a => parseFloat(a.regularHours)) || [],
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
              },
              {
                label: 'Horas Extra',
                data: data.attendance?.map(a => parseFloat(a.overtimeHours)) || [],
                backgroundColor: 'rgba(255, 99, 132, 0.5)',
              },
            ],
          },
          pieChart: {
            labels: data.attendance?.map(a => a.name) || [],
            datasets: [{
              data: data.attendance?.map(a => parseFloat(a.totalHours)) || [],
              backgroundColor: [
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 99, 132, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
              ],
            }],
          },
          metrics: data.summary || {},
        };
      case 'efficiency':
        return {
          barChart: {
            labels: data.efficiency?.map(e => e.name) || [],
            datasets: [
              {
                label: 'Eficiencia de Tiempo (%)',
                data: data.efficiency?.map(e => parseFloat(e.timeEfficiency)) || [],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
              },
              {
                label: 'Eficiencia de Tareas (%)',
                data: data.efficiency?.map(e => parseFloat(e.taskEfficiency)) || [],
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
              },
            ],
          },
          pieChart: {
            labels: data.efficiency?.map(e => e.name) || [],
            datasets: [{
              data: data.efficiency?.map(e => parseFloat(e.overallScore)) || [],
              backgroundColor: [
                'rgba(255, 206, 86, 0.5)',
                'rgba(153, 102, 255, 0.5)',
                'rgba(255, 159, 64, 0.5)',
                'rgba(199, 199, 199, 0.5)',
              ],
            }],
          },
          metrics: data.summary || {},
        };
      case 'workload':
        return {
          barChart: {
            labels: data.workload?.map(w => w.name) || [],
            datasets: [
              {
                label: 'Utilización (%)',
                data: data.workload?.map(w => parseFloat(w.utilization)) || [],
                backgroundColor: data.workload?.map(w => {
                  const util = parseFloat(w.utilization);
                  if (util > 100) return 'rgba(255, 99, 132, 0.5)'; // Sobrecargado
                  if (util < 50) return 'rgba(255, 206, 86, 0.5)'; // Subcargado
                  return 'rgba(75, 192, 192, 0.5)'; // Normal
                }) || [],
              },
            ],
          },
          pieChart: {
            labels: data.workload?.map(w => w.name) || [],
            datasets: [{
              data: data.workload?.map(w => parseFloat(w.percentage)) || [],
              backgroundColor: [
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 99, 132, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(153, 102, 255, 0.5)',
              ],
            }],
          },
          metrics: data.summary || {},
        };
      case 'predictive':
        return {
          barChart: {
            labels: data.predictions?.map(p => p.name) || [],
            datasets: [
              {
                label: 'Horas Estimadas para Completar',
                data: data.predictions?.map(p => parseFloat(p.estimatedHoursToComplete)) || [],
                backgroundColor: data.predictions?.map(p => {
                  if (p.riskLevel === 'Alto') return 'rgba(255, 99, 132, 0.5)';
                  if (p.riskLevel === 'Medio') return 'rgba(255, 206, 86, 0.5)';
                  return 'rgba(75, 192, 192, 0.5)';
                }) || [],
              },
            ],
          },
          pieChart: {
            labels: ['Riesgo Alto', 'Riesgo Medio', 'Riesgo Bajo'],
            datasets: [{
              data: [
                data.predictions?.filter(p => p.riskLevel === 'Alto').length || 0,
                data.predictions?.filter(p => p.riskLevel === 'Medio').length || 0,
                data.predictions?.filter(p => p.riskLevel === 'Bajo').length || 0,
              ],
              backgroundColor: [
                'rgba(255, 99, 132, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
              ],
            }],
          },
          metrics: data.insights || {},
        };
      case 'user':
        return {
          barChart: {
            labels: data.users?.map(u => u.name) || [],
            datasets: [
              {
                label: 'Horas Manuales',
                data: data.users?.map(u => (u.regularHours + u.overtimeHours)) || [],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
              },
              {
                label: 'Horas Kanban',
                data: data.users?.map(u => u.kanbanHours) || [],
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
              },
            ],
          },
          pieChart: {
            labels: data.users?.map(u => u.name) || [],
            datasets: [{
              data: data.users?.map(u => u.totalHours) || [],
              backgroundColor: [
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 99, 132, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(153, 102, 255, 0.5)',
              ],
            }],
          },
          metrics: data.summary || {},
          tableData: {
            headers: ['Usuario', 'Horas Manuales', 'Horas Kanban', 'Total Horas', 'Registros', 'Tareas'],
            rows: data.users?.map(u => [
              u.name,
              `${(u.regularHours + u.overtimeHours).toFixed(2)}h`,
              `${u.kanbanHours.toFixed(2)}h`,
              `${u.totalHours.toFixed(2)}h`,
              u.entriesCount || 0,
              u.kanbanTasksCount || 0
            ]) || []
          }
        };
        break;
      case 'task':
        return {
          barChart: {
            labels: data.tasks?.map(t => t.title) || [],
            datasets: [
              {
                label: 'Horas Manuales',
                data: data.tasks?.map(t => t.manualHours) || [],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
              },
              {
                label: 'Horas Kanban',
                data: data.tasks?.map(t => t.kanbanHours) || [],
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
              },
            ],
          },
          pieChart: {
            labels: data.tasks?.map(t => t.title) || [],
            datasets: [{
              data: data.tasks?.map(t => t.totalHours) || [],
              backgroundColor: [
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 99, 132, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(153, 102, 255, 0.5)',
              ],
            }],
          },
          metrics: data.summary || {},
          tableData: {
            headers: ['Tarea', 'Horas Manuales', 'Horas Kanban', 'Total Horas', 'Registros', 'Tipo'],
            rows: data.tasks?.map(t => [
              t.title,
              `${t.manualHours.toFixed(2)}h`,
              `${t.kanbanHours.toFixed(2)}h`,
              `${t.totalHours.toFixed(2)}h`,
              t.manualEntries || 0,
              t.isKanbanTask ? 'Kanban' : 'Manual'
            ]) || []
          }
        };
        break;
      case 'status':
        return {
          barChart: {
            labels: ['Pendiente', 'Aprobado', 'Rechazado'],
            datasets: [{
              label: 'Cantidad de Registros',
              data: [
                data.statusCount.PENDING || 0,
                data.statusCount.APPROVED || 0,
                data.statusCount.REJECTED || 0,
              ],
              backgroundColor: [
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(255, 99, 132, 0.5)',
              ],
            }],
          },
          pieChart: {
            labels: ['Pendiente', 'Aprobado', 'Rechazado'],
            datasets: [{
              data: [
                data.statusCount.PENDING || 0,
                data.statusCount.APPROVED || 0,
                data.statusCount.REJECTED || 0,
              ],
              backgroundColor: [
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(255, 99, 132, 0.5)',
              ],
            }],
          },
          metrics: {
            totalEntries: (data.timeEntriesCount || 0) + (data.permissionsCount || 0),
            timeEntries: data.timeEntriesCount || 0,
            permissions: data.permissionsCount || 0,
            approvalRate: data.statusCount ? ((data.statusCount.APPROVED || 0) / ((data.statusCount.PENDING || 0) + (data.statusCount.APPROVED || 0) + (data.statusCount.REJECTED || 0)) * 100).toFixed(1) : 0
          }
        };
        break;
      case 'time':
        return {
          barChart: {
            labels: data.dailyStats?.map(d => new Date(d.date).toLocaleDateString()) || [],
            datasets: [
              {
                label: 'Horas Manuales',
                data: data.dailyStats?.map(d => d.manualHours) || [],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
              },
              {
                label: 'Horas Kanban',
                data: data.dailyStats?.map(d => d.kanbanHours) || [],
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
              },
            ],
          },
          pieChart: {
            labels: ['Registros Manuales', 'Tareas Kanban'],
            datasets: [{
              data: [
                data.summary?.totalManualHours || 0,
                data.summary?.totalKanbanHours || 0
              ],
              backgroundColor: [
                'rgba(54, 162, 235, 0.5)',
                'rgba(75, 192, 192, 0.5)',
              ],
            }],
          },
          metrics: data.summary || {},
          tableData: {
            headers: ['Fecha/Hora', 'Título', 'Usuario', 'Horas', 'Tipo', 'Estado'],
            rows: data.timeEntries?.map(entry => [
              new Date(entry.startTime).toLocaleString(),
              entry.title,
              entry.user?.name || 'N/A',
              `${entry.hoursWorked}h`,
              entry.type === 'manual' ? 'Manual' : 'Kanban',
              entry.status === 'APPROVED' ? 'Aprobado' : entry.status === 'ARCHIVED' ? 'Archivado' : entry.status
            ]) || []
          }
        };
        break;

      // Reportes de Permisos
      case 'permissions-summary':
        return {
          barChart: {
            labels: ['Pendientes', 'Aprobados', 'Rechazados'],
            datasets: [{
              label: 'Cantidad de Permisos',
              data: [
                data.summary?.pending || 0,
                data.summary?.approved || 0,
                data.summary?.rejected || 0
              ],
              backgroundColor: [
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(255, 99, 132, 0.5)',
              ],
            }],
          },
          pieChart: {
            labels: data.byType?.map(t => t.type) || [],
            datasets: [{
              data: data.byType?.map(t => t.count) || [],
              backgroundColor: [
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 99, 132, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(153, 102, 255, 0.5)',
              ],
            }],
          },
          metrics: {
            totalPermissions: data.summary?.total || 0,
            approvalRate: data.summary?.approvalRate || 0,
            averageDays: data.summary?.averageDays || 0,
            mostRequestedType: data.summary?.mostRequestedType || 'N/A'
          },
          tableData: {
            headers: ['Usuario', 'Tipo', 'Fecha Inicio', 'Fecha Fin', 'Estado', 'Días'],
            rows: data.permissions?.map(p => [
              p.user?.name || 'N/A',
              p.type,
              new Date(p.startTime).toLocaleDateString(),
              new Date(p.endTime).toLocaleDateString(),
              p.status === 'PENDING' ? 'Pendiente' : p.status === 'APPROVED' ? 'Aprobado' : 'Rechazado',
              p.days || 0
            ]) || []
          }
        };
        break;

      case 'permissions-by-type':
        return {
          barChart: {
            labels: data.byType?.map(t => t.type) || [],
            datasets: [{
              label: 'Cantidad de Permisos',
              data: data.byType?.map(t => t.count) || [],
              backgroundColor: 'rgba(54, 162, 235, 0.5)',
            }],
          },
          pieChart: {
            labels: data.byType?.map(t => t.type) || [],
            datasets: [{
              data: data.byType?.map(t => t.totalDays) || [],
              backgroundColor: [
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 99, 132, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(153, 102, 255, 0.5)',
              ],
            }],
          },
          metrics: data.summary || {},
          tableData: {
            headers: ['Tipo de Permiso', 'Cantidad', 'Total Días', 'Promedio Días', 'Tasa Aprobación'],
            rows: data.byType?.map(t => [
              t.type,
              t.count,
              t.totalDays,
              t.averageDays?.toFixed(1) || 0,
              `${t.approvalRate?.toFixed(1) || 0}%`
            ]) || []
          }
        };
        break;

      case 'permissions-by-user':
        return {
          barChart: {
            labels: data.byUser?.map(u => u.name) || [],
            datasets: [{
              label: 'Días de Permiso',
              data: data.byUser?.map(u => u.totalDays) || [],
              backgroundColor: 'rgba(75, 192, 192, 0.5)',
            }],
          },
          pieChart: {
            labels: data.byUser?.map(u => u.name) || [],
            datasets: [{
              data: data.byUser?.map(u => u.count) || [],
              backgroundColor: [
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 99, 132, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(153, 102, 255, 0.5)',
              ],
            }],
          },
          metrics: data.summary || {},
          tableData: {
            headers: ['Usuario', 'Total Permisos', 'Días Utilizados', 'Pendientes', 'Aprobados', 'Rechazados'],
            rows: data.byUser?.map(u => [
              u.name,
              u.count,
              u.totalDays,
              u.pending || 0,
              u.approved || 0,
              u.rejected || 0
            ]) || []
          }
        };
        break;

      case 'permissions-trends':
        return {
          barChart: {
            labels: data.trends?.map(t => t.month) || [],
            datasets: [{
              label: 'Permisos por Mes',
              data: data.trends?.map(t => t.count) || [],
              backgroundColor: 'rgba(153, 102, 255, 0.5)',
            }],
          },
          pieChart: {
            labels: data.seasonal?.map(s => s.season) || [],
            datasets: [{
              data: data.seasonal?.map(s => s.count) || [],
              backgroundColor: [
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(255, 99, 132, 0.5)',
                'rgba(54, 162, 235, 0.5)',
              ],
            }],
          },
          metrics: data.summary || {},
          tableData: {
            headers: ['Período', 'Total Permisos', 'Días Promedio', 'Tipo Más Común'],
            rows: data.trends?.map(t => [
              t.month,
              t.count,
              t.averageDays?.toFixed(1) || 0,
              t.mostCommonType || 'N/A'
            ]) || []
          }
        };
        break;

      default:
        return {
          barChart: null,
          pieChart: null,
        };
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const generateReport = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        startDate: dateRange.startDate.toISOString(),
        endDate: dateRange.endDate.toISOString(),
      });

      console.log('🔍 GENERANDO REPORTE:', {
        type: reportType,
        category: selectedCategory,
        dateRange: {
          start: dateRange.startDate.toISOString(),
          end: dateRange.endDate.toISOString()
        }
      });

      const response = await axios.get(
        `${API_URL}/api/reports/${reportType}?${params}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );

      console.log('📊 DATOS DEL REPORTE RECIBIDOS:', response.data);
      console.log('🔄 PROCESANDO REPORTE TIPO:', reportType);

      const processedData = processReportData(response.data);
      console.log('✅ DATOS PROCESADOS:', processedData);
      setReportData(processedData);

      console.log('✅ REPORTE PROCESADO EXITOSAMENTE:', processedData);
    } catch (err) {
      console.error('❌ ERROR AL GENERAR REPORTE:', err);
      console.error('Response data:', err.response?.data);
      setError(`Error al generar el reporte: ${err.response?.data?.error || err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${API_URL}/api/reports/${reportType}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
          params: {
            startDate: dateRange.startDate.toISOString(),
            endDate: dateRange.endDate.toISOString(),
            format: exportFormat,
          },
          responseType: 'blob',
        }
      );

      // Crear URL del blob y descargar
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `reporte-${reportType}.${exportFormat}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error al exportar reporte:', err);
      setError('Error al exportar el reporte');
    } finally {
      setLoading(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={es}>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Paper sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h4" component="h1">
              Reportes
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                select
                label="Formato de exportación"
                value={exportFormat}
                onChange={(e) => setExportFormat(e.target.value)}
                sx={{ width: 150 }}
              >
                <MenuItem value="pdf">PDF</MenuItem>
                <MenuItem value="xlsx">Excel</MenuItem>
              </TextField>
              <Button
                variant="contained"
                color="primary"
                onClick={generateReport}
                disabled={loading}
                sx={{ mr: 1 }}
              >
                Generar Reporte
              </Button>
              <Button
                variant="outlined"
                startIcon={<FileDownloadIcon />}
                onClick={handleExport}
                disabled={loading || !reportData.barChart}
              >
                Exportar
              </Button>
            </Box>
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Categoría</InputLabel>
                <Select
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value);
                    // Seleccionar el primer reporte de la nueva categoría
                    const firstReport = reportTypes.find(r => r.category === e.target.value);
                    if (firstReport) {
                      setReportType(firstReport.value);
                    }
                  }}
                >
                  <MenuItem value="executive">📊 Reportes Ejecutivos</MenuItem>
                  <MenuItem value="technical">🔧 Reportes Técnicos</MenuItem>
                  <MenuItem value="basic">📋 Reportes Básicos</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                select
                fullWidth
                label="Tipo de reporte"
                value={reportType}
                onChange={(e) => setReportType(e.target.value)}
              >
                {reportTypes
                  .filter(type => type.category === selectedCategory)
                  .map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
              </TextField>
            </Grid>
            <Grid item xs={12} md={3}>
              <DatePicker
                label="Fecha inicio"
                value={dateRange.startDate}
                onChange={(newValue) =>
                  setDateRange((prev) => ({ ...prev, startDate: newValue }))
                }
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <DatePicker
                label="Fecha fin"
                value={dateRange.endDate}
                onChange={(newValue) =>
                  setDateRange((prev) => ({ ...prev, endDate: newValue }))
                }
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
          </Grid>

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {/* Dashboard Ejecutivo */}
              {reportType === 'executive-dashboard' && (
                <Box sx={{ mt: 4 }}>
                  {/* KPIs principales */}
                  <Grid container spacing={3} sx={{ mb: 4 }}>
                    <Grid item xs={12} sm={6} md={3}>
                      <KPICard
                        title="Productividad General"
                        value={reportData.kpis?.productivity || "85%"}
                        trend={reportData.kpis?.productivityTrend || 5.2}
                        icon={<TrendingUpIcon fontSize="large" />}
                        color="success"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <KPICard
                        title="Horas Trabajadas"
                        value={reportData.kpis?.totalHours || "1,240h"}
                        trend={reportData.kpis?.hoursTrend || -2.1}
                        icon={<ScheduleIcon fontSize="large" />}
                        color="primary"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <KPICard
                        title="Costo Total"
                        value={reportData.kpis?.totalCost || "$45,600"}
                        trend={reportData.kpis?.costTrend || 3.8}
                        icon={<AttachMoneyIcon fontSize="large" />}
                        color="warning"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <KPICard
                        title="Empleados Activos"
                        value={reportData.kpis?.activeEmployees || "24"}
                        trend={reportData.kpis?.employeesTrend || 0}
                        icon={<PeopleIcon fontSize="large" />}
                        color="info"
                      />
                    </Grid>
                  </Grid>

                  {/* Alertas y gráficos */}
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={4}>
                      <AlertsPanel alerts={reportData.alerts} />
                    </Grid>
                    <Grid item xs={12} md={8}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            📊 Productividad por Equipo
                          </Typography>
                          {reportData.barChart && (
                            <Box sx={{ height: 300 }}>
                              <Bar
                                data={reportData.barChart}
                                options={{
                                  responsive: true,
                                  maintainAspectRatio: false,
                                  plugins: {
                                    legend: {
                                      position: 'top',
                                    },
                                  },
                                }}
                              />
                            </Box>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {/* Reportes estándar con tabs */}
              {reportType !== 'executive-dashboard' && (
                <>
                  <Box sx={{ mt: 4 }}>
                    <Tabs value={tabValue} onChange={handleTabChange} centered>
                      <Tab label="Gráfico de Barras" />
                      <Tab label="Gráfico Circular" />
                      {(reportType === 'user' || reportType === 'task' ||
                        reportType === 'status' || reportType === 'time' ||
                        reportType === 'permissions-summary' || reportType === 'permissions-by-type' ||
                        reportType === 'permissions-by-user' || reportType === 'permissions-trends') && (
                        <Tab label="Tabla de Datos" />
                      )}
                      {(reportType === 'productivity' || reportType === 'financial' ||
                        reportType === 'performance' || reportType === 'attendance' ||
                        reportType === 'efficiency' || reportType === 'workload' ||
                        reportType === 'predictive') && (
                        <Tab label="Métricas Detalladas" />
                      )}
                    </Tabs>
                  </Box>

                  <TabPanel value={tabValue} index={0}>
                    {reportData.barChart && (
                      <Box sx={{ height: 400 }}>
                        <Bar
                          data={reportData.barChart}
                          options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                              legend: {
                                position: 'top',
                              },
                            },
                          }}
                        />
                      </Box>
                    )}
                  </TabPanel>

                  <TabPanel value={tabValue} index={1}>
                    {reportData.pieChart && (
                      <Box sx={{ height: 400 }}>
                        <Pie
                          data={reportData.pieChart}
                          options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                              legend: {
                                position: 'top',
                              },
                            },
                          }}
                        />
                      </Box>
                    )}
                  </TabPanel>

                  {/* Pestaña de Tabla de Datos para reportes básicos y permisos */}
                  {(reportType === 'user' || reportType === 'task' ||
                    reportType === 'status' || reportType === 'time' ||
                    reportType === 'permissions-summary' || reportType === 'permissions-by-type' ||
                    reportType === 'permissions-by-user' || reportType === 'permissions-trends') && (
                    <TabPanel value={tabValue} index={2}>
                      {reportData.tableData && (
                        <TableContainer component={Paper}>
                          <Table>
                            <TableHead>
                              <TableRow>
                                {reportData.tableData.headers.map((header, index) => (
                                  <TableCell key={index} sx={{ fontWeight: 'bold' }}>
                                    {header}
                                  </TableCell>
                                ))}
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {reportData.tableData.rows.map((row, rowIndex) => (
                                <TableRow key={rowIndex}>
                                  {row.map((cell, cellIndex) => (
                                    <TableCell key={cellIndex}>
                                      {cell}
                                    </TableCell>
                                  ))}
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )}
                    </TabPanel>
                  )}

                  {/* Pestaña de Métricas para reportes avanzados */}
                  {(reportType === 'productivity' || reportType === 'financial' ||
                    reportType === 'performance' || reportType === 'attendance' ||
                    reportType === 'efficiency' || reportType === 'workload' ||
                    reportType === 'predictive') && (
                    <TabPanel value={tabValue} index={3}>
                      {reportData.metrics && (
                        <Grid container spacing={3}>
                          {Object.entries(reportData.metrics).map(([key, value]) => (
                            <Grid item xs={12} sm={6} md={4} key={key}>
                              <Card>
                                <CardContent>
                                  <Typography color="textSecondary" gutterBottom>
                                    {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                                  </Typography>
                                  <Typography variant="h5" component="div">
                                    {value}
                                  </Typography>
                                </CardContent>
                              </Card>
                            </Grid>
                          ))}
                        </Grid>
                      )}
                    </TabPanel>
                  )}
                </>
              )}
            </>
          )}
        </Paper>
      </Container>
    </LocalizationProvider>
  );
};

export default Reports;
