const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testMultipliers() {
  try {
    console.log('🔍 VERIFICANDO MULTIPLICADORES...');
    
    // Verificar multiplicadores existentes
    const multipliers = await prisma.timeMultiplier.findMany();
    console.log(`📋 Multiplicadores encontrados: ${multipliers.length}`);
    
    multipliers.forEach(mult => {
      console.log(`  - ${mult.name}: ${mult.value}x (${mult.startTime || 'N/A'} - ${mult.endTime || 'N/A'})`);
    });
    
    // Verificar horarios de trabajo
    const schedules = await prisma.workSchedule.findMany();
    console.log(`\n📅 Horarios de trabajo encontrados: ${schedules.length}`);
    
    const dayNames = ['Domingo', 'Lunes', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'];
    schedules.forEach(schedule => {
      console.log(`  - ${dayNames[schedule.dayOfWeek]}: ${schedule.startTime}-${schedule.endTime} (${schedule.multiplier}x)`);
    });
    
    // Verificar feriados
    const holidays = await prisma.holiday.findMany();
    console.log(`\n🎉 Feriados encontrados: ${holidays.length}`);
    
    holidays.forEach(holiday => {
      console.log(`  - ${holiday.name}: ${holiday.date.toISOString().split('T')[0]} (${holiday.multiplier}x)`);
    });
    
    console.log('\n✅ VERIFICACIÓN COMPLETADA');
    
  } catch (error) {
    console.error('❌ Error en verificación:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testMultipliers();
