const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function verifyMultiplierConfig() {
  try {
    console.log('🔍 VERIFICANDO CONFIGURACIÓN DE MULTIPLICADORES...\n');

    // 1. Verificar multiplicadores activos
    const timeMultipliers = await prisma.timeMultiplier.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    });

    console.log(`📊 MULTIPLICADORES ACTIVOS ENCONTRADOS: ${timeMultipliers.length}`);
    timeMultipliers.forEach(mult => {
      console.log(`  - ${mult.name}: ${mult.value}x ${mult.startTime ? `(${mult.startTime}-${mult.endTime})` : '(sin horario específico)'}`);
    });

    // 2. Verificar horarios de trabajo
    const workSchedules = await prisma.workSchedule.findMany({
      orderBy: { dayOfWeek: 'asc' }
    });

    console.log(`\n📅 HORARIOS DE TRABAJO ENCONTRADOS: ${workSchedules.length}`);
    const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
    workSchedules.forEach(schedule => {
      console.log(`  - ${dayNames[schedule.dayOfWeek]}: ${schedule.startTime}-${schedule.endTime} (${schedule.multiplier}x)`);
    });

    // 3. Verificar si existe multiplicador para feriados/domingos
    const holidayMultiplier = timeMultipliers.find(m => 
      m.name.toLowerCase().includes('feriado') || 
      m.name.toLowerCase().includes('holiday') ||
      m.value === 2.0
    );

    console.log(`\n🎉 MULTIPLICADOR DE FERIADOS: ${holidayMultiplier ? `${holidayMultiplier.name} (${holidayMultiplier.value}x)` : 'NO ENCONTRADO'}`);

    // 4. Verificar configuración del domingo
    const sundaySchedule = workSchedules.find(s => s.dayOfWeek === 0);
    console.log(`\n🌅 CONFIGURACIÓN DEL DOMINGO: ${sundaySchedule ? `${sundaySchedule.startTime}-${sundaySchedule.endTime} (${sundaySchedule.multiplier}x)` : 'NO CONFIGURADO'}`);

    // 5. Verificar entradas de tiempo del domingo para testing
    const sundayEntries = await prisma.timeEntry.findMany({
      where: {
        status: 'APPROVED',
        startTime: {
          gte: new Date('2024-01-01'),
        }
      },
      include: {
        user: {
          select: { name: true }
        }
      }
    });

    const sundayTimeEntries = sundayEntries.filter(entry => {
      const entryDate = new Date(entry.startTime);
      return entryDate.getDay() === 0; // Domingo
    });

    console.log(`\n📝 ENTRADAS DE TIEMPO EN DOMINGO: ${sundayTimeEntries.length}`);
    sundayTimeEntries.slice(0, 3).forEach(entry => {
      const entryDate = new Date(entry.startTime);
      console.log(`  - ${entry.user.name}: ${entry.title} (${entryDate.toISOString().split('T')[0]}) - ${entry.hoursWorked}h`);
    });

    console.log('\n✅ VERIFICACIÓN COMPLETADA');

  } catch (error) {
    console.error('❌ ERROR EN VERIFICACIÓN:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  verifyMultiplierConfig();
}

module.exports = { verifyMultiplierConfig };
