version: '3.8'
services:
  db:
    image: postgres:15-alpine
    container_name: proyectoweb-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: worktrack
    ports:
      - "5432:5432"

    volumes:
      - postgres_data:/var/lib/postgresql/data

  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: proyectoweb-backend
    network_mode: host  # Usar la red del host para acceso directo a redes privadas
    environment:
      - DATABASE_URL=postgresql://postgres:postgres@localhost:5432/worktrack
      - JWT_SECRET=your_jwt_secret_key
      - PORT=5000
      - NODE_ENV=development
      - LDAP_URL=ldap://localhost:389
      - LDAP_BIND_DN=cn=admin,dc=example,dc=com
      - LDAP_BIND_PASSWORD=admin
      - LDAP_SEARCH_BASE=dc=example,dc=com
      - LDAP_USER_FILTER=(objectClass=person)
      - API_KEY_ENCRYPTION_KEY=your-32-char-secret-key-here-123
    # ports: # No se necesitan con network_mode: host
    #   - "0.0.0.0:5000:5000"
    #   - "0.0.0.0:5555:5555"
    # extra_hosts: # No se necesitan con network_mode: host
    depends_on:
      - db
      - openldap
    volumes:
      - ./server:/app
      - /app/node_modules
    restart: unless-stopped

  frontend:
    build: 
      context: ./client
      dockerfile: Dockerfile
    container_name: proyectoweb-frontend
    ports:
      - "0.0.0.0:3000:3000"
    environment:
      - REACT_APP_API_URL=http://************:5000
    volumes:
      - ./client:/app
      - /app/node_modules
    depends_on:
      - backend
    restart: unless-stopped

  openldap:
    image: osixia/openldap:1.5.0
    environment:
      - LDAP_ORGANISATION=Example Inc
      - LDAP_DOMAIN=example.com
      - LDAP_ADMIN_PASSWORD=admin
    ports:
      - "389:389"
      - "636:636"
    volumes:
      - ldap_data:/var/lib/ldap
      - ldap_config:/etc/ldap/slapd.d

volumes:
  postgres_data:
  ldap_data:
  ldap_config:
