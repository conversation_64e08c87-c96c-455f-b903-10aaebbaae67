import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  Stack,
  TextField,
  MenuItem,
  Box,
  Snackbar,
  Alert,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import axios from 'axios';
import DeleteIcon from '@mui/icons-material/Delete';
import { API_URL } from '../config';

const TimeApproval = () => {
  const [timeEntries, setTimeEntries] = useState([]);
  const [filteredEntries, setFilteredEntries] = useState([]);
  const [filters, setFilters] = useState({
    status: 'PENDING',
    search: '',
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedEntryId, setSelectedEntryId] = useState(null);

  useEffect(() => {
    fetchTimeEntries();
  }, []);

  useEffect(() => {
    filterTimeEntries();
  }, [timeEntries, filters]);

  const fetchTimeEntries = async (statusFilter = 'PENDING') => {
    try {
      const params = new URLSearchParams();
      if (statusFilter && statusFilter !== 'ALL') {
        params.append('status', statusFilter);
      }

      const url = `${API_URL}/api/time-entries/for-approval${params.toString() ? `?${params.toString()}` : ''}`;
      console.log('Consultando URL:', url);

      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      console.log('Registros de tiempo obtenidos:', response.data);
      setTimeEntries(response.data);

      if (response.data.length === 0) {
        setSnackbar({
          open: true,
          message: `No hay registros de tiempo ${statusFilter === 'ALL' ? '' : statusFilter.toLowerCase()} para mostrar`,
          severity: 'info',
        });
      }
    } catch (error) {
      console.error('Error al obtener registros:', error);
      setSnackbar({
        open: true,
        message: `Error al cargar los registros de tiempo: ${error.response?.status === 404 ? 'Ruta no encontrada' : error.message}`,
        severity: 'error',
      });
    }
  };

  const filterTimeEntries = () => {
    let filtered = [...timeEntries];

    if (filters.status !== 'ALL') {
      filtered = filtered.filter(entry => entry.status === filters.status);
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(
        entry =>
          entry.user.name.toLowerCase().includes(searchLower) ||
          entry.title.toLowerCase().includes(searchLower) ||
          entry.description?.toLowerCase().includes(searchLower)
      );
    }

    setFilteredEntries(filtered);
  };

  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prev => ({
      ...prev,
      [name]: value,
    }));

    // Si cambió el filtro de estado, recargar datos
    if (name === 'status') {
      fetchTimeEntries(value);
    }
  };

  const handleStatusChange = async (entryId, newStatus) => {
    try {
      await axios.patch(
        `${API_URL}/api/time-entries/${entryId}/approve`,
        { status: newStatus },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );

      setTimeEntries(entries =>
        entries.map(entry =>
          entry.id === entryId ? { ...entry, status: newStatus } : entry
        )
      );

      setSnackbar({
        open: true,
        message: `Registro ${newStatus === 'APPROVED' ? 'aprobado' : 'rechazado'} correctamente`,
        severity: 'success',
      });
    } catch (error) {
      console.error('Error al actualizar estado:', error);
      setSnackbar({
        open: true,
        message: 'Error al actualizar el estado del registro',
        severity: 'error',
      });
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'APPROVED':
        return 'success';
      case 'REJECTED':
        return 'error';
      default:
        return 'warning';
    }
  };

  const handleDeleteEntry = async (entryId) => {
    try {
      console.log('Intentando eliminar registro:', entryId);
      
      // Usar la URL de la API desde la configuración
      const url = `${API_URL}/api/time-entries/${entryId}`;
      
      console.log('URL de eliminación:', url);
      
      const response = await axios.delete(
        url,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );

      console.log('Respuesta de eliminación:', response.data);

      // Actualizar la lista de registros
      setTimeEntries(entries =>
        entries.filter(entry => entry.id !== entryId)
      );

      // Actualizar también la lista filtrada
      setFilteredEntries(entries =>
        entries.filter(entry => entry.id !== entryId)
      );

      setSnackbar({
        open: true,
        message: 'Registro eliminado correctamente',
        severity: 'success',
      });
      
      // Cerrar el diálogo de confirmación
      setDeleteDialogOpen(false);
      
      // Opcional: Recargar la lista completa para asegurar sincronización
      fetchTimeEntries(filters.status);
    } catch (error) {
      console.error('Error al eliminar registro:', error);
      
      // Mostrar mensaje de error más detallado
      const errorMessage = error.response?.data?.error || error.message || 'Error desconocido';
      
      setSnackbar({
        open: true,
        message: `Error al eliminar el registro de tiempo: ${errorMessage}`,
        severity: 'error',
      });
    }
  };

  const openDeleteDialog = (entryId) => {
    setSelectedEntryId(entryId);
    setDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setSelectedEntryId(null);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Aprobación de Registros de Tiempo
      </Typography>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
          <TextField
            label="Buscar"
            name="search"
            value={filters.search}
            onChange={handleFilterChange}
            size="small"
            sx={{ width: 300 }}
          />
          <TextField
            select
            label="Estado"
            name="status"
            value={filters.status}
            onChange={handleFilterChange}
            size="small"
            sx={{ width: 200 }}
          >
            <MenuItem value="ALL">Todos</MenuItem>
            <MenuItem value="PENDING">Pendientes</MenuItem>
            <MenuItem value="APPROVED">Aprobados</MenuItem>
            <MenuItem value="REJECTED">Rechazados</MenuItem>
          </TextField>
        </Stack>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Empleado</TableCell>
                <TableCell>Título</TableCell>
                <TableCell>Tarea</TableCell>
                <TableCell>Fecha</TableCell>
                <TableCell>Horas</TableCell>
                <TableCell>Estado</TableCell>
                <TableCell>Acciones</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredEntries.length > 0 ? (
                filteredEntries.map((entry) => (
                  <TableRow key={entry.id}>
                    <TableCell>{entry.user.name}</TableCell>
                    <TableCell>{entry.title}</TableCell>
                    <TableCell>{entry.task?.title || 'N/A'}</TableCell>
                    <TableCell>
                      {format(new Date(entry.startTime), 'dd/MM/yyyy HH:mm', { locale: es })}
                    </TableCell>
                    <TableCell>{entry.hoursWorked}</TableCell>
                    <TableCell>
                      <Chip
                        label={entry.status}
                        color={getStatusColor(entry.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        {entry.status === 'PENDING' && (
                          <>
                            <Button
                              size="small"
                              variant="contained"
                              color="success"
                              onClick={() => handleStatusChange(entry.id, 'APPROVED')}
                            >
                              Aprobar
                            </Button>
                            <Button
                              size="small"
                              variant="contained"
                              color="error"
                              onClick={() => handleStatusChange(entry.id, 'REJECTED')}
                            >
                              Rechazar
                            </Button>
                          </>
                        )}
                        {/* Botón de eliminar para cualquier estado */}
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => openDeleteDialog(entry.id)}
                          title="Eliminar registro"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    No hay registros de tiempo que coincidan con los filtros
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Diálogo de confirmación para eliminar */}
      <Dialog
        open={deleteDialogOpen}
        onClose={closeDeleteDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          Confirmar eliminación
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            ¿Estás seguro de que deseas eliminar este registro de tiempo? Esta acción no se puede deshacer.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDeleteDialog} color="primary">
            Cancelar
          </Button>
          <Button 
            onClick={() => handleDeleteEntry(selectedEntryId)} 
            color="error" 
            autoFocus
          >
            Eliminar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar para notificaciones */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          onClose={() => setSnackbar({ ...snackbar, open: false })} 
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default TimeApproval;



