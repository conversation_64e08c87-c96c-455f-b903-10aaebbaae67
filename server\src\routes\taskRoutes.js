const express = require('express');
const router = express.Router();
const taskController = require('../controllers/taskController');
const kanbanController = require('../controllers/kanbanController');
const { authenticateToken } = require('../middleware/auth');

// Aplicar middleware de autenticación a todas las rutas
router.use(authenticateToken);

// Rutas básicas para tareas (usando kanbanController para CRUD)
router.get('/', kanbanController.getKanbanBoard);
router.post('/', kanbanController.createTask);
router.get('/archived', kanbanController.getArchivedTasks);
router.put('/:id', kanbanController.updateTask);
router.delete('/:id', kanbanController.deleteTask);
router.patch('/:id/status', kanbanController.updateTaskStatus);

// Ruta para obtener tareas completadas por rango de fechas
router.get('/completed-by-date', taskController.getCompletedTasksByDate);

// Ruta para obtener tareas kanban archivadas con tiempos (para calendario)
router.get('/archived-kanban-tasks', taskController.getArchivedKanbanTasks);

module.exports = router;


