const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  try {
    // Crear usuario administrador
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    const admin = await prisma.user.create({
      data: {
        name: 'Administrador',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN'
      }
    });
    
    console.log('Usuario administrador creado:', admin);
    
    // Crear usuario regular
    const hashedPasswordUser = await bcrypt.hash('user123', 10);
    
    const user = await prisma.user.create({
      data: {
        name: 'Usuario Regular',
        email: '<EMAIL>',
        password: hashedPasswordUser,
        role: 'USER'
      }
    });
    
    console.log('Usuario regular creado:', user);
    
    // Crear proyecto de ejemplo
    const project = await prisma.project.create({
      data: {
        name: 'Proyecto de ejemplo',
        description: 'Este es un proyecto de ejemplo para probar la aplicación',
        status: 'ACTIVE'
      }
    });
    
    console.log('Proyecto de ejemplo creado:', project);
    
    // Crear tarea de ejemplo
    const task = await prisma.task.create({
      data: {
        title: 'Tarea de ejemplo',
        description: 'Esta es una tarea de ejemplo para probar la aplicación',
        status: 'TO_DO',
        priority: 'MEDIUM',
        estimatedHours: 8,
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 días desde ahora
        creatorId: admin.id,
        assigneeId: user.id,
        projectId: project.id
      }
    });
    
    console.log('Tarea de ejemplo creada:', task);
    
    console.log('Base de datos inicializada correctamente');
  } catch (error) {
    console.error('Error al inicializar la base de datos:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
