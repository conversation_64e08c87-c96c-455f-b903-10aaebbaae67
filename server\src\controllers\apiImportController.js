const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');
const apiExecutionService = require('../services/apiExecutionService_simple');

const prisma = new PrismaClient();

// Clave para desencriptar API keys
const ENCRYPTION_KEY = process.env.API_KEY_ENCRYPTION_KEY || 'your-32-char-secret-key-here-123';

const decrypt = (encryptedText) => {
  const decipher = crypto.createDecipher('aes-256-cbc', ENCRYPTION_KEY);
  let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};

// Importar datos del usuario desde todas las integraciones configuradas
exports.importUserData = async (req, res) => {
  const startTime = Date.now();
  const userId = req.user.id;
  const { targetMenu } = req.body;

  try {
    console.log(`🔄 Iniciando importación para usuario ${req.user.name} (${req.user.email})`);

    // Obtener todas las integraciones activas
    const integrations = await prisma.apiIntegration.findMany({
      where: {
        isActive: true
      },
      include: {
        userApiKeys: {
          where: {
            userId: userId,
            isActive: true
          }
        }
      }
    });

    if (integrations.length === 0) {
      return res.json({
        success: true,
        message: 'No hay integraciones configuradas',
        tasksCreated: 0,
        results: []
      });
    }

    let totalTasksCreated = 0;
    let totalTasksUpdated = 0;
    const results = [];

    for (const integration of integrations) {
      // Verificar que el usuario tenga API key configurada
      const userApiKey = integration.userApiKeys[0];
      if (!userApiKey) {
        console.log(`⚠️ Usuario ${req.user.email} no tiene API key para ${integration.name}`);
        continue;
      }

      // Filtrar endpoints por menú objetivo
      const targetEndpoints = integration.endpoints.filter(ep => ep.targetMenu === targetMenu);
      
      for (const endpoint of targetEndpoints) {
        try {
          console.log(`🔗 Ejecutando endpoint: ${endpoint.name} para ${integration.name}`);

          // Desencriptar API key
          const decryptedApiKey = decrypt(userApiKey.apiKey);

          // Ejecutar petición API usando el servicio
          const apiResult = await apiExecutionService.executeRequest(
            integration,
            endpoint,
            decryptedApiKey,
            req.user,
            false
          );

          if (!apiResult.success) {
            results.push({
              integration: integration.name,
              endpoint: endpoint.name,
              status: 'error',
              error: apiResult.message
            });
            continue;
          }

          // Procesar datos según el menú objetivo
          let processedResult;
          switch (targetMenu) {
            case 'kanban':
              processedResult = await processKanbanData(apiResult.data, endpoint, integration, userId);
              break;
            default:
              processedResult = { tasksCreated: 0, tasksUpdated: 0 };
          }

          totalTasksCreated += processedResult.tasksCreated;
          totalTasksUpdated += processedResult.tasksUpdated;

          results.push({
            integration: integration.name,
            endpoint: endpoint.name,
            status: 'success',
            tasksCreated: processedResult.tasksCreated,
            tasksUpdated: processedResult.tasksUpdated
          });

          // Log de la importación
          await prisma.apiImportLog.create({
            data: {
              userId,
              integrationId: integration.id,
              endpointName: endpoint.name,
              status: 'success',
              recordsFound: processedResult.recordsFound || 0,
              tasksCreated: processedResult.tasksCreated,
              tasksUpdated: processedResult.tasksUpdated,
              executionTime: Date.now() - startTime
            }
          });

        } catch (endpointError) {
          console.error(`❌ Error en endpoint ${endpoint.name}:`, endpointError);
          
          results.push({
            integration: integration.name,
            endpoint: endpoint.name,
            status: 'error',
            error: endpointError.message
          });

          // Log del error
          await prisma.apiImportLog.create({
            data: {
              userId,
              integrationId: integration.id,
              endpointName: endpoint.name,
              status: 'error',
              errorMessage: endpointError.message,
              executionTime: Date.now() - startTime
            }
          });
        }
      }
    }

    const executionTime = Date.now() - startTime;
    console.log(`✅ Importación completada en ${executionTime}ms. Tareas creadas: ${totalTasksCreated}, actualizadas: ${totalTasksUpdated}`);

    res.json({
      success: true,
      tasksCreated: totalTasksCreated,
      tasksUpdated: totalTasksUpdated,
      results,
      executionTime,
      message: totalTasksCreated > 0 || totalTasksUpdated > 0
        ? `${totalTasksCreated} tickets importados, ${totalTasksUpdated} actualizados`
        : 'No se encontraron tickets nuevos para importar'
    });

  } catch (error) {
    console.error('❌ Error en importación automática:', error);
    
    // Log del error general
    try {
      await prisma.apiImportLog.create({
        data: {
          userId,
          integrationId: 'general',
          endpointName: 'auto_import',
          status: 'error',
          errorMessage: error.message,
          executionTime: Date.now() - startTime
        }
      });
    } catch (logError) {
      console.error('Error al crear log:', logError);
    }

    res.status(500).json({
      success: false,
      error: 'Error al importar datos desde sistemas externos',
      message: error.message
    });
  }
};

// Procesar datos para el tablero Kanban
const processKanbanData = async (apiData, endpoint, integration, userId) => {
  try {
    console.log(`📊 Procesando datos de Kanban. Endpoint: ${endpoint.name}`);

    // Extraer datos según la estructura de la respuesta
    let records = [];
    if (apiData && apiData.requests) {
      records = apiData.requests; // ManageEngine structure
    } else if (Array.isArray(apiData)) {
      records = apiData;
    } else if (apiData && apiData.data && Array.isArray(apiData.data)) {
      records = apiData.data;
    }

    console.log(`📋 Encontrados ${records.length} registros para procesar`);

    let tasksCreated = 0;
    let tasksUpdated = 0;

    for (const record of records) {
      try {
        // Mapear campos según configuración
        const mappedData = mapApiDataToTask(record, endpoint.fieldMapping, endpoint.statusMapping, endpoint.priorityMapping);
        
        // Verificar si la tarea ya existe (por externalId)
        const existingTask = await prisma.task.findFirst({
          where: {
            externalId: mappedData.externalId,
            integrationId: integration.id
          }
        });

        if (existingTask) {
          // Actualizar tarea existente
          await prisma.task.update({
            where: { id: existingTask.id },
            data: {
              title: mappedData.title,
              description: mappedData.description,
              status: mappedData.status,
              priority: mappedData.priority,
              dueDate: mappedData.dueDate,
              updatedAt: new Date()
            }
          });
          tasksUpdated++;
        } else {
          // Crear nueva tarea
          await prisma.task.create({
            data: {
              ...mappedData,
              assigneeId: userId,
              creatorId: userId,
              source: 'API_IMPORT',
              integrationId: integration.id,
              estimatedHours: 0
            }
          });
          tasksCreated++;
        }

      } catch (recordError) {
        console.error(`⚠️ Error procesando registro:`, recordError);
        // Continuar con el siguiente registro
      }
    }

    return {
      recordsFound: records.length,
      tasksCreated,
      tasksUpdated
    };

  } catch (error) {
    console.error('❌ Error procesando datos de Kanban:', error);
    throw error;
  }
};

// Mapear datos de API a estructura de tarea
const mapApiDataToTask = (apiRecord, fieldMapping, statusMapping, priorityMapping) => {
  const getNestedValue = (obj, path) => {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  };

  const mapStatus = (apiStatus) => {
    // TODOS los tickets importados van a TODO sin importar su estado original
    // Esto permite al usuario revisar y mover manualmente según sea necesario
    return 'TODO';
  };

  const mapPriority = (apiPriority) => {
    if (priorityMapping && priorityMapping[apiPriority]) {
      return priorityMapping[apiPriority];
    }
    // Mapeo por defecto
    const priorityMap = {
      'Low': 'LOW',
      'Medium': 'MEDIUM',
      'High': 'HIGH',
      'Urgent': 'URGENT',
      'Critical': 'URGENT'
    };
    return priorityMap[apiPriority] || 'MEDIUM';
  };

  return {
    title: getNestedValue(apiRecord, fieldMapping.title) || 'Ticket sin título',
    description: getNestedValue(apiRecord, fieldMapping.description) || '',
    status: mapStatus(getNestedValue(apiRecord, fieldMapping.status)),
    priority: mapPriority(getNestedValue(apiRecord, fieldMapping.priority)),
    dueDate: fieldMapping.dueDate ? new Date(getNestedValue(apiRecord, fieldMapping.dueDate)) : null,
    externalId: getNestedValue(apiRecord, fieldMapping.externalId || 'id'),
    externalUrl: fieldMapping.externalUrl ? getNestedValue(apiRecord, fieldMapping.externalUrl) : null
  };
};

// Obtener integraciones disponibles para un usuario
exports.getAvailableIntegrations = async (req, res) => {
  try {
    const userId = req.user.id;
    const { targetMenu } = req.query;

    const integrations = await prisma.apiIntegration.findMany({
      where: {
        isActive: true,
        userApiKeys: {
          some: {
            userId: userId,
            isActive: true
          }
        }
      },
      select: {
        id: true,
        name: true,
        description: true,
        endpoints: true
      }
    });

    // Filtrar endpoints por menú objetivo si se especifica
    const filteredIntegrations = integrations.map(integration => ({
      ...integration,
      endpoints: targetMenu 
        ? integration.endpoints.filter(ep => ep.targetMenu === targetMenu)
        : integration.endpoints
    })).filter(integration => integration.endpoints.length > 0);

    res.json(filteredIntegrations);
  } catch (error) {
    console.error('Error al obtener integraciones disponibles:', error);
    res.status(500).json({ error: 'Error al obtener las integraciones disponibles' });
  }
};

// Obtener logs de importación
exports.getImportLogs = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 20 } = req.query;

    const logs = await prisma.apiImportLog.findMany({
      where: {
        userId: userId
      },
      include: {
        integration: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        executedAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: parseInt(limit)
    });

    const total = await prisma.apiImportLog.count({
      where: {
        userId: userId
      }
    });

    res.json({
      logs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error al obtener logs de importación:', error);
    res.status(500).json({ error: 'Error al obtener los logs de importación' });
  }
};
