const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/dashboardController');
const { authenticateToken } = require('../middleware/auth');

// Todas las rutas requieren autenticación
router.use(authenticateToken);

// Rutas del dashboard
router.get('/summary', dashboardController.getDashboardSummary);
router.get('/stats', dashboardController.getDashboardStats);
// Nueva ruta para obtener horas semanales de todos los usuarios (solo para administradores)
router.get('/users-weekly-hours', dashboardController.getUsersWeeklyHours);
// Ruta para obtener tabla de horas extra y compensaciones
router.get('/overtime-compensation-table', dashboardController.getOvertimeCompensationTable);
// Ruta de prueba
router.get('/test-overtime', dashboardController.testOvertimeEndpoint);
// Ruta de prueba para verificar acceso de usuarios
router.get('/test-user-access', dashboardController.testUserAccess);

module.exports = router;
