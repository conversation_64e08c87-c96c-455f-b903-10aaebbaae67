const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testOfficeHoursConfiguration() {
  try {
    console.log('🧪 PROBANDO CONFIGURACIÓN DE HORARIOS DE OFICINA');
    console.log('='.repeat(60));

    // 1. Verificar configuración actual de WorkSchedule
    const workSchedules = await prisma.workSchedule.findMany();
    console.log(`\n📅 HORARIOS DE TRABAJO CONFIGURADOS: ${workSchedules.length}`);
    
    if (workSchedules.length > 0) {
      workSchedules.forEach(schedule => {
        const dayNames = ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Viernes', 'Sábado'];
        console.log(`  - ${dayNames[schedule.dayOfWeek]}: ${schedule.startTime} - ${schedule.endTime}`);
      });
    } else {
      console.log('  ⚠️ No hay horarios configurados en la base de datos');
    }

    // 2. Simular la lógica del taskController
    console.log('\n🔧 SIMULANDO LÓGICA DEL TASK CONTROLLER...');
    
    // Configuración por defecto (igual que en taskController.js)
    let defaultSchedule = {
      startTime: '09:00',
      endTime: '18:00',
      lunchBreak: '13:00',
      lunchDuration: 60,
      workDays: [1, 2, 3, 4, 5], // Lunes a Viernes
    };

    if (workSchedules.length > 0) {
      // Usar el primer horario como referencia para el horario estándar
      const firstSchedule = workSchedules[0];
      defaultSchedule = {
        startTime: firstSchedule.startTime,
        endTime: firstSchedule.endTime,
        lunchBreak: firstSchedule.lunchBreak,
        lunchDuration: firstSchedule.lunchDuration,
        workDays: workSchedules.map(ws => ws.dayOfWeek)
      };
      console.log(`✅ Usando configuración de la base de datos:`);
    } else {
      console.log(`⚠️ Usando configuración por defecto:`);
    }

    console.log(`   📅 Horario: ${defaultSchedule.startTime} - ${defaultSchedule.endTime}`);
    console.log(`   🍽️ Almuerzo: ${defaultSchedule.lunchBreak} (${defaultSchedule.lunchDuration} min)`);
    console.log(`   📆 Días laborables: ${defaultSchedule.workDays.join(', ')}`);

    // 3. Probar diferentes escenarios de horarios
    console.log('\n🧪 PROBANDO DIFERENTES ESCENARIOS:');
    
    const testCases = [
      { time: '08:00', description: 'Antes del horario laboral' },
      { time: '09:00', description: 'Inicio del horario laboral' },
      { time: '12:00', description: 'Durante horario laboral (mañana)' },
      { time: '13:00', description: 'Hora de almuerzo' },
      { time: '14:00', description: 'Después del almuerzo' },
      { time: '17:00', description: 'Cerca del fin del horario' },
      { time: '18:00', description: 'Fin del horario laboral' },
      { time: '19:00', description: 'Después del horario laboral' }
    ];

    // Convertir horarios de oficina configurados a minutos
    const [startHour, startMin] = defaultSchedule.startTime.split(':').map(Number);
    const [endHour, endMin] = defaultSchedule.endTime.split(':').map(Number);
    const officeStartMinutes = startHour * 60 + startMin;
    const officeEndMinutes = endHour * 60 + endMin;

    testCases.forEach(testCase => {
      const [hour, min] = testCase.time.split(':').map(Number);
      const testMinutes = hour * 60 + min;
      
      const isOutsideOfficeHours = testMinutes < officeStartMinutes || testMinutes >= officeEndMinutes;
      const status = isOutsideOfficeHours ? '🔴 FUERA' : '🟢 DENTRO';
      
      console.log(`  ${testCase.time} (${testCase.description}): ${status} del horario`);
    });

    // 4. Probar días de la semana
    console.log('\n📅 PROBANDO DÍAS DE LA SEMANA:');
    const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
    
    for (let day = 0; day <= 6; day++) {
      const isWorkDay = defaultSchedule.workDays.includes(day);
      const status = isWorkDay ? '🟢 LABORABLE' : '🔴 NO LABORABLE';
      console.log(`  ${dayNames[day]}: ${status}`);
    }

    console.log('\n✅ PRUEBA COMPLETADA');
    console.log('='.repeat(60));

  } catch (error) {
    console.error('❌ Error durante la prueba:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar la prueba
testOfficeHoursConfiguration();
