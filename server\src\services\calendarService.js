const { google } = require('googleapis');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

class CalendarService {
  constructor() {
    // Verificar si las credenciales están disponibles
    if (!process.env.GOOGLE_CLIENT_EMAIL || !process.env.GOOGLE_PRIVATE_KEY || !process.env.GOOGLE_CALENDAR_ID) {
      console.warn('Credenciales de Google Calendar no configuradas. El servicio de calendario estará inactivo.');
      this.isConfigured = false;
      return;
    }

    this.isConfigured = true;
    this.calendar = google.calendar({
      version: 'v3',
      auth: new google.auth.JWT(
        process.env.GOOGLE_CLIENT_EMAIL,
        null,
        process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        ['https://www.googleapis.com/auth/calendar.readonly']
      ),
    });
  }

  async syncHolidays() {
    if (!this.isConfigured) {
      console.log('Servicio de calendario no configurado. Omitiendo sincronización de feriados.');
      return [];
    }

    try {
      const startDate = new Date();
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(startDate);
      endDate.setFullYear(endDate.getFullYear() + 1);

      const response = await this.calendar.events.list({
        calendarId: process.env.GOOGLE_CALENDAR_ID, // ID del calendario de feriados
        timeMin: startDate.toISOString(),
        timeMax: endDate.toISOString(),
        singleEvents: true,
        orderBy: 'startTime',
      });

      const holidays = response.data.items.map(event => ({
        date: new Date(event.start.date || event.start.dateTime),
        name: event.summary,
        description: event.description || '',
      }));

      // Guardar o actualizar los feriados en la base de datos
      for (const holiday of holidays) {
        await prisma.holiday.upsert({
          where: {
            date: holiday.date,
          },
          update: {
            name: holiday.name,
            description: holiday.description,
          },
          create: {
            date: holiday.date,
            name: holiday.name,
            description: holiday.description,
          },
        });
      }

      return holidays;
    } catch (error) {
      console.error('Error al sincronizar feriados:', error);
      throw error;
    }
  }

  async getHolidays(startDate, endDate) {
    // No necesitamos verificar isConfigured aquí ya que este método
    // solo accede a la base de datos local, no a la API de Google
    try {
      return await prisma.holiday.findMany({
        where: {
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: {
          date: 'asc',
        },
      });
    } catch (error) {
      console.error('Error al obtener feriados:', error);
      throw error;
    }
  }
}

// Exportar una instancia singleton del servicio
module.exports = new CalendarService();
