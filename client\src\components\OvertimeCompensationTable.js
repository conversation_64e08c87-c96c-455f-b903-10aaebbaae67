import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Box,
  Alert,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  Button,
  IconButton,
  Tooltip,
  MenuItem,
  TextField,
} from '@mui/material';
import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Today as TodayIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../config';

const OvertimeCompensationTable = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentWeekStart, setCurrentWeekStart] = useState(null);
  const [selectedUserId, setSelectedUserId] = useState('ALL');
  const [users, setUsers] = useState([]);

  // Obtener el lunes de la semana actual
  const getCurrentWeekStart = () => {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const diffToMonday = (dayOfWeek === 0) ? 6 : (dayOfWeek - 1);
    const monday = new Date(today);
    monday.setDate(today.getDate() - diffToMonday);
    return monday;
  };

  useEffect(() => {
    if (!currentWeekStart) {
      setCurrentWeekStart(getCurrentWeekStart());
    } else {
      fetchOvertimeData();
    }
  }, [currentWeekStart]);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchOvertimeData = async () => {
    if (!currentWeekStart) return;

    try {
      setLoading(true);

      const weekEnd = new Date(currentWeekStart);
      weekEnd.setDate(currentWeekStart.getDate() + 6);

      const params = new URLSearchParams({
        startDate: currentWeekStart.toISOString().split('T')[0],
        endDate: weekEnd.toISOString().split('T')[0],
      });

      // Solo agregar userId si es admin y se seleccionó un usuario específico
      const userRole = localStorage.getItem('userRole');
      if ((userRole === 'ADMIN' || userRole === 'MANAGER') && selectedUserId !== 'ALL') {
        params.append('userId', selectedUserId);
      }

      const response = await axios.get(`${API_URL}/api/dashboard/overtime-compensation-table?${params}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });

      setData(response.data);
    } catch (error) {
      console.error('Error al obtener datos de horas extra:', error);
      setError(error.response?.data?.error || 'Error al cargar datos');
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await axios.get(`${API_URL}/api/users`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      setUsers(response.data || []);
    } catch (error) {
      console.error('Error al obtener usuarios:', error);
    }
  };

  const getStatusColor = (hours, isOvertime, isUndertime) => {
    if (isOvertime) return 'error.main';
    if (isUndertime) return 'warning.main';
    if (hours > 0) return 'info.main';
    return 'success.main';
  };

  const getStatusText = (hours, isOvertime, isUndertime) => {
    if (isOvertime) return 'Sobretiempo';
    if (isUndertime) return 'Subtiempo';
    if (hours > 0) return 'Con Extra';
    return 'Normal';
  };

  // Funciones de navegación de semanas
  const goToPreviousWeek = () => {
    const previousWeek = new Date(currentWeekStart);
    previousWeek.setDate(currentWeekStart.getDate() - 7);
    setCurrentWeekStart(previousWeek);
  };

  const goToNextWeek = () => {
    const nextWeek = new Date(currentWeekStart);
    nextWeek.setDate(currentWeekStart.getDate() + 7);
    setCurrentWeekStart(nextWeek);
  };

  const goToCurrentWeek = () => {
    setCurrentWeekStart(getCurrentWeekStart());
  };

  const handleUserFilterChange = (event) => {
    setSelectedUserId(event.target.value);
  };

  // Actualizar datos cuando cambie el filtro de usuario
  useEffect(() => {
    if (currentWeekStart) {
      fetchOvertimeData();
    }
  }, [selectedUserId]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          Calculando horas extra y compensaciones...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!data || !data.users) {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        No hay datos disponibles
      </Alert>
    );
  }

  const userRole = localStorage.getItem('userRole');
  const isAdmin = userRole === 'ADMIN' || userRole === 'MANAGER';

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h5" gutterBottom>
        📊 Control de Horas Extra y Compensaciones
      </Typography>

      {/* Controles de navegación y filtros */}
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
        {/* Navegación de semanas */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="Semana anterior">
            <IconButton onClick={goToPreviousWeek} size="small">
              <ChevronLeftIcon />
            </IconButton>
          </Tooltip>

          <Typography variant="h6" sx={{ minWidth: '200px', textAlign: 'center' }}>
            {data?.weekRange || 'Cargando...'}
          </Typography>

          <Tooltip title="Semana siguiente">
            <IconButton onClick={goToNextWeek} size="small">
              <ChevronRightIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Ir a semana actual">
            <IconButton onClick={goToCurrentWeek} size="small" color="primary">
              <TodayIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Filtro de usuarios (solo para admins) */}
        {isAdmin && (
          <TextField
            select
            label="Filtrar por usuario"
            value={selectedUserId}
            onChange={handleUserFilterChange}
            size="small"
            sx={{ minWidth: 200 }}
          >
            <MenuItem value="ALL">Todos los usuarios</MenuItem>
            {users.map((user) => (
              <MenuItem key={user.id} value={user.id}>
                {user.name}
              </MenuItem>
            ))}
          </TextField>
        )}

        {/* Botón de actualizar */}
        <Button
          variant="outlined"
          onClick={fetchOvertimeData}
          disabled={loading}
          size="small"
        >
          {loading ? 'Cargando...' : 'Actualizar'}
        </Button>
      </Box>

      {/* Resumen General */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="primary">
                {data.summary.totalUsers}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Total Empleados
              </Typography>
            </CardContent>
          </Card>
        </Grid>



        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="info.main">
                {data.summary.totalOutsideHours}h
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Fuera de Oficina
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="warning.main">
                {data.summary.totalWeightedHours1_5 || 0}h
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Horas × 1.5
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="error.main">
                {data.summary.totalWeightedHours2_0 || 0}h
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Horas × 2.0
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="success.main">
                {data.summary.totalCompensatedHours}h
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Compensadas
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="error.main">
                {data.summary.totalBalanceHours}h
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Balance Total
              </Typography>
            </CardContent>
          </Card>
        </Grid>


      </Grid>

      {/* Información de Configuración */}
      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Horario Estándar:</strong> {data.summary.officeHours.start} - {data.summary.officeHours.end} |
          <strong> Almuerzo:</strong> {data.summary.officeHours.lunch} ({data.summary.officeHours.lunchDuration}min) |
          <strong> Días Laborables:</strong> {data.summary.officeHours.workDays?.length || 0} días |
          <strong> Horas Base Asumidas:</strong> {data.summary.assumedWeeklyHours || 40}h/semana
        </Typography>
      </Alert>

      {/* Tabla de usuarios para la semana seleccionada */}
      <TableContainer component={Paper} sx={{ mb: 3 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell><strong>Usuario</strong></TableCell>
              <TableCell align="center"><strong>Horas Fuera Oficina</strong></TableCell>
              <TableCell align="center"><strong>Horas × 1.5</strong></TableCell>
              <TableCell align="center"><strong>Horas × 2.0</strong></TableCell>
              <TableCell align="center"><strong>Horas Compensadas</strong></TableCell>
              <TableCell align="center"><strong>Balance Acumulado</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data.users.map((user) => (
              <TableRow key={user.userId}>
                <TableCell>
                  <Typography variant="body1" fontWeight="medium">
                    {user.userName}
                  </Typography>
                </TableCell>

                {/* Columna 1: Horas Fuera de Oficina */}
                <TableCell align="center">
                  <Typography
                    variant="body2"
                    sx={{
                      color: user.outsideOfficeHours > 0 ? 'info.main' : 'text.secondary',
                      fontWeight: user.outsideOfficeHours > 0 ? 'bold' : 'normal'
                    }}
                  >
                    {user.outsideOfficeHours}h
                  </Typography>
                </TableCell>

                {/* Columna 2: Horas × 1.5 */}
                <TableCell align="center">
                  <Typography
                    variant="body2"
                    sx={{
                      color: user.weightedHours1_5 > 0 ? 'warning.main' : 'text.secondary',
                      fontWeight: user.weightedHours1_5 > 0 ? 'bold' : 'normal'
                    }}
                  >
                    {user.weightedHours1_5 || 0}h
                  </Typography>
                  {user.weightedHours1_5 > 0 && (
                    <Typography variant="caption" color="warning.main" display="block">
                      (×1.5)
                    </Typography>
                  )}
                </TableCell>

                {/* Columna 3: Horas × 2.0 */}
                <TableCell align="center">
                  <Typography
                    variant="body2"
                    sx={{
                      color: user.weightedHours2_0 > 0 ? 'error.main' : 'text.secondary',
                      fontWeight: user.weightedHours2_0 > 0 ? 'bold' : 'normal'
                    }}
                  >
                    {user.weightedHours2_0 || 0}h
                  </Typography>
                  {user.weightedHours2_0 > 0 && (
                    <Typography variant="caption" color="error.main" display="block">
                      (×2.0)
                    </Typography>
                  )}
                </TableCell>

                {/* Columna 4: Horas Compensadas */}
                <TableCell align="center">
                  <Typography
                    variant="body2"
                    sx={{
                      color: user.compensatedHours > 0 ? 'success.main' : 'text.secondary',
                      fontWeight: user.compensatedHours > 0 ? 'bold' : 'normal'
                    }}
                  >
                    {user.compensatedHours}h
                  </Typography>
                </TableCell>

                {/* Columna 5: Balance Acumulado */}
                <TableCell align="center">
                  <Typography
                    variant="body2"
                    sx={{
                      color: user.balanceHours > 0 ? 'error.main' : 'success.main',
                      fontWeight: 'bold'
                    }}
                  >
                    {user.balanceHours > 0 ? '+' : ''}{user.balanceHours}h
                  </Typography>
                </TableCell>


              </TableRow>
            ))}

            {/* Fila de Totales */}
            <TableRow sx={{ bgcolor: 'grey.100', borderTop: 2, borderColor: 'grey.300' }}>
              <TableCell>
                <Typography variant="body1" fontWeight="bold" color="primary.main">
                  TOTALES
                </Typography>
              </TableCell>

              {/* Total Horas Fuera de Oficina */}
              <TableCell align="center">
                <Typography variant="body1" fontWeight="bold" color="info.main">
                  {data.summary?.totalOutsideHours || 0}h
                </Typography>
              </TableCell>

              {/* Total Horas × 1.5 */}
              <TableCell align="center">
                <Typography variant="body1" fontWeight="bold" color="warning.main">
                  {data.summary?.totalWeightedHours1_5 || 0}h
                </Typography>
              </TableCell>

              {/* Total Horas × 2.0 */}
              <TableCell align="center">
                <Typography variant="body1" fontWeight="bold" color="error.main">
                  {data.summary?.totalWeightedHours2_0 || 0}h
                </Typography>
              </TableCell>

              {/* Total Horas Compensadas */}
              <TableCell align="center">
                <Typography variant="body1" fontWeight="bold" color="success.main">
                  {data.summary?.totalCompensatedHours || 0}h
                </Typography>
              </TableCell>

              {/* Total Balance Acumulado */}
              <TableCell align="center">
                <Typography
                  variant="body1"
                  fontWeight="bold"
                  color={(data.summary?.totalBalanceHours || 0) > 0 ? 'error.main' : 'success.main'}
                >
                  {(data.summary?.totalBalanceHours || 0) > 0 ? '+' : ''}{data.summary?.totalBalanceHours || 0}h
                </Typography>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>

      {/* Leyenda */}
      <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
        <Typography variant="body2" gutterBottom>
          <strong>Explicación del Cálculo:</strong>
        </Typography>
        <Typography variant="caption" display="block">
          • <strong>Horas Fuera Oficina:</strong> Registros manuales fuera del horario estándar configurado + días no laborables (almuerzo NO incluido)
        </Typography>
        <Typography variant="caption" display="block">
          • <strong>Horas × 1.5:</strong> Horas fuera de oficina con multiplicador 1.5x (nocturnas, horas extra, etc.)
        </Typography>
        <Typography variant="caption" display="block">
          • <strong>Horas × 2.0:</strong> Horas fuera de oficina con multiplicador 2.0x (feriados, especiales, etc.)
        </Typography>
        <Typography variant="caption" display="block">
          • <strong>Horas Compensadas:</strong> Permisos de "Compensación por Horas Extra" y "Permiso Personal" aprobados
        </Typography>
        <Typography variant="caption" display="block">
          • <strong>Balance Acumulado:</strong> Horas fuera de oficina - horas compensadas (sin límites)
        </Typography>
        <Typography variant="caption" display="block">
          • <strong>Asunción:</strong> Cada trabajador ya completó sus 40h semanales base
        </Typography>
        <Typography variant="caption" display="block">
          • <strong>Navegación:</strong> Use las flechas para ver semanas anteriores/siguientes
        </Typography>
      </Box>
    </Paper>
  );
};

export default OvertimeCompensationTable;
