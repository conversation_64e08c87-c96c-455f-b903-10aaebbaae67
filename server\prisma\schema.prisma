generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                  String         @id @default(uuid())
  email               String         @unique
  name                String
  password            String
  role                Role           @default(EMPLOYEE)
  twoFactorEnabled    <PERSON>olean        @default(false)
  twoFactorSecret     String?
  createdAt           DateTime       @default(now())
  updatedAt           DateTime       @updatedAt
  isLDAPUser          Boolean        @default(false)
  isActive            Boolean        @default(true)
  lastLogin           DateTime?
  activeTimers        ActiveTimer[]
  notifications       Notification[]
  approvedPermissions Permission[]   @relation("PermissionApprover")
  permissions         Permission[]
  tasks               Task[]         @relation("AssignedTasks")
  createdTasks        Task[]         @relation("TaskCreator")
  currentTimerTasks   Task[]         @relation("CurrentTaskTimer")
  approvedEntries     TimeEntry[]    @relation("TimeEntryApprover")
  timeEntries         TimeEntry[]
  userApiKeys         UserApiKey[]
  apiImportLogs       ApiImportLog[]
}

model Task {
  id                      String        @id @default(uuid())
  title                   String
  description             String?
  status                  TaskStatus
  estimatedHours          Float
  actualHours             Float?
  priority                Priority      @default(MEDIUM)
  dueDate                 DateTime?
  tags                    String[]
  createdAt               DateTime      @default(now())
  updatedAt               DateTime      @updatedAt
  assigneeId              String
  creatorId               String
  source                  String        @default("KANBAN")
  externalId              String?       // ID del sistema externo (ej: ticket ID de ManageEngine)
  externalUrl             String?       // URL del ticket en el sistema externo
  integrationId           String?       // ID de la integración que creó esta tarea
  timerIsRunning          Boolean       @default(false)
  timerStartTime          DateTime?
  timerLastStarted        DateTime?
  timerAccumulatedSeconds Int           @default(0)
  timerCurrentUserId      String?
  activeTimers            ActiveTimer[]
  attachments             Attachment[]
  comments                Comment[]
  assignee                User          @relation("AssignedTasks", fields: [assigneeId], references: [id])
  creator                 User          @relation("TaskCreator", fields: [creatorId], references: [id])
  timerCurrentUser        User?         @relation("CurrentTaskTimer", fields: [timerCurrentUserId], references: [id])
  timeEntries             TimeEntry[]
  integration             ApiIntegration? @relation(fields: [integrationId], references: [id])
}

model TimeEntry {
  id              String           @id @default(uuid())
  title           String
  description     String?
  startTime       DateTime
  endTime         DateTime
  hoursWorked     Float
  multipliedHours Float?
  status          ApprovalStatus   @default(PENDING)
  type            TimeEntryType    @default(REGULAR)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  userId          String
  taskId          String?
  approverId      String?
  approvedAt      DateTime?
  audit           AuditLog[]
  approver        User?            @relation("TimeEntryApprover", fields: [approverId], references: [id])
  task            Task?            @relation(fields: [taskId], references: [id])
  user            User             @relation(fields: [userId], references: [id])
  multipliers     TimeMultiplier[] @relation("EntryMultipliers")
}

model Permission {
  id                     String         @id @default(uuid())
  type                   PermissionType
  startTime              DateTime
  endTime                DateTime
  reason                 String?
  status                 ApprovalStatus @default(PENDING)
  comments               String?
  createdAt              DateTime       @default(now())
  updatedAt              DateTime       @updatedAt
  approvedAt             DateTime?
  userId                 String
  approverId             String?
  overtimeHoursUsed      Float?
  overtimeHoursAvailable Float?
  audit                  AuditLog[]
  approver               User?          @relation("PermissionApprover", fields: [approverId], references: [id])
  user                   User           @relation(fields: [userId], references: [id])
}

model WorkSchedule {
  id                  String   @id @default(uuid())
  dayOfWeek           Int
  startTime           String
  endTime             String
  lunchBreak          String
  lunchDuration       Int
  multiplier          Float    @default(1.0)
  isHoliday           Boolean  @default(false)
  allowedOutsideHours Int?     @default(8)
  assumedWeeklyHours  Int?     @default(40)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
}

model TimeMultiplier {
  id          String      @id @default(uuid())
  name        String
  value       Float
  startTime   String?
  endTime     String?
  description String?
  conditions  String?
  isActive    Boolean     @default(true)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  timeEntries TimeEntry[] @relation("EntryMultipliers")
}

model PermissionTypeConfig {
  id                String    @id @default(uuid())
  name              String    @unique
  label             String
  maxDaysPerYear    Int       @default(0)
  requiresApproval  Boolean   @default(true)
  active            Boolean   @default(true)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}

model Holiday {
  id          String   @id @default(uuid())
  name        String
  date        DateTime
  multiplier  Float    @default(2.0)
  isRecurring Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  isActive    Boolean? @default(true)
}

model Notification {
  id        String           @id @default(uuid())
  type      NotificationType
  title     String
  message   String
  read      Boolean          @default(false)
  createdAt DateTime         @default(now())
  userId    String
  user      User             @relation(fields: [userId], references: [id])
}

model Comment {
  id        String   @id @default(uuid())
  content   String
  createdAt DateTime @default(now())
  taskId    String
  task      Task     @relation(fields: [taskId], references: [id])
}

model Attachment {
  id        String   @id @default(uuid())
  name      String
  url       String
  type      String
  size      Int
  createdAt DateTime @default(now())
  taskId    String
  task      Task     @relation(fields: [taskId], references: [id])
}

model AuditLog {
  id           String      @id @default(uuid())
  action       String
  entityType   String
  entityId     String
  oldValue     String?
  newValue     String?
  createdAt    DateTime    @default(now())
  timeEntryId  String?
  permissionId String?
  permission   Permission? @relation(fields: [permissionId], references: [id])
  timeEntry    TimeEntry?  @relation(fields: [timeEntryId], references: [id])
}

model ActiveTimer {
  id              String   @id @default(uuid())
  taskId          String
  userId          String
  startTime       DateTime
  isRunning       Boolean  @default(true)
  accumulatedTime Int      @default(0)
  lastStarted     DateTime
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  task            Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([taskId, userId])
}

model DynamicField {
  id           String   @id @default(cuid())
  name         String   @unique
  label        String
  type         String   @default("text") // text, textarea, number, date, datetime-local, select, checkbox
  required     Boolean  @default(false)
  placeholder  String?
  options      String?  // For select fields, comma-separated values
  defaultValue String?
  order        Int      @default(0)
  isActive     Boolean  @default(true)
  appliesTo    String   @default("both") // timeEntry, task, both
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  values DynamicFieldValue[]
}

model DynamicFieldValue {
  id         String   @id @default(cuid())
  fieldId    String
  entityType String   // timeEntry, task
  entityId   String   // ID of the timeEntry or task
  value      String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  field DynamicField @relation(fields: [fieldId], references: [id], onDelete: Cascade)

  @@unique([fieldId, entityType, entityId])
  @@index([fieldId])
  @@index([entityType, entityId])
}

model ApiIntegration {
  id              String         @id @default(uuid())
  name            String         // "ManageEngine ServiceDesk Plus"
  description     String?        // Descripción de la integración
  baseUrl         String         // URL base de la API
  authType        String         // "api_key", "bearer_token", "basic_auth", "oauth2"
  authConfig      Json           // Configuración de autenticación (headers, etc.)
  endpoints       Json           // Array de endpoints configurados
  isActive        Boolean        @default(true)
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  createdBy       String         // Admin que creó la integración
  userApiKeys     UserApiKey[]
  tasks           Task[]
  importLogs      ApiImportLog[]
}

model UserApiKey {
  id              String         @id @default(uuid())
  userId          String
  integrationId   String
  apiKey          String         // API key personal del usuario (encriptada)
  isActive        Boolean        @default(true)
  lastTested      DateTime?      // Última vez que se probó la conexión
  testStatus      String?        // "success", "error", "pending"
  testMessage     String?        // Mensaje del último test
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  user            User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  integration     ApiIntegration @relation(fields: [integrationId], references: [id], onDelete: Cascade)

  @@unique([userId, integrationId])
  @@index([userId])
  @@index([integrationId])
}

model ApiImportLog {
  id              String         @id @default(uuid())
  userId          String
  integrationId   String
  endpointName    String         // Nombre del endpoint ejecutado
  status          String         // "success", "error", "partial", "no_data"
  recordsFound    Int?           // Número de registros encontrados en la API
  tasksCreated    Int?           // Número de tareas creadas
  tasksUpdated    Int?           // Número de tareas actualizadas
  errorMessage    String?        // Mensaje de error si falló
  executionTime   Int?           // Tiempo de ejecución en milisegundos
  requestData     Json?          // Datos de la petición realizada
  responseData    Json?          // Datos de respuesta (limitados)
  executedAt      DateTime       @default(now())
  user            User           @relation(fields: [userId], references: [id])
  integration     ApiIntegration @relation(fields: [integrationId], references: [id])

  @@index([userId])
  @@index([integrationId])
  @@index([executedAt])
}

enum Role {
  ADMIN
  MANAGER
  SUPERVISOR
  EMPLOYEE
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  REVIEW
  ARCHIVED
  DONE
  COMPLETED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum TimeEntryType {
  REGULAR
  OVERTIME
  NIGHT_SHIFT
  HOLIDAY
}

enum PermissionType {
  VACATION
  SICK_LEAVE
  PERSONAL_LEAVE
  OVERTIME_COMPENSATION
  OTHER
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

enum NotificationType {
  TASK_ASSIGNED
  TASK_UPDATED
  TIME_ENTRY_APPROVAL
  PERMISSION_APPROVAL
  SYSTEM
}
