const express = require('express');
const router = express.Router();
const holidayController = require('../controllers/holidayController');
const { authenticateToken, isAdmin } = require('../middleware/auth');

// Todas las rutas requieren autenticación
router.use(authenticateToken);

// Solo los administradores pueden sincronizar feriados
router.post('/sync', isAdmin, holidayController.syncHolidays);

// Cualquier usuario autenticado puede ver los feriados
router.get('/', holidayController.getHolidays);

module.exports = router;
