const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkPrueba21Status() {
  try {
    console.log('🔍 VERIFICANDO ESTADO DE TAREA "prueba21"');
    console.log('='.repeat(60));

    // Buscar la tarea prueba21
    const timeEntries = await prisma.timeEntry.findMany({
      where: {
        title: {
          contains: 'prueba21',
          mode: 'insensitive'
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📝 Entradas encontradas: ${timeEntries.length}`);

    if (timeEntries.length === 0) {
      console.log('❌ No se encontró la tarea "prueba21"');
      return;
    }

    const entry = timeEntries[0];
    console.log(`\n🎯 DETALLES DE LA TAREA:`);
    console.log(`   ID: ${entry.id}`);
    console.log(`   Títu<PERSON>: "${entry.title}"`);
    console.log(`   Usuario: ${entry.user?.name || 'Desconocido'}`);
    console.log(`   Estado: ${entry.status}`);
    console.log(`   Creado: ${entry.createdAt}`);
    console.log(`   Inicio: ${entry.startTime}`);
    console.log(`   Fin: ${entry.endTime}`);
    console.log(`   Horas: ${entry.hoursWorked}`);

    // Verificar si está en el rango de fechas del calendario
    const startDate = '2025-06-30';
    const endDate = '2025-07-06';
    const rangeStart = new Date(startDate + 'T00:00:00.000Z');
    const rangeEnd = new Date(endDate + 'T23:59:59.999Z');
    
    console.log(`\n📅 VERIFICACIÓN DE RANGO DE FECHAS:`);
    console.log(`   Rango del calendario: ${startDate} a ${endDate}`);
    console.log(`   Rango UTC: ${rangeStart.toISOString()} a ${rangeEnd.toISOString()}`);
    console.log(`   Inicio de tarea: ${entry.startTime}`);
    
    const taskStart = new Date(entry.startTime);
    const isInRange = taskStart >= rangeStart && taskStart <= rangeEnd;
    
    console.log(`   ¿Está en el rango? ${isInRange ? '✅ SÍ' : '❌ NO'}`);

    // Verificar criterios de filtrado
    console.log(`\n🔍 CRITERIOS DE FILTRADO:`);
    console.log(`   ¿Estado APPROVED? ${entry.status === 'APPROVED' ? '✅ SÍ' : '❌ NO'}`);
    console.log(`   ¿En rango de fechas? ${isInRange ? '✅ SÍ' : '❌ NO'}`);

    if (entry.status === 'APPROVED' && isInRange) {
      console.log(`\n✅ LA TAREA DEBERÍA APARECER EN EL CALENDARIO`);
    } else {
      console.log(`\n❌ LA TAREA NO APARECERÁ EN EL CALENDARIO`);
      if (entry.status !== 'APPROVED') {
        console.log(`   Razón: Estado no es APPROVED (actual: ${entry.status})`);
      }
      if (!isInRange) {
        console.log(`   Razón: No está en el rango de fechas del calendario`);
      }
    }

    // Simular la consulta exacta que hace la API
    console.log(`\n🧪 SIMULANDO CONSULTA DE LA API:`);
    const apiQuery = await prisma.timeEntry.findMany({
      where: {
        status: 'APPROVED',
        startTime: {
          gte: rangeStart,
          lt: rangeEnd
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    console.log(`   Entradas encontradas por la API: ${apiQuery.length}`);
    
    const prueba21InAPI = apiQuery.find(e => e.title.toLowerCase().includes('prueba21'));
    console.log(`   ¿prueba21 en resultados de API? ${prueba21InAPI ? '✅ SÍ' : '❌ NO'}`);

    if (prueba21InAPI) {
      console.log(`   ✅ La tarea SÍ está siendo devuelta por la API`);
    } else {
      console.log(`   ❌ La tarea NO está siendo devuelta por la API`);
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkPrueba21Status();
