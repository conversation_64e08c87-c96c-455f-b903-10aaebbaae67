const ldap = require('ldapjs');
const { promisify } = require('util');
const winston = require('winston');
const { PrismaClient } = require('@prisma/client');
const socketService = require('./socketService');

const prisma = new PrismaClient();

class LDAPService {
  constructor() {
    this.client = null;
    this.logger = winston.createLogger({
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.File({ 
          filename: 'logs/ldap.log'
        })
      ]
    });
  }

  async connect() {
    try {
      this.client = ldap.createClient({
        url: process.env.LDAP_URL,
        tlsOptions: {
          rejectUnauthorized: false
        }
      });

      // Promisify client methods
      this.client.bindAsync = promisify(this.client.bind).bind(this.client);
      this.client.searchAsync = promisify(this.client.search).bind(this.client);

      await this.client.bindAsync(
        process.env.LDAP_BIND_DN,
        process.env.LDAP_BIND_PASSWORD
      );

      this.logger.info('Conexión LDAP establecida exitosamente');
      return true;
    } catch (error) {
      this.logger.error('Error al conectar con LDAP:', error);
      throw new Error('Error al conectar con el servidor LDAP');
    }
  }

  async searchUsers(filter = process.env.LDAP_USER_FILTER) {
    try {
      if (!this.client) {
        await this.connect();
      }

      const options = {
        scope: 'sub',
        filter: filter,
        attributes: ['uid', 'cn', 'mail', 'title', 'manager']
      };

      const users = [];
      const response = await this.client.searchAsync(
        process.env.LDAP_SEARCH_BASE,
        options
      );

      return new Promise((resolve, reject) => {
        response.on('searchEntry', (entry) => {
          users.push(this.mapLDAPUserToDBUser(entry.object));
        });

        response.on('error', (err) => {
          this.logger.error('Error en búsqueda LDAP:', err);
          reject(err);
        });

        response.on('end', () => {
          resolve(users);
        });
      });
    } catch (error) {
      this.logger.error('Error al buscar usuarios LDAP:', error);
      throw new Error('Error al buscar usuarios en LDAP');
    }
  }

  async syncUsers() {
    try {
      const ldapUsers = await this.searchUsers();
      let syncStats = {
        created: 0,
        updated: 0,
        errors: 0
      };

      for (const ldapUser of ldapUsers) {
        try {
          const existingUser = await prisma.user.findUnique({
            where: { email: ldapUser.email }
          });

          if (existingUser) {
            // Actualizar usuario existente
            await prisma.user.update({
              where: { id: existingUser.id },
              data: {
                name: ldapUser.name,
                title: ldapUser.title,
                role: this.mapLDAPRoleToDBRole(ldapUser.title),
                updatedAt: new Date()
              }
            });
            syncStats.updated++;
          } else {
            // Crear nuevo usuario
            await prisma.user.create({
              data: {
                email: ldapUser.email,
                name: ldapUser.name,
                title: ldapUser.title,
                role: this.mapLDAPRoleToDBRole(ldapUser.title),
                password: await this.generateTempPassword(),
                isLDAPUser: true
              }
            });
            syncStats.created++;
          }
        } catch (error) {
          this.logger.error(`Error al sincronizar usuario ${ldapUser.email}:`, error);
          syncStats.errors++;
        }
      }

      this.logger.info('Sincronización LDAP completada', syncStats);
      
      // Notificar sincronización completada
      socketService.notifyLDAPSync(syncStats);

      return syncStats;
    } catch (error) {
      this.logger.error('Error en sincronización LDAP:', error);
      throw new Error('Error en sincronización LDAP');
    }
  }

  mapLDAPUserToDBUser(ldapUser) {
    return {
      email: ldapUser.mail,
      name: ldapUser.cn,
      title: ldapUser.title,
      manager: ldapUser.manager
    };
  }

  mapLDAPRoleToDBRole(title) {
    const titleLower = (title || '').toLowerCase();
    if (titleLower.includes('manager') || titleLower.includes('director')) {
      return 'MANAGER';
    } else if (titleLower.includes('admin')) {
      return 'ADMIN';
    }
    return 'USER';
  }

  async generateTempPassword() {
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      password += charset[randomIndex];
    }
    return password;
  }

  async authenticate(email, password) {
    try {
      const user = await prisma.user.findUnique({
        where: { email }
      });

      if (!user || !user.isLDAPUser) {
        return null;
      }

      // Buscar DN del usuario en LDAP
      const options = {
        scope: 'sub',
        filter: `(&${process.env.LDAP_USER_FILTER}(mail=${email}))`,
        attributes: ['dn']
      };

      const response = await this.client.searchAsync(
        process.env.LDAP_SEARCH_BASE,
        options
      );

      return new Promise((resolve, reject) => {
        response.on('searchEntry', async (entry) => {
          try {
            // Intentar autenticar con las credenciales proporcionadas
            const ldapClient = ldap.createClient({
              url: process.env.LDAP_URL,
              tlsOptions: {
                rejectUnauthorized: false
              }
            });

            await promisify(ldapClient.bind).bind(ldapClient)(entry.object.dn, password);
            ldapClient.unbind();
            resolve(user);
          } catch (error) {
            resolve(null);
          }
        });

        response.on('error', () => resolve(null));
        response.on('end', (result) => {
          if (!result.entries.length) resolve(null);
        });
      });
    } catch (error) {
      this.logger.error('Error en autenticación LDAP:', error);
      return null;
    }
  }

  disconnect() {
    if (this.client) {
      this.client.unbind();
      this.client = null;
    }
  }
}

module.exports = new LDAPService();
