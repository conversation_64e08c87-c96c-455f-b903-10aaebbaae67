import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tab,
  Tabs,
  Snackbar,
  Alert,
  Chip,
  MenuItem,
} from '@mui/material';
import { Edit as EditIcon, Delete as DeleteIcon, Save as SaveIcon } from '@mui/icons-material';
import { API_URL } from '../config';
import DynamicFieldsSettings from '../components/DynamicFieldsSettings';
import UserManagement from '../components/UserManagement';
import ApiIntegrationsSettings from '../components/ApiIntegrationsSettings';

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div role="tabpanel" hidden={value !== index} {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const Settings = () => {
  const [tabValue, setTabValue] = useState(0);
  const [workSchedule, setWorkSchedule] = useState({
    startTime: '09:00',
    endTime: '18:00',
    lunchBreak: '13:00',
    lunchDuration: 60,
    workDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'], // Added workDays array
    allowedOutsideHours: 8, // Horas permitidas fuera de oficina por semana
    assumedWeeklyHours: 40, // Horas semanales base asumidas como completadas
  });

  const [multipliers, setMultipliers] = useState([
    { id: 1, name: 'Horas Nocturnas', startTime: '22:00', endTime: '06:00', value: 1.5 },
    { id: 2, name: 'Feriados', value: 2.0 },
    { id: 3, name: 'Horas Extra', value: 1.5 },
  ]);

  const [editingMultiplier, setEditingMultiplier] = useState(null);
  const [newMultiplier, setNewMultiplier] = useState({
    name: '',
    startTime: '',
    endTime: '',
    value: '',
  });

  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // Estado para tipos de permisos
  const [permissionTypes, setPermissionTypes] = useState([
    { id: 1, name: 'Vacaciones', label: 'Vacaciones', maxDaysPerYear: 30, requiresApproval: true, active: true },
    { id: 2, name: 'Enfermedad', label: 'Descanso Médico', maxDaysPerYear: 15, requiresApproval: false, active: true },
    { id: 3, name: 'Personal', label: 'Permiso Personal', maxDaysPerYear: 10, requiresApproval: true, active: true },
    { id: 4, name: 'Compensacion', label: 'Compensación de Horas Extra', maxDaysPerYear: 0, requiresApproval: true, active: true },
    { id: 5, name: 'Maternidad', label: 'Maternidad/Paternidad', maxDaysPerYear: 90, requiresApproval: false, active: true },
    { id: 6, name: 'Capacitacion', label: 'Capacitación', maxDaysPerYear: 5, requiresApproval: true, active: true },
    { id: 7, name: 'Otros', label: 'Otros', maxDaysPerYear: 5, requiresApproval: true, active: true },
  ]);

  const [editingPermissionType, setEditingPermissionType] = useState(null);
  const [newPermissionType, setNewPermissionType] = useState({
    name: '',
    label: '',
    maxDaysPerYear: 0,
    requiresApproval: true,
    active: true,
  });

  // Actualizar la URL base para usar la variable de entorno
  const API_BASE_URL = API_URL;

  useEffect(() => {
    // Cargar configuración del backend cuando el componente se monta
    fetchSettings();
    fetchPermissionTypes();
  }, []);

  const fetchSettings = async () => {
    try {
      // Usar try-catch independientes para cada solicitud
      try {
        // Obtener horario de trabajo - Corregir la ruta según settingsRoutes.js
        const scheduleResponse = await fetch(`${API_BASE_URL}/api/settings/work-schedules`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        // Verificar si la respuesta es JSON
        const contentType = scheduleResponse.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          if (scheduleResponse.ok) {
            const schedules = await scheduleResponse.json();
            if (schedules.length > 0) {
              const schedule = schedules[0];
              setWorkSchedule({
                startTime: schedule.startTime,
                endTime: schedule.endTime,
                lunchBreak: schedule.lunchBreak,
                lunchDuration: schedule.lunchDuration,
                workDays: schedule.workDays || ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
                allowedOutsideHours: schedule.allowedOutsideHours || 8,
                assumedWeeklyHours: schedule.assumedWeeklyHours || 40
              });
            }
          } else {
            console.warn('El endpoint de horarios devolvió estado:', scheduleResponse.status);
          }
        } else {
          console.warn('El endpoint de horarios no devolvió JSON');
        }
      } catch (scheduleError) {
        console.error('Error al obtener horarios de trabajo:', scheduleError);
        // Continuar con valores predeterminados
      }

      try {
        // Obtener multiplicadores - Corregir la ruta según settingsRoutes.js
        const multipliersResponse = await fetch(`${API_BASE_URL}/api/settings/multipliers`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        // Verificar si la respuesta es JSON
        const contentType = multipliersResponse.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          if (multipliersResponse.ok) {
            const fetchedMultipliers = await multipliersResponse.json();
            if (fetchedMultipliers.length > 0) {
              setMultipliers(fetchedMultipliers);
            }
          } else {
            console.warn('El endpoint de multiplicadores devolvió estado:', multipliersResponse.status);
          }
        } else {
          console.warn('El endpoint de multiplicadores no devolvió JSON');
        }
      } catch (multipliersError) {
        console.error('Error al obtener multiplicadores:', multipliersError);
        // Continuar con valores predeterminados
      }

      // No mostrar notificación de error si estamos usando valores predeterminados
      console.log('Usando configuración predeterminada');

    } catch (error) {
      console.error('Error al cargar la configuración:', error);
      setSnackbar({
        open: true,
        message: 'Error al cargar la configuración. Usando valores predeterminados.',
        severity: 'warning',
      });
    }
  };

  const fetchPermissionTypes = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/settings/permission-types`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const types = await response.json();
        setPermissionTypes(types);
      } else {
        console.error('Error al cargar tipos de permisos:', response.status);
      }
    } catch (error) {
      console.error('Error al cargar tipos de permisos:', error);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleWorkScheduleChange = (event) => {
    const { name, value } = event.target;
    setWorkSchedule((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // New handler for work days selection
  const handleWorkDayToggle = (day) => {
    setWorkSchedule((prev) => {
      const currentDays = [...prev.workDays];
      if (currentDays.includes(day)) {
        return { ...prev, workDays: currentDays.filter(d => d !== day) };
      } else {
        return { ...prev, workDays: [...currentDays, day] };
      }
    });
  };

  const handleSaveWorkSchedule = async () => {
    try {
      // Log the data being sent to help with debugging
      console.log('Sending work schedule data:', workSchedule);

      // Ensure workDays is an array before sending
      const dataToSend = {
        ...workSchedule,
        workDays: Array.isArray(workSchedule.workDays) ? workSchedule.workDays : []
      };

      // Corregir la ruta para guardar el horario según settingsRoutes.js
      const response = await fetch(`${API_BASE_URL}/api/settings/work-schedule`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(dataToSend),
      });

      // Get the full response text for better debugging
      const responseText = await response.text();
      console.log('Server response:', response.status, responseText);

      let responseData;
      try {
        // Try to parse as JSON if possible
        responseData = JSON.parse(responseText);
      } catch (e) {
        // If not JSON, use the text as is
        responseData = { message: responseText };
      }

      if (response.ok) {
        setSnackbar({
          open: true,
          message: 'Horario guardado correctamente',
          severity: 'success',
        });
      } else {
        // Use the error message from the server if available
        const errorMessage = responseData.error || 'Error en la respuesta del servidor';
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Error al guardar el horario:', error);
      setSnackbar({
        open: true,
        message: `Error al guardar el horario: ${error.message}`,
        severity: 'error',
      });
    }
  };

  const handleEditMultiplier = (multiplier) => {
    setEditingMultiplier(multiplier);
  };

  const handleSaveMultiplier = async (multiplier) => {
    try {
      // Implementar la llamada al backend con la ruta correcta según settingsRoutes.js
      const response = await fetch(`${API_BASE_URL}/api/settings/multipliers/${multiplier.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(multiplier),
      });

      if (response.ok) {
        setMultipliers((prev) =>
          prev.map((m) => (m.id === multiplier.id ? multiplier : m))
        );
        setEditingMultiplier(null);
        setSnackbar({
          open: true,
          message: 'Multiplicador actualizado correctamente',
          severity: 'success',
        });
      } else {
        throw new Error(`Error al actualizar (${response.status})`);
      }
    } catch (error) {
      console.error('Error al actualizar multiplicador:', error);
      setSnackbar({
        open: true,
        message: `Error al actualizar el multiplicador: ${error.message}`,
        severity: 'error',
      });
    }
  };

  const handleDeleteMultiplier = async (id) => {
    try {
      // Implementar la llamada al backend con la ruta correcta según settingsRoutes.js
      const response = await fetch(`${API_BASE_URL}/api/settings/multipliers/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        setMultipliers((prev) => prev.filter((m) => m.id !== id));
        setSnackbar({
          open: true,
          message: 'Multiplicador eliminado correctamente',
          severity: 'success',
        });
      } else {
        throw new Error(`Error al eliminar (${response.status})`);
      }
    } catch (error) {
      console.error('Error al eliminar multiplicador:', error);
      setSnackbar({
        open: true,
        message: `Error al eliminar el multiplicador: ${error.message}`,
        severity: 'error',
      });
    }
  };

  const handleAddMultiplier = async () => {
    try {
      // Implementar la llamada al backend con la ruta correcta según settingsRoutes.js
      const response = await fetch(`${API_BASE_URL}/api/settings/multipliers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(newMultiplier),
      });

      if (response.ok) {
        const addedMultiplier = await response.json();
        setMultipliers((prev) => [...prev, addedMultiplier]);
        setNewMultiplier({
          name: '',
          startTime: '',
          endTime: '',
          value: '',
        });
        setSnackbar({
          open: true,
          message: 'Multiplicador agregado correctamente',
          severity: 'success',
        });
      } else {
        throw new Error(`Error al agregar (${response.status})`);
      }
    } catch (error) {
      console.error('Error al agregar multiplicador:', error);
      setSnackbar({
        open: true,
        message: `Error al agregar el multiplicador: ${error.message}`,
        severity: 'error',
      });
    }
  };

  // Funciones para manejar tipos de permisos
  const handleEditPermissionType = (permissionType) => {
    setEditingPermissionType(permissionType);
  };

  const handleSavePermissionType = async (permissionType) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/settings/permission-types/${permissionType.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(permissionType),
      });

      if (response.ok) {
        setPermissionTypes((prev) =>
          prev.map((pt) => (pt.id === permissionType.id ? permissionType : pt))
        );
        setEditingPermissionType(null);
        setSnackbar({
          open: true,
          message: 'Tipo de permiso actualizado correctamente',
          severity: 'success',
        });
      } else {
        throw new Error(`Error al actualizar (${response.status})`);
      }
    } catch (error) {
      console.error('Error al actualizar tipo de permiso:', error);
      setSnackbar({
        open: true,
        message: `Error al actualizar el tipo de permiso: ${error.message}`,
        severity: 'error',
      });
    }
  };

  const handleDeletePermissionType = async (id) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/settings/permission-types/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        setPermissionTypes((prev) => prev.filter((pt) => pt.id !== id));
        setSnackbar({
          open: true,
          message: 'Tipo de permiso eliminado correctamente',
          severity: 'success',
        });
      } else {
        throw new Error(`Error al eliminar (${response.status})`);
      }
    } catch (error) {
      console.error('Error al eliminar tipo de permiso:', error);
      setSnackbar({
        open: true,
        message: `Error al eliminar el tipo de permiso: ${error.message}`,
        severity: 'error',
      });
    }
  };

  const handleAddPermissionType = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/settings/permission-types`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(newPermissionType),
      });

      if (response.ok) {
        const addedPermissionType = await response.json();
        setPermissionTypes((prev) => [...prev, addedPermissionType]);
        setNewPermissionType({
          name: '',
          label: '',
          maxDaysPerYear: 0,
          requiresApproval: true,
          active: true,
        });
        setSnackbar({
          open: true,
          message: 'Tipo de permiso agregado correctamente',
          severity: 'success',
        });
      } else {
        throw new Error(`Error al agregar (${response.status})`);
      }
    } catch (error) {
      console.error('Error al agregar tipo de permiso:', error);
      setSnackbar({
        open: true,
        message: `Error al agregar el tipo de permiso: ${error.message}`,
        severity: 'error',
      });
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Configuración
      </Typography>

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="Horario Laboral" />
          <Tab label="Multiplicadores" />
          <Tab label="Campos Dinámicos" />
          <Tab label="Gestión de Usuarios" />
          <Tab label="Tipos de Permisos" />
          <Tab label="Integraciones API" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Horario Estándar
          </Typography>
          <Grid container spacing={3}>
            {/* Days of the week selection */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Días Laborables
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                  <Button
                    key={day}
                    variant={workSchedule.workDays.includes(day) ? "contained" : "outlined"}
                    onClick={() => handleWorkDayToggle(day)}
                    sx={{ minWidth: '100px', mb: 1 }}
                  >
                    {day === 'Monday' ? 'Lunes' :
                     day === 'Tuesday' ? 'Martes' :
                     day === 'Wednesday' ? 'Miércoles' :
                     day === 'Thursday' ? 'Jueves' :
                     day === 'Friday' ? 'Viernes' :
                     day === 'Saturday' ? 'Sábado' : 'Domingo'}
                  </Button>
                ))}
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Hora de Inicio"
                type="time"
                name="startTime"
                value={workSchedule.startTime}
                onChange={handleWorkScheduleChange}
                InputLabelProps={{ shrink: true }}
                inputProps={{ step: 300 }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Hora de Fin"
                type="time"
                name="endTime"
                value={workSchedule.endTime}
                onChange={handleWorkScheduleChange}
                InputLabelProps={{ shrink: true }}
                inputProps={{ step: 300 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Inicio Almuerzo"
                type="time"
                name="lunchBreak"
                value={workSchedule.lunchBreak}
                onChange={handleWorkScheduleChange}
                InputLabelProps={{ shrink: true }}
                inputProps={{ step: 300 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Duración Almuerzo (minutos)"
                type="number"
                name="lunchDuration"
                value={workSchedule.lunchDuration}
                onChange={handleWorkScheduleChange}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            {/* Nuevas configuraciones para horas extra - TEMPORALMENTE OCULTO */}
            {false && (
              <>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                    Configuración de Horas Extra
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Horas Permitidas Fuera de Oficina (por semana)"
                    type="number"
                    name="allowedOutsideHours"
                    value={workSchedule.allowedOutsideHours}
                    onChange={handleWorkScheduleChange}
                    InputLabelProps={{ shrink: true }}
                    inputProps={{ min: 0, max: 168 }}
                    helperText="Horas semanales permitidas fuera del horario estándar"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Horas Semanales Base Asumidas"
                    type="number"
                    name="assumedWeeklyHours"
                    value={workSchedule.assumedWeeklyHours}
                    onChange={handleWorkScheduleChange}
                    InputLabelProps={{ shrink: true }}
                    inputProps={{ min: 1, max: 168 }}
                    helperText="Horas que se asumen completadas cada semana"
                  />
                </Grid>
              </>
            )}

            <Grid item xs={12}>
              <Button
                variant="contained"
                onClick={handleSaveWorkSchedule}
                sx={{ mt: 2 }}
              >
                Guardar Configuración
              </Button>
            </Grid>
          </Grid>
        </Paper>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Multiplicadores de Horas
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Nombre</TableCell>
                  <TableCell>Hora Inicio</TableCell>
                  <TableCell>Hora Fin</TableCell>
                  <TableCell>Multiplicador</TableCell>
                  <TableCell>Acciones</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {multipliers.map((multiplier) => (
                  <TableRow key={multiplier.id}>
                    <TableCell>
                      {editingMultiplier?.id === multiplier.id ? (
                        <TextField
                          value={editingMultiplier.name}
                          onChange={(e) =>
                            setEditingMultiplier({
                              ...editingMultiplier,
                              name: e.target.value,
                            })
                          }
                        />
                      ) : (
                        multiplier.name
                      )}
                    </TableCell>
                    <TableCell>
                      {editingMultiplier?.id === multiplier.id ? (
                        <TextField
                          type="time"
                          value={editingMultiplier.startTime}
                          onChange={(e) =>
                            setEditingMultiplier({
                              ...editingMultiplier,
                              startTime: e.target.value,
                            })
                          }
                        />
                      ) : (
                        multiplier.startTime
                      )}
                    </TableCell>
                    <TableCell>
                      {editingMultiplier?.id === multiplier.id ? (
                        <TextField
                          type="time"
                          value={editingMultiplier.endTime}
                          onChange={(e) =>
                            setEditingMultiplier({
                              ...editingMultiplier,
                              endTime: e.target.value,
                            })
                          }
                        />
                      ) : (
                        multiplier.endTime
                      )}
                    </TableCell>
                    <TableCell>
                      {editingMultiplier?.id === multiplier.id ? (
                        <TextField
                          type="number"
                          value={editingMultiplier.value}
                          onChange={(e) =>
                            setEditingMultiplier({
                              ...editingMultiplier,
                              value: e.target.value,
                            })
                          }
                        />
                      ) : (
                        `${multiplier.value}x`
                      )}
                    </TableCell>
                    <TableCell>
                      {editingMultiplier?.id === multiplier.id ? (
                        <IconButton
                          onClick={() => handleSaveMultiplier(editingMultiplier)}
                        >
                          <SaveIcon />
                        </IconButton>
                      ) : (
                        <>
                          <IconButton
                            onClick={() => handleEditMultiplier(multiplier)}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            onClick={() => handleDeleteMultiplier(multiplier.id)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow>
                  <TableCell>
                    <TextField
                      placeholder="Nuevo multiplicador"
                      value={newMultiplier.name}
                      onChange={(e) =>
                        setNewMultiplier({
                          ...newMultiplier,
                          name: e.target.value,
                        })
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      type="time"
                      value={newMultiplier.startTime}
                      onChange={(e) =>
                        setNewMultiplier({
                          ...newMultiplier,
                          startTime: e.target.value,
                        })
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      type="time"
                      value={newMultiplier.endTime}
                      onChange={(e) =>
                        setNewMultiplier({
                          ...newMultiplier,
                          endTime: e.target.value,
                        })
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      type="number"
                      placeholder="Valor"
                      value={newMultiplier.value}
                      onChange={(e) =>
                        setNewMultiplier({
                          ...newMultiplier,
                          value: e.target.value,
                        })
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="contained"
                      onClick={handleAddMultiplier}
                      disabled={!newMultiplier.name || !newMultiplier.value}
                    >
                      Agregar
                    </Button>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <DynamicFieldsSettings />
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <UserManagement />
      </TabPanel>

      <TabPanel value={tabValue} index={4}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Configuración de Tipos de Permisos
          </Typography>
          <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
            Gestiona los tipos de permisos disponibles en el sistema, sus límites y configuraciones.
          </Typography>

          {/* Formulario para agregar nuevo tipo de permiso */}
          <Box sx={{ mb: 4, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="subtitle1" gutterBottom>
              Agregar Nuevo Tipo de Permiso
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Nombre Interno"
                  value={newPermissionType.name}
                  onChange={(e) =>
                    setNewPermissionType({ ...newPermissionType, name: e.target.value })
                  }
                  size="small"
                  fullWidth
                  helperText="Identificador único (sin espacios)"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Etiqueta Visible"
                  value={newPermissionType.label}
                  onChange={(e) =>
                    setNewPermissionType({ ...newPermissionType, label: e.target.value })
                  }
                  size="small"
                  fullWidth
                  helperText="Nombre que verán los usuarios"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  label="Días Máximos/Año"
                  type="number"
                  value={newPermissionType.maxDaysPerYear}
                  onChange={(e) =>
                    setNewPermissionType({ ...newPermissionType, maxDaysPerYear: parseInt(e.target.value) || 0 })
                  }
                  size="small"
                  fullWidth
                  helperText="0 = Sin límite"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  select
                  label="Requiere Aprobación"
                  value={newPermissionType.requiresApproval}
                  onChange={(e) =>
                    setNewPermissionType({ ...newPermissionType, requiresApproval: e.target.value })
                  }
                  size="small"
                  fullWidth
                >
                  <MenuItem value={true}>Sí</MenuItem>
                  <MenuItem value={false}>No</MenuItem>
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <Button
                  variant="contained"
                  onClick={handleAddPermissionType}
                  disabled={!newPermissionType.name || !newPermissionType.label}
                  fullWidth
                  sx={{ height: '40px' }}
                >
                  Agregar
                </Button>
              </Grid>
            </Grid>
          </Box>

          {/* Tabla de tipos de permisos */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Nombre Interno</TableCell>
                  <TableCell>Etiqueta Visible</TableCell>
                  <TableCell>Días Máximos/Año</TableCell>
                  <TableCell>Requiere Aprobación</TableCell>
                  <TableCell>Estado</TableCell>
                  <TableCell>Acciones</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {permissionTypes.map((permissionType) => (
                  <TableRow key={permissionType.id}>
                    <TableCell>
                      {editingPermissionType?.id === permissionType.id ? (
                        <TextField
                          value={editingPermissionType.name}
                          onChange={(e) =>
                            setEditingPermissionType({ ...editingPermissionType, name: e.target.value })
                          }
                          size="small"
                        />
                      ) : (
                        permissionType.name
                      )}
                    </TableCell>
                    <TableCell>
                      {editingPermissionType?.id === permissionType.id ? (
                        <TextField
                          value={editingPermissionType.label}
                          onChange={(e) =>
                            setEditingPermissionType({ ...editingPermissionType, label: e.target.value })
                          }
                          size="small"
                        />
                      ) : (
                        permissionType.label
                      )}
                    </TableCell>
                    <TableCell>
                      {editingPermissionType?.id === permissionType.id ? (
                        <TextField
                          type="number"
                          value={editingPermissionType.maxDaysPerYear}
                          onChange={(e) =>
                            setEditingPermissionType({ ...editingPermissionType, maxDaysPerYear: parseInt(e.target.value) || 0 })
                          }
                          size="small"
                        />
                      ) : (
                        permissionType.maxDaysPerYear === 0 ? 'Sin límite' : `${permissionType.maxDaysPerYear} días`
                      )}
                    </TableCell>
                    <TableCell>
                      {editingPermissionType?.id === permissionType.id ? (
                        <TextField
                          select
                          value={editingPermissionType.requiresApproval}
                          onChange={(e) =>
                            setEditingPermissionType({ ...editingPermissionType, requiresApproval: e.target.value })
                          }
                          size="small"
                        >
                          <MenuItem value={true}>Sí</MenuItem>
                          <MenuItem value={false}>No</MenuItem>
                        </TextField>
                      ) : (
                        permissionType.requiresApproval ? '✅ Sí' : '❌ No'
                      )}
                    </TableCell>
                    <TableCell>
                      {editingPermissionType?.id === permissionType.id ? (
                        <TextField
                          select
                          value={editingPermissionType.active}
                          onChange={(e) =>
                            setEditingPermissionType({ ...editingPermissionType, active: e.target.value })
                          }
                          size="small"
                        >
                          <MenuItem value={true}>Activo</MenuItem>
                          <MenuItem value={false}>Inactivo</MenuItem>
                        </TextField>
                      ) : (
                        <Chip
                          label={permissionType.active ? 'Activo' : 'Inactivo'}
                          color={permissionType.active ? 'success' : 'default'}
                          size="small"
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      {editingPermissionType?.id === permissionType.id ? (
                        <IconButton
                          onClick={() => handleSavePermissionType(editingPermissionType)}
                        >
                          <SaveIcon />
                        </IconButton>
                      ) : (
                        <>
                          <IconButton onClick={() => handleEditPermissionType(permissionType)}>
                            <EditIcon />
                          </IconButton>
                          <IconButton onClick={() => handleDeletePermissionType(permissionType.id)}>
                            <DeleteIcon />
                          </IconButton>
                        </>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </TabPanel>

      <TabPanel value={tabValue} index={5}>
        <ApiIntegrationsSettings />
      </TabPanel>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Settings;
