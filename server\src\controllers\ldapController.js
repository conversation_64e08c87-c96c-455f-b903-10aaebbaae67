const ldapService = require('../services/ldapService');

exports.syncUsers = async (req, res) => {
  try {
    const stats = await ldapService.syncUsers();
    res.json({
      message: 'Sincronización LDAP completada',
      stats
    });
  } catch (error) {
    console.error('Error en sincronización LDAP:', error);
    res.status(500).json({ error: 'Error en la sincronización LDAP' });
  }
};

exports.getLDAPStatus = async (req, res) => {
  try {
    const isConnected = await ldapService.connect();
    res.json({
      status: isConnected ? 'connected' : 'disconnected'
    });
  } catch (error) {
    console.error('Error al verificar estado LDAP:', error);
    res.status(500).json({ error: 'Error al verificar estado LDAP' });
  } finally {
    ldapService.disconnect();
  }
};

// Nuevo método para obtener la configuración LDAP
exports.getLDAPConfig = async (req, res) => {
  try {
    const config = await ldapService.getConfig();
    res.json({ config });
  } catch (error) {
    console.error('Error al obtener configuración LDAP:', error);
    res.status(500).json({ error: 'Error al obtener configuración LDAP' });
  }
};

// Nuevo método para actualizar la configuración LDAP
exports.updateLDAPConfig = async (req, res) => {
  try {
    const { config } = req.body;
    
    if (!config) {
      return res.status(400).json({ error: 'Configuración no proporcionada' });
    }
    
    await ldapService.updateConfig(config);
    res.json({ message: 'Configuración LDAP actualizada correctamente' });
  } catch (error) {
    console.error('Error al actualizar configuración LDAP:', error);
    res.status(500).json({ error: 'Error al actualizar configuración LDAP' });
  }
};
