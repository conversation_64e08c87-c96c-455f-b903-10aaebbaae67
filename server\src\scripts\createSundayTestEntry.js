const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createSundayTestEntry() {
  try {
    console.log('🧪 CREANDO ENTRADA DE PRUEBA DEL DOMINGO...\n');

    // 1. Buscar un usuario para la prueba
    const users = await prisma.user.findMany({
      take: 1
    });

    if (users.length === 0) {
      console.log('❌ No se encontraron usuarios en el sistema');
      return;
    }

    const testUser = users[0];
    console.log(`👤 Usuario para prueba: ${testUser.name} (${testUser.email})`);

    // 2. Crear fecha del domingo más reciente
    const today = new Date();
    const dayOfWeek = today.getDay();
    
    // Calcular el domingo más reciente
    let sundayDate = new Date(today);
    if (dayOfWeek === 0) {
      // Hoy es domingo, usar hoy
      sundayDate = today;
    } else {
      // Retroceder al domingo anterior
      sundayDate.setDate(today.getDate() - dayOfWeek);
    }

    // Configurar horario del domingo (10:00 AM - 2:00 PM)
    const startTime = new Date(sundayDate);
    startTime.setHours(10, 0, 0, 0);

    const endTime = new Date(sundayDate);
    endTime.setHours(14, 0, 0, 0);

    const hoursWorked = (endTime - startTime) / (1000 * 60 * 60); // 4 horas

    console.log(`📅 Fecha del domingo: ${sundayDate.toISOString().split('T')[0]}`);
    console.log(`⏰ Horario: ${startTime.toTimeString().split(' ')[0]} - ${endTime.toTimeString().split(' ')[0]}`);
    console.log(`🕐 Horas trabajadas: ${hoursWorked}h`);

    // 3. Verificar si ya existe una entrada similar
    const existingEntry = await prisma.timeEntry.findFirst({
      where: {
        userId: testUser.id,
        startTime: {
          gte: new Date(sundayDate.getFullYear(), sundayDate.getMonth(), sundayDate.getDate()),
          lt: new Date(sundayDate.getFullYear(), sundayDate.getMonth(), sundayDate.getDate() + 1)
        },
        title: 'Trabajo Domingo - Prueba Multiplicador'
      }
    });

    if (existingEntry) {
      console.log(`ℹ️ Ya existe una entrada de prueba del domingo (ID: ${existingEntry.id})`);
      console.log(`   Estado: ${existingEntry.status}`);
      console.log(`   Horas: ${existingEntry.hoursWorked}h`);
      
      if (existingEntry.status !== 'APPROVED') {
        console.log('🔄 Actualizando estado a APPROVED...');
        await prisma.timeEntry.update({
          where: { id: existingEntry.id },
          data: { status: 'APPROVED' }
        });
        console.log('✅ Estado actualizado a APPROVED');
      }
      
      return existingEntry;
    }

    // 4. Crear nueva entrada del domingo
    const sundayEntry = await prisma.timeEntry.create({
      data: {
        title: 'Trabajo Domingo - Prueba Multiplicador',
        description: 'Entrada de prueba para verificar que las horas del domingo se multiplican por 2.0x',
        startTime: startTime,
        endTime: endTime,
        hoursWorked: hoursWorked,
        status: 'APPROVED',
        userId: testUser.id
      }
    });

    console.log('\n✅ ENTRADA DEL DOMINGO CREADA:');
    console.log(`   🆔 ID: ${sundayEntry.id}`);
    console.log(`   📝 Título: ${sundayEntry.title}`);
    console.log(`   👤 Usuario: ${testUser.name}`);
    console.log(`   📅 Fecha: ${sundayEntry.startTime.toISOString().split('T')[0]}`);
    console.log(`   ⏰ Horario: ${sundayEntry.startTime.toTimeString().split(' ')[0]} - ${sundayEntry.endTime.toTimeString().split(' ')[0]}`);
    console.log(`   🕐 Horas: ${sundayEntry.hoursWorked}h`);
    console.log(`   ✅ Estado: ${sundayEntry.status}`);

    // 5. Simular cálculo del multiplicador
    console.log('\n🧮 SIMULACIÓN DEL CÁLCULO:');
    
    // Obtener multiplicadores activos
    const timeMultipliers = await prisma.timeMultiplier.findMany({
      where: { isActive: true }
    });

    // Función de cálculo (copiada del dashboard)
    const calculateMultiplier = (entryStartTime) => {
      const entryDate = new Date(entryStartTime);
      const dayOfWeek = entryDate.getDay();
      const startHour = entryDate.getHours();
      const startMinute = entryDate.getMinutes();

      let applicableMultiplier = 1.0;
      let multiplierReasons = [];

      // Domingos automáticamente tienen multiplicador 2.0x
      if (dayOfWeek === 0) {
        applicableMultiplier = Math.max(applicableMultiplier, 2.0);
        multiplierReasons.push(`Domingo (considerado feriado): 2.0x`);
      }

      // Verificar multiplicadores por horario
      timeMultipliers.forEach(mult => {
        if (mult.startTime && mult.endTime) {
          const [multStartHour, multStartMin] = mult.startTime.split(':').map(Number);
          const [multEndHour, multEndMin] = mult.endTime.split(':').map(Number);

          const multStartMinutes = multStartHour * 60 + multStartMin;
          const multEndMinutes = multEndHour * 60 + multEndMin;
          const entryStartMinutes = startHour * 60 + startMinute;

          let isInTimeRange = false;
          if (multStartMinutes > multEndMinutes) {
            isInTimeRange = (entryStartMinutes >= multStartMinutes) || (entryStartMinutes <= multEndMinutes);
          } else {
            isInTimeRange = (entryStartMinutes >= multStartMinutes) && (entryStartMinutes <= multEndMinutes);
          }

          if (isInTimeRange) {
            applicableMultiplier = Math.max(applicableMultiplier, mult.value);
            multiplierReasons.push(`${mult.name} (${mult.startTime}-${mult.endTime}): ${mult.value}x`);
          }
        }
      });

      return { multiplier: applicableMultiplier, reasons: multiplierReasons };
    };

    const { multiplier, reasons } = calculateMultiplier(sundayEntry.startTime);
    const weightedHours = sundayEntry.hoursWorked * multiplier;

    console.log(`   📊 Multiplicador aplicado: ${multiplier}x`);
    console.log(`   💎 Horas ponderadas: ${weightedHours}h`);
    console.log(`   📋 Razones: ${reasons.join(', ')}`);
    console.log(`   🎯 Columna destino: ${multiplier >= 2.0 ? 'Horas × 2.0' : multiplier >= 1.5 ? 'Horas × 1.5' : 'Sin multiplicador'}`);

    console.log('\n🎯 PRÓXIMOS PASOS:');
    console.log('1. Ve al Dashboard → Control de Horas Extra y Compensaciones');
    console.log(`2. Busca la semana que incluye ${sundayDate.toISOString().split('T')[0]}`);
    console.log(`3. Verifica que ${testUser.name} tenga ${weightedHours}h en la columna "Horas × 2.0"`);

    return sundayEntry;

  } catch (error) {
    console.error('❌ ERROR AL CREAR ENTRADA:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  createSundayTestEntry();
}

module.exports = { createSundayTestEntry };
