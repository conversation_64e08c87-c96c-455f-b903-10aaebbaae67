const express = require('express');
const router = express.Router();
const reportController = require('../controllers/reportController');
const { authenticateToken } = require('../middleware/auth');

// Todas las rutas requieren autenticación
router.use(authenticateToken);

// Rutas para reportes
router.get('/time', reportController.getTimeReport);
router.get('/kanban', reportController.getKanbanReport);
router.get('/user', reportController.getUserReport);
router.get('/task', reportController.getTaskReport);
router.get('/status', reportController.getStatusReport);

// Nuevos reportes ejecutivos y técnicos
router.get('/executive-dashboard', reportController.getExecutiveDashboard);
router.get('/productivity', reportController.getProductivityReport);
router.get('/financial', reportController.getFinancialReport);
router.get('/performance', reportController.getPerformanceReport);

// Reportes técnicos
router.get('/attendance', reportController.getAttendanceReport);
router.get('/efficiency', reportController.getEfficiencyReport);
router.get('/workload', reportController.getWorkloadReport);
router.get('/predictive', reportController.getPredictiveReport);

// Reportes de permisos
router.get('/permissions-summary', reportController.getPermissionsSummaryReport);
router.get('/permissions-by-type', reportController.getPermissionsByTypeReport);
router.get('/permissions-by-user', reportController.getPermissionsByUserReport);
router.get('/permissions-trends', reportController.getPermissionsTrendsReport);

module.exports = router;
