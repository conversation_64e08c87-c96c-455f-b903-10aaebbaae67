-- Script de verificación para la migración de separación de tareas
-- Ejecutar después de aplicar la migración

\echo '=== VERIFICACIÓN DE MIGRACIÓN ==='
\echo ''

-- 1. Verificar que los nuevos estados existen
\echo '1. Verificando estados de TaskStatus:'
SELECT enumlabel as "Estados Disponibles"
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'TaskStatus')
ORDER BY enumlabel;

\echo ''

-- 2. Verificar que el campo source existe
\echo '2. Verificando campo source en tabla Task:'
SELECT 
    column_name as "<PERSON><PERSON><PERSON>", 
    data_type as "Tipo", 
    column_default as "Valor por Defecto",
    is_nullable as "Permite NULL"
FROM information_schema.columns 
WHERE table_name = 'Task' AND column_name = 'source';

\echo ''

-- 3. Verificar distribución de tareas por source
\echo '3. Distribución de tareas por origen:'
SELECT 
    source as "Origen",
    COUNT(*) as "Cantidad de Tareas"
FROM "Task" 
GROUP BY source
ORDER BY source;

\echo ''

-- 4. Verificar distribución de tareas por estado
\echo '4. Distribución de tareas por estado:'
SELECT 
    status as "Estado",
    COUNT(*) as "Cantidad"
FROM "Task" 
GROUP BY status
ORDER BY status;

\echo ''

-- 5. Verificar tareas manuales completadas
\echo '5. Tareas manuales completadas:'
SELECT 
    COUNT(*) as "Tareas Manuales Completadas"
FROM "Task" 
WHERE source = 'MANUAL_ENTRY' AND status = 'COMPLETED';

\echo ''

-- 6. Verificar tareas del kanban archivadas
\echo '6. Tareas del kanban archivadas:'
SELECT 
    COUNT(*) as "Tareas Kanban Archivadas"
FROM "Task" 
WHERE source = 'KANBAN' AND status = 'ARCHIVED';

\echo ''

-- 7. Mostrar algunas tareas de ejemplo
\echo '7. Ejemplos de tareas (últimas 5):'
SELECT 
    LEFT(title, 30) as "Título",
    status as "Estado",
    source as "Origen",
    "createdAt"::date as "Fecha Creación"
FROM "Task" 
ORDER BY "createdAt" DESC 
LIMIT 5;

\echo ''
\echo '=== VERIFICACIÓN COMPLETADA ==='
\echo ''
\echo 'Si todos los resultados son correctos, la migración fue exitosa.'
\echo 'Deberías ver:'
\echo '- Estados: ARCHIVED, COMPLETED, DONE, IN_PROGRESS, REVIEW, TODO'
\echo '- Campo source con tipo TEXT y valor por defecto KANBAN'
\echo '- Tareas distribuidas entre KANBAN y MANUAL_ENTRY'
