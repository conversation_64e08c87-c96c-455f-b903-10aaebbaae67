const http = require('http');
const https = require('https');
const url = require('url');

// Configuración
const PROXY_PORT = 8081;
const TARGET_HOST = '************';
const TARGET_PORT = 8080;

// Crear agente HTTPS que ignore certificados SSL
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

const server = http.createServer((req, res) => {
  console.log(`📥 Proxy request: ${req.method} ${req.url}`);
  
  // Configurar CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, authtoken, Accept, PORTALID');
  
  // Manejar preflight OPTIONS
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // Construir URL de destino
  const targetUrl = `https://${TARGET_HOST}:${TARGET_PORT}${req.url}`;
  
  // Configurar petición hacia ManageEngine
  const options = {
    method: req.method,
    headers: { ...req.headers },
    agent: httpsAgent
  };
  
  // Eliminar headers problemáticos
  delete options.headers.host;
  delete options.headers.connection;
  
  console.log(`🔗 Forwarding to: ${targetUrl}`);
  console.log(`📋 Headers:`, options.headers);
  
  const proxyReq = https.request(targetUrl, options, (proxyRes) => {
    console.log(`✅ Response status: ${proxyRes.statusCode}`);
    
    // Copiar headers de respuesta
    Object.keys(proxyRes.headers).forEach(key => {
      res.setHeader(key, proxyRes.headers[key]);
    });
    
    // Configurar status code
    res.writeHead(proxyRes.statusCode);
    
    // Pipe la respuesta
    proxyRes.pipe(res);
  });
  
  proxyReq.on('error', (error) => {
    console.error('❌ Proxy error:', error.message);
    res.writeHead(500);
    res.end(`Proxy Error: ${error.message}`);
  });
  
  // Pipe el request body si existe
  req.pipe(proxyReq);
});

server.listen(PROXY_PORT, '0.0.0.0', () => {
  console.log(`🚀 ManageEngine Proxy running on port ${PROXY_PORT}`);
  console.log(`📡 Forwarding to: https://${TARGET_HOST}:${TARGET_PORT}`);
  console.log(`🔗 Use this URL in Docker: http://************:${PROXY_PORT}`);
});

server.on('error', (error) => {
  console.error('❌ Server error:', error.message);
});
