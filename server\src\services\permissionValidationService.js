const { PrismaClient } = require('@prisma/client');
const { spanishToEnum } = require('../utils/permissionTypeMapper');
const prisma = new PrismaClient();

/**
 * Servicio de validaciones para permisos
 * Proporciona validaciones completas para solicitudes de permisos
 */
class PermissionValidationService {



  /**
   * Validar una solicitud de permiso completa
   * @param {Object} permissionData - Datos del permiso a validar
   * @param {string} userId - ID del usuario solicitante
   * @param {string} permissionId - ID del permiso (para ediciones, opcional)
   * @returns {Object} Resultado de validación con errores si los hay
   */
  async validatePermissionRequest(permissionData, userId, permissionId = null) {
    const errors = [];
    const warnings = [];

    console.log('🔍 Iniciando validación completa:', {
      type: permissionData.type,
      userId,
      startTime: permissionData.startTime,
      endTime: permissionData.endTime
    });

    try {
      // 1. Validaciones básicas de fechas
      const dateValidation = this.validateDates(permissionData);
      if (!dateValidation.isValid) {
        errors.push(...dateValidation.errors);
      }
      
      // 2. Validación de solapamiento
      const overlapValidation = await this.validateOverlap(permissionData, userId, permissionId);
      if (!overlapValidation.isValid) {
        errors.push(...overlapValidation.errors);
        warnings.push(...overlapValidation.warnings);
      }
      
      // 3. Validación de límites anuales
      const limitValidation = await this.validateAnnualLimits(permissionData, userId, permissionId);
      if (!limitValidation.isValid) {
        errors.push(...limitValidation.errors);
        warnings.push(...limitValidation.warnings);
      }
      
      // 4. Validaciones específicas por tipo
      const typeValidation = await this.validateByType(permissionData, userId);
      if (!typeValidation.isValid) {
        errors.push(...typeValidation.errors);
        warnings.push(...typeValidation.warnings);
      }
      
      // 5. Validaciones por rol
      const roleValidation = await this.validateByRole(permissionData, userId);
      if (!roleValidation.isValid) {
        errors.push(...roleValidation.errors);
      }
      
      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        details: {
          dateValidation,
          overlapValidation,
          limitValidation,
          typeValidation,
          roleValidation
        }
      };
      
    } catch (error) {
      console.error('❌ Error en validación de permisos:', error);
      console.error('📋 Stack trace:', error.stack);
      console.error('📋 Datos de entrada:', { permissionData, userId, permissionId });
      return {
        isValid: false,
        errors: [`Error interno en la validación: ${error.message}`],
        warnings: [],
        details: { error: error.message, stack: error.stack }
      };
    }
  }
  
  /**
   * Validar fechas básicas
   */
  validateDates(permissionData) {
    const errors = [];
    const { startTime, endTime } = permissionData;
    
    const start = new Date(startTime);
    const end = new Date(endTime);
    const now = new Date();
    
    // Validar que las fechas sean válidas
    if (isNaN(start.getTime())) {
      errors.push('Fecha de inicio inválida');
    }
    
    if (isNaN(end.getTime())) {
      errors.push('Fecha de fin inválida');
    }
    
    if (errors.length > 0) {
      return { isValid: false, errors };
    }
    
    // Validar que la fecha de fin sea posterior o igual a la de inicio
    // Para permisos de un día, las fechas pueden ser iguales
    if (end < start) {
      errors.push('La fecha de fin no puede ser anterior a la fecha de inicio');
    }
    
    // Validar que no sea muy en el pasado (más de 30 días)
    const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
    if (start < thirtyDaysAgo) {
      errors.push('No se pueden solicitar permisos con más de 30 días de antigüedad');
    }
    
    // Validar que no sea muy en el futuro (más de 1 año)
    const oneYearFromNow = new Date(now.getTime() + (365 * 24 * 60 * 60 * 1000));
    if (start > oneYearFromNow) {
      errors.push('No se pueden solicitar permisos con más de 1 año de anticipación');
    }
    
    // Validar duración máxima (no más de 90 días consecutivos)
    const diffInDays = (end - start) / (1000 * 60 * 60 * 24);
    if (diffInDays > 90) {
      errors.push('La duración del permiso no puede exceder 90 días consecutivos');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      duration: diffInDays
    };
  }
  
  /**
   * Validar solapamiento con otros permisos
   */
  async validateOverlap(permissionData, userId, permissionId = null) {
    const errors = [];
    const warnings = [];
    const { startTime, endTime } = permissionData;
    
    // Buscar permisos que se solapen
    const whereClause = {
      userId,
      status: { in: ['PENDING', 'APPROVED'] },
      OR: [
        // Caso 1: El nuevo permiso empieza durante un permiso existente
        {
          AND: [
            { startTime: { lte: new Date(startTime) } },
            { endTime: { gt: new Date(startTime) } }
          ]
        },
        // Caso 2: El nuevo permiso termina durante un permiso existente
        {
          AND: [
            { startTime: { lt: new Date(endTime) } },
            { endTime: { gte: new Date(endTime) } }
          ]
        },
        // Caso 3: El nuevo permiso contiene completamente un permiso existente
        {
          AND: [
            { startTime: { gte: new Date(startTime) } },
            { endTime: { lte: new Date(endTime) } }
          ]
        },
        // Caso 4: Un permiso existente contiene completamente el nuevo permiso
        {
          AND: [
            { startTime: { lte: new Date(startTime) } },
            { endTime: { gte: new Date(endTime) } }
          ]
        }
      ]
    };
    
    // Excluir el permiso actual si es una edición
    if (permissionId) {
      whereClause.id = { not: permissionId };
    }
    
    const overlappingPermissions = await prisma.permission.findMany({
      where: whereClause,
      select: {
        id: true,
        type: true,
        startTime: true,
        endTime: true,
        status: true,
        reason: true
      }
    });
    
    if (overlappingPermissions.length > 0) {
      const approvedOverlaps = overlappingPermissions.filter(p => p.status === 'APPROVED');
      const pendingOverlaps = overlappingPermissions.filter(p => p.status === 'PENDING');
      
      if (approvedOverlaps.length > 0) {
        errors.push(`Ya tienes ${approvedOverlaps.length} permiso(s) aprobado(s) que se solapa(n) con estas fechas`);
      }
      
      if (pendingOverlaps.length > 0) {
        warnings.push(`Tienes ${pendingOverlaps.length} permiso(s) pendiente(s) que se solapa(n) con estas fechas`);
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      overlappingPermissions
    };
  }
  
  /**
   * Validar límites anuales por tipo de permiso
   */
  async validateAnnualLimits(permissionData, userId, permissionId = null) {
    const errors = [];
    const warnings = [];
    const { type, startTime, endTime } = permissionData;

    console.log('🔍 Validando límites anuales para:', { type, userId });

    // Obtener configuración del tipo de permiso
    const permissionTypeConfig = await prisma.permissionTypeConfig.findFirst({
      where: { name: type, active: true }
    });

    console.log('📋 Configuración encontrada:', permissionTypeConfig);

    if (!permissionTypeConfig || permissionTypeConfig.maxDaysPerYear === 0) {
      // Sin límite configurado
      console.log('✅ Sin límite configurado para este tipo');
      return { isValid: true, errors: [], warnings: [] };
    }
    
    const maxDaysPerYear = permissionTypeConfig.maxDaysPerYear;
    
    // Calcular días del año actual
    const currentYear = new Date().getFullYear();
    const yearStart = new Date(currentYear, 0, 1);
    const yearEnd = new Date(currentYear, 11, 31, 23, 59, 59);
    
    // Mapear el tipo de permiso al enum correcto
    const enumType = spanishToEnum(type);

    // Obtener permisos aprobados del mismo tipo en el año actual
    const whereClause = {
      userId,
      type: enumType,
      status: 'APPROVED',
      startTime: { gte: yearStart },
      endTime: { lte: yearEnd }
    };
    
    // Excluir el permiso actual si es una edición
    if (permissionId) {
      whereClause.id = { not: permissionId };
    }
    
    const existingPermissions = await prisma.permission.findMany({
      where: whereClause,
      select: {
        startTime: true,
        endTime: true
      }
    });
    
    // Calcular días ya utilizados
    let daysUsed = 0;
    for (const permission of existingPermissions) {
      const days = await this.calculateWorkingDays(permission.startTime, permission.endTime);
      daysUsed += days;
    }

    // Calcular días del nuevo permiso
    const newPermissionDays = await this.calculateWorkingDays(new Date(startTime), new Date(endTime));
    
    // Validar límite
    const totalDaysAfterRequest = daysUsed + newPermissionDays;
    
    if (totalDaysAfterRequest > maxDaysPerYear) {
      errors.push(
        `Límite anual excedido para ${permissionTypeConfig.label}. ` +
        `Límite: ${maxDaysPerYear} días, Ya utilizados: ${daysUsed} días, ` +
        `Solicitados: ${newPermissionDays} días, Total: ${totalDaysAfterRequest} días`
      );
    } else if (totalDaysAfterRequest > maxDaysPerYear * 0.8) {
      warnings.push(
        `Te acercas al límite anual de ${permissionTypeConfig.label}. ` +
        `Utilizados: ${daysUsed}/${maxDaysPerYear} días`
      );
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      daysUsed,
      maxDaysPerYear,
      newPermissionDays,
      remainingDays: maxDaysPerYear - daysUsed
    };
  }
  
  /**
   * Calcular días laborales entre dos fechas usando configuración de Settings
   */
  async calculateWorkingDays(startDate, endDate) {
    let workingDays = 0;
    const currentDate = new Date(startDate);
    const end = new Date(endDate);

    // Obtener configuración de días laborales desde Settings
    const workSchedules = await prisma.workSchedule.findMany();
    let workDays = [1, 2, 3, 4, 5]; // Default: Lunes-Viernes

    if (workSchedules.length > 0) {
      // Extraer días laborales de la configuración
      workDays = workSchedules.map(schedule => schedule.dayOfWeek);
    }

    console.log('📅 Días laborales configurados:', workDays);

    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay();
      // Verificar si el día está en la configuración de días laborales
      if (workDays.includes(dayOfWeek)) {
        workingDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    console.log(`📊 Días laborales calculados entre ${startDate.toDateString()} y ${end.toDateString()}: ${workingDays}`);
    return workingDays;
  }

  /**
   * Obtener configuración del horario laboral desde Settings
   */
  async getWorkScheduleConfig() {
    const workSchedules = await prisma.workSchedule.findMany();

    if (workSchedules.length === 0) {
      // Configuración por defecto si no hay configuración
      return {
        startTime: '09:00',
        endTime: '18:00',
        lunchBreak: '13:00',
        lunchDuration: 60,
        workDays: [1, 2, 3, 4, 5] // Lunes-Viernes
      };
    }

    // Usar la primera configuración como template
    const template = workSchedules[0];
    const workDays = workSchedules.map(schedule => schedule.dayOfWeek);

    return {
      startTime: template.startTime,
      endTime: template.endTime,
      lunchBreak: template.lunchBreak,
      lunchDuration: template.lunchDuration,
      workDays
    };
  }

  /**
   * Calcular horas laborales netas de un día completo (descontando almuerzo)
   * Esto se usa para convertir permisos de "días" a "horas" considerando la configuración de Settings
   */
  async calculateDailyWorkHours() {
    const workScheduleConfig = await this.getWorkScheduleConfig();

    const startTime = workScheduleConfig.startTime; // ej: '09:00'
    const endTime = workScheduleConfig.endTime;     // ej: '18:00'
    const lunchDuration = workScheduleConfig.lunchDuration; // ej: 60 minutos

    // Convertir horas a minutos
    const [startHour, startMin] = startTime.split(':').map(Number);
    const [endHour, endMin] = endTime.split(':').map(Number);

    const startMinutes = startHour * 60 + startMin;
    const endMinutes = endHour * 60 + endMin;

    // Calcular total de minutos trabajados (descontando almuerzo)
    const totalMinutes = (endMinutes - startMinutes) - lunchDuration;
    const totalHours = totalMinutes / 60;

    console.log(`⏰ Horas laborales netas por día: ${totalHours} horas (${startTime}-${endTime}, almuerzo: ${lunchDuration}min)`);
    return totalHours;
  }

  /**
   * Calcular horas totales de un permiso considerando la configuración laboral
   * Para permisos de días completos, usa las horas laborales netas por día
   */
  async calculatePermissionHours(startTime, endTime) {
    const start = new Date(startTime);
    const end = new Date(endTime);

    // Verificar si es un permiso de día completo (misma fecha, horario laboral completo)
    const workScheduleConfig = await this.getWorkScheduleConfig();
    const startTimeStr = start.toTimeString().substring(0, 5); // 'HH:MM'
    const endTimeStr = end.toTimeString().substring(0, 5);     // 'HH:MM'

    const isFullDay = (
      start.toDateString() === end.toDateString() && // Mismo día
      startTimeStr === workScheduleConfig.startTime && // Inicia con horario laboral
      endTimeStr === workScheduleConfig.endTime        // Termina con horario laboral
    );

    if (isFullDay) {
      // Para días completos, usar horas laborales netas
      const dailyHours = await this.calculateDailyWorkHours();
      const days = await this.calculateWorkingDays(start, end);
      const totalHours = days * dailyHours;

      console.log(`📊 Permiso de día completo: ${days} día(s) × ${dailyHours} horas/día = ${totalHours} horas`);
      return totalHours;
    } else {
      // Para permisos por horas específicas, calcular diferencia real
      const diffMs = end.getTime() - start.getTime();
      const diffHours = diffMs / (1000 * 60 * 60);

      console.log(`📊 Permiso por horas específicas: ${diffHours} horas`);
      return diffHours;
    }
  }

  /**
   * Validaciones específicas por tipo de permiso
   */
  async validateByType(permissionData, userId) {
    const errors = [];
    const warnings = [];
    const { type } = permissionData;
    
    switch (type) {
      case 'Compensacion':
        // Validar que tenga horas extra disponibles
        const overtimeValidation = await this.validateOvertimeCompensation(permissionData, userId);
        if (!overtimeValidation.isValid) {
          errors.push(...overtimeValidation.errors);
        }
        break;
        
      case 'Enfermedad':
        // Para permisos médicos, validar duración razonable
        const duration = await this.calculateWorkingDays(
          new Date(permissionData.startTime),
          new Date(permissionData.endTime)
        );
        if (duration > 15) {
          warnings.push('Los permisos médicos de más de 15 días pueden requerir documentación adicional');
        }
        break;
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
  
  /**
   * Validar compensación de horas extra
   */
  async validateOvertimeCompensation(permissionData, userId) {
    // Esta validación ya existe en el frontend, pero la reforzamos aquí
    // TODO: Implementar lógica de validación de horas extra disponibles
    return { isValid: true, errors: [] };
  }
  
  /**
   * Validaciones por rol de usuario
   */
  async validateByRole(permissionData, userId) {
    const errors = [];
    
    // Obtener información del usuario
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true }
    });
    
    if (!user) {
      errors.push('Usuario no encontrado');
      return { isValid: false, errors };
    }
    
    // Validaciones específicas por rol
    switch (user.role) {
      case 'EMPLOYEE':
        // Los empleados no pueden solicitar ciertos tipos de permisos
        // TODO: Implementar restricciones específicas si es necesario
        break;
        
      case 'MANAGER':
      case 'ADMIN':
        // Managers y admins tienen menos restricciones
        break;
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

module.exports = new PermissionValidationService();
