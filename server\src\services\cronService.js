const cron = require('node-cron');
const backupService = require('./backupService');
const ldapService = require('./ldapService');
const winston = require('winston');

class CronService {
  constructor() {
    this.logger = winston.createLogger({
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.File({ 
          filename: 'logs/cron.log'
        })
      ]
    });
  }

  startJobs() {
    // Realizar backup automático todos los días a las 3 AM
    cron.schedule('0 3 * * *', async () => {
      try {
        this.logger.info('Iniciando backup automático programado');
        await backupService.createBackup();
        this.logger.info('Backup automático completado exitosamente');
      } catch (error) {
        this.logger.error('Error en backup automático:', error);
      }
    });

    // Sincronizar usuarios LDAP cada hora
    cron.schedule('0 * * * *', async () => {
      try {
        this.logger.info('Iniciando sincronización LDAP programada');
        await ldapService.syncUsers();
        this.logger.info('Sincronización LDAP completada exitosamente');
      } catch (error) {
        this.logger.error('Error en sincronización LDAP:', error);
      }
    });

    this.logger.info('Trabajos programados iniciados');
  }
}

module.exports = new CronService();
