import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Button,
  CircularProgress,
  Alert,
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  TextField,
  Divider,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Sync as SyncIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Save as SaveIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../config';

const LDAPSettings = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [status, setStatus] = useState(null);
  const [syncStats, setSyncStats] = useState(null);
  const [config, setConfig] = useState({
    url: '',
    baseDN: '',
    username: '',
    password: '',
    userSearchBase: '',
    userSearchFilter: '',
    groupSearchBase: '',
    groupSearchFilter: '',
    tlsEnabled: false
  });

  useEffect(() => {
    checkLDAPStatus();
    fetchLDAPConfig();
  }, []);

  const fetchLDAPConfig = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_URL}/api/ldap/config`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      setConfig(response.data.config);
    } catch (err) {
      console.error('Error al obtener configuración LDAP:', err);
      setError('Error al obtener la configuración LDAP');
    } finally {
      setLoading(false);
    }
  };

  const checkLDAPStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get(`${API_URL}/api/ldap/status`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      setStatus(response.data.status);
    } catch (err) {
      console.error('Error al verificar estado LDAP:', err);
      setError('Error al verificar el estado de la conexión LDAP');
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      const response = await axios.post(
        `${API_URL}/api/ldap/sync`,
        {},
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );
      setSyncStats(response.data.stats);
      setSuccess('Sincronización completada exitosamente');
    } catch (err) {
      console.error('Error en sincronización LDAP:', err);
      setError('Error al sincronizar usuarios LDAP');
    } finally {
      setLoading(false);
    }
  };

  const handleConfigChange = (e) => {
    const { name, value, type, checked } = e.target;
    setConfig({
      ...config,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const saveConfig = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      await axios.post(
        `${API_URL}/api/ldap/config`,
        { config },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );
      setSuccess('Configuración LDAP guardada correctamente');
      checkLDAPStatus(); // Verificar estado después de guardar
    } catch (err) {
      console.error('Error al guardar configuración LDAP:', err);
      setError('Error al guardar la configuración LDAP');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Configuración LDAP
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Configuración LDAP */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h2" gutterBottom>
                  Parámetros de Conexión LDAP
                </Typography>
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="URL del Servidor LDAP"
                      name="url"
                      value={config.url}
                      onChange={handleConfigChange}
                      placeholder="ldap://ldap.example.com:389"
                      margin="normal"
                      helperText="Ejemplo: ldap://ldap.example.com:389"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Base DN"
                      name="baseDN"
                      value={config.baseDN}
                      onChange={handleConfigChange}
                      placeholder="dc=example,dc=com"
                      margin="normal"
                      helperText="Base DN para búsquedas LDAP"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Usuario Bind DN"
                      name="username"
                      value={config.username}
                      onChange={handleConfigChange}
                      placeholder="cn=admin,dc=example,dc=com"
                      margin="normal"
                      helperText="Usuario para autenticarse con el servidor LDAP"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Contraseña"
                      name="password"
                      type="password"
                      value={config.password}
                      onChange={handleConfigChange}
                      margin="normal"
                      helperText="Contraseña del usuario bind"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="subtitle1" gutterBottom>
                      Configuración de Búsqueda
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Base de Búsqueda de Usuarios"
                      name="userSearchBase"
                      value={config.userSearchBase}
                      onChange={handleConfigChange}
                      placeholder="ou=users,dc=example,dc=com"
                      margin="normal"
                      helperText="DN base para buscar usuarios"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Filtro de Búsqueda de Usuarios"
                      name="userSearchFilter"
                      value={config.userSearchFilter}
                      onChange={handleConfigChange}
                      placeholder="(objectClass=person)"
                      margin="normal"
                      helperText="Filtro LDAP para encontrar usuarios"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Base de Búsqueda de Grupos"
                      name="groupSearchBase"
                      value={config.groupSearchBase}
                      onChange={handleConfigChange}
                      placeholder="ou=groups,dc=example,dc=com"
                      margin="normal"
                      helperText="DN base para buscar grupos"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Filtro de Búsqueda de Grupos"
                      name="groupSearchFilter"
                      value={config.groupSearchFilter}
                      onChange={handleConfigChange}
                      placeholder="(objectClass=groupOfNames)"
                      margin="normal"
                      helperText="Filtro LDAP para encontrar grupos"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={config.tlsEnabled}
                          onChange={handleConfigChange}
                          name="tlsEnabled"
                        />
                      }
                      label="Habilitar TLS/SSL"
                    />
                  </Grid>
                </Grid>
              </CardContent>
              <CardActions>
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={saveConfig}
                  disabled={loading}
                >
                  Guardar Configuración
                </Button>
              </CardActions>
            </Card>
          </Grid>

          {/* Estado de la Conexión */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h2" gutterBottom>
                  Estado de la Conexión
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mt: 2,
                  }}
                >
                  {loading ? (
                    <CircularProgress size={24} sx={{ mr: 1 }} />
                  ) : status === 'connected' ? (
                    <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                  ) : (
                    <ErrorIcon color="error" sx={{ mr: 1 }} />
                  )}
                  <Typography>
                    {loading
                      ? 'Verificando conexión...'
                      : status === 'connected'
                      ? 'Conectado'
                      : 'Desconectado'}
                  </Typography>
                </Box>
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  onClick={checkLDAPStatus}
                  disabled={loading}
                >
                  Verificar Estado
                </Button>
              </CardActions>
            </Card>
          </Grid>

          {/* Sincronización de Usuarios */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h2" gutterBottom>
                  Sincronización de Usuarios
                </Typography>
                {syncStats && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      Usuarios creados: {syncStats.created}
                    </Typography>
                    <Typography variant="body2">
                      Usuarios actualizados: {syncStats.updated}
                    </Typography>
                    <Typography variant="body2">
                      Errores: {syncStats.errors}
                    </Typography>
                  </Box>
                )}
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  startIcon={<SyncIcon />}
                  onClick={handleSync}
                  disabled={loading}
                >
                  Sincronizar Ahora
                </Button>
              </CardActions>
            </Card>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default LDAPSettings;
