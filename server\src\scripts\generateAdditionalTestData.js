const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function generateAdditionalTestData() {
  try {
    // Create work schedules for a regular week
    for (let i = 0; i < 7; i++) {
      await prisma.workSchedule.create({
        data: {
          dayOfWeek: i,
          startTime: '09:00',
          endTime: '18:00',
          lunchBreak: '13:00',
          lunchDuration: 60,
          multiplier: i >= 5 ? 1.5 : 1.0, // Higher multiplier for weekends
          isHoliday: i >= 5 // Weekends are holidays
        }
      });
    }

    // Create time multipliers
    const nightShiftMultiplier = await prisma.timeMultiplier.create({
      data: {
        name: 'Night Shift',
        value: 1.25,
        startTime: '22:00',
        endTime: '06:00',
        description: 'Additional pay for night shift hours'
      }
    });

    const holidayMultiplier = await prisma.timeMultiplier.create({
      data: {
        name: 'Holiday Hours',
        value: 2.0,
        description: 'Double pay for holiday hours'
      }
    });

    // Create holidays
    const holidays = [
      {
        name: 'New Year\'s Day',
        date: new Date('2024-01-01'),
        multiplier: 2.0,
        isRecurring: true
      },
      {
        name: 'Independence Day',
        date: new Date('2024-07-04'),
        multiplier: 2.0,
        isRecurring: true
      },
      {
        name: 'Christmas Day',
        date: new Date('2024-12-25'),
        multiplier: 2.5,
        isRecurring: true
      }
    ];

    for (const holiday of holidays) {
      await prisma.holiday.create({
        data: holiday
      });
    }

    // Create some notifications
    await prisma.notification.create({
      data: {
        type: 'TIME_ENTRY_APPROVAL',
        title: 'Overtime Approval Required',
        message: 'New overtime entry requires your approval',
        userId: (await prisma.user.findFirst({ where: { role: 'MANAGER' } })).id
      }
    });

    // Create task comments and attachments
    const task = await prisma.task.findFirst();
    if (task) {
      await prisma.comment.create({
        data: {
          content: 'Implementation is progressing well',
          taskId: task.id
        }
      });

      await prisma.attachment.create({
        data: {
          name: 'requirements.pdf',
          url: 'https://example.com/files/requirements.pdf',
          type: 'application/pdf',
          size: 1024 * 1024, // 1MB
          taskId: task.id
        }
      });
    }

    // Create audit logs
    const timeEntry = await prisma.timeEntry.findFirst();
    if (timeEntry) {
      await prisma.auditLog.create({
        data: {
          action: 'UPDATE',
          entityType: 'TIME_ENTRY',
          entityId: timeEntry.id,
          oldValue: JSON.stringify({ status: 'PENDING' }),
          newValue: JSON.stringify({ status: 'APPROVED' }),
          timeEntryId: timeEntry.id
        }
      });
    }

    console.log('Additional test data generated successfully!');
  } catch (error) {
    console.error('Error generating additional test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

generateAdditionalTestData();