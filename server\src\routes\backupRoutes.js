const express = require('express');
const router = express.Router();
const path = require('path');
const backupController = require('../controllers/backupController');
const { authenticateToken, isAdmin } = require('../middleware/auth');

// Importar multer de forma opcional
let multer;
let upload;

try {
  multer = require('multer');
  console.log('✅ Multer cargado correctamente');

  // Configurar multer para carga de archivos
  const storage = multer.diskStorage({
    destination: function (req, file, cb) {
      cb(null, '/tmp'); // Directorio temporal
    },
    filename: function (req, file, cb) {
      // Generar nombre único temporal
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      cb(null, 'backup-upload-' + uniqueSuffix + path.extname(file.originalname));
    }
  });

  upload = multer({
    storage: storage,
    limits: {
      fileSize: 100 * 1024 * 1024 // 100MB máximo
    },
    fileFilter: function (req, file, cb) {
      // Solo permitir archivos .sql
      if (file.originalname.toLowerCase().endsWith('.sql')) {
        cb(null, true);
      } else {
        cb(new Error('Solo se permiten archivos .sql'), false);
      }
    }
  });
} catch (error) {
  console.warn('⚠️ Multer no disponible. Funcionalidad de upload deshabilitada:', error.message);
  multer = null;
  upload = null;
}

// Ruta de diagnóstico sin autenticación (temporal)
router.get('/diagnose-public', async (req, res) => {
  try {
    console.log('Iniciando diagnóstico público de backup...');
    const backupService = require('../services/backupService');
    const result = await backupService.diagnoseBackupIssues();
    console.log('Diagnóstico público completado');
    res.json(result);
  } catch (error) {
    console.error('Error en diagnóstico público de backup:', error);
    res.status(500).json({ error: error.message });
  }
});

// Rutas protegidas
router.use(authenticateToken);
router.use(isAdmin);

router.get('/diagnose', backupController.diagnoseBackup);
router.post('/create', backupController.createBackup);
router.post('/restore/:filename', backupController.restoreBackup);
router.get('/list', backupController.listBackups);
router.get('/download/:filename', backupController.downloadBackup);

// Solo habilitar upload si multer está disponible
if (upload) {
  router.post('/upload', upload.single('backup'), backupController.uploadBackup);
} else {
  router.post('/upload', (req, res) => {
    res.status(503).json({
      error: 'Funcionalidad de upload no disponible. Multer no está instalado.'
    });
  });
}

module.exports = router;

