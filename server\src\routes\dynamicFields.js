const express = require('express');
const router = express.Router();
const dynamicFieldController = require('../controllers/dynamicFieldController');
const { authenticateToken } = require('../middleware/auth');

// Aplicar autenticación a todas las rutas
router.use(authenticateToken);

// Obtener campos dinámicos activos (para formularios)
router.get('/', dynamicFieldController.getDynamicFields);

// Obtener todos los campos dinámicos (para administración)
router.get('/all', dynamicFieldController.getAllDynamicFields);

// Crear nuevo campo dinámico
router.post('/', dynamicFieldController.createDynamicField);

// Actualizar campo dinámico
router.put('/:id', dynamicFieldController.updateDynamicField);

// Eliminar campo dinámico
router.delete('/:id', dynamicFieldController.deleteDynamicField);

// Obtener valores de campos dinámicos para una entidad
router.get('/values/:entityType/:entityId', dynamicFieldController.getDynamicFieldValues);

// Guardar valores de campos dinámicos
router.post('/values', dynamicFieldController.saveDynamicFieldValues);

// Obtener sugerencias para un campo específico
router.get('/suggestions/:fieldName', dynamicFieldController.getFieldSuggestions);

module.exports = router;
