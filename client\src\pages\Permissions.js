import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  MenuItem,
  Snackbar,
  Alert,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  LinearProgress,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Chip,
  Tooltip,
  IconButton,
  Stack,
  Divider,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  AccessTime as TimeIcon,
  CalendarToday as CalendarIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  HourglassEmpty as HourglassEmptyIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { es } from 'date-fns/locale';
import { format, addDays, differenceInDays, isWeekend } from 'date-fns';
import axios from 'axios';
import { API_URL } from '../config';
import { useDynamicFields } from '../hooks/useDynamicFields';
import PermissionDetailsModal from '../components/PermissionDetailsModal';
import PermissionFilters from '../components/PermissionFilters';
import PermissionValidationAlert from '../components/PermissionValidationAlert';

// Los tipos de permisos ahora se cargan dinámicamente desde Settings
// const permissionTypes = []; // Removido - ahora se carga desde API

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const Permissions = () => {
  const [tabValue, setTabValue] = useState(0);
  const [formData, setFormData] = useState({
    type: '',
    startTime: new Date(),
    endTime: new Date(),
    reason: '',
  });

  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  const [validationResult, setValidationResult] = useState(null);

  // Estado para almacenar los permisos reales del usuario
  const [permissions, setPermissions] = useState([]);

  // Estado para validaciones en tiempo real
  const [isFormValid, setIsFormValid] = useState(false);

  // Estado para horas extra disponibles
  const [overtimeHours, setOvertimeHours] = useState({
    available: 0,
    used: 0,
    total: 0
  });

  // Estado para el modo de selección de tiempo
  const [timeMode, setTimeMode] = useState('hours'); // 'hours' o 'days'

  // Estado para fechas cuando se selecciona por días
  const [daySelection, setDaySelection] = useState({
    startDate: new Date(),
    endDate: new Date(),
  });

  // Estado para configuración del horario laboral
  const [workSchedule, setWorkSchedule] = useState({
    startTime: '09:00',
    endTime: '18:00',
    lunchBreak: '13:00',
    lunchDuration: 60,
  });

  // Estado para el modal de detalles
  const [detailsModal, setDetailsModal] = useState({
    open: false,
    permission: null,
  });

  // Estado para filtros
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    employeeId: '',
    startDate: null,
    endDate: null,
    permissionStartDate: null,
    permissionEndDate: null,
    searchText: '',
  });

  // Estado para tipos de permisos dinámicos
  const [permissionTypes, setPermissionTypes] = useState([]);

  // Cargar permisos del usuario al montar el componente
  useEffect(() => {
    fetchUserPermissions();
    fetchOvertimeHours();
    fetchWorkSchedule();
    fetchPermissionTypes();
  }, []);

  // Manejar cambios en los filtros
  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    fetchUserPermissions(newFilters);
  };

  const fetchUserPermissions = async (appliedFilters = {}) => {
    try {
      console.log('🔍 OBTENIENDO PERMISOS DEL USUARIO...');
      console.log('🔍 FILTROS APLICADOS:', appliedFilters);

      // Construir parámetros de consulta
      const queryParams = new URLSearchParams();
      queryParams.append('_t', new Date().getTime());

      // Agregar filtros si existen
      Object.entries(appliedFilters).forEach(([key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
          if (value instanceof Date) {
            queryParams.append(key, value.toISOString());
          } else {
            queryParams.append(key, value);
          }
        }
      });

      const response = await axios.get(`${API_URL}/api/permissions?${queryParams.toString()}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
      });
      console.log('✅ PERMISOS OBTENIDOS:', response.data);
      setPermissions(response.data);
    } catch (error) {
      console.error('❌ ERROR AL OBTENER PERMISOS:', error);
      console.error('Response data:', error.response?.data);
      console.error('Response status:', error.response?.status);
      console.error('Request URL:', error.config?.url);

      setSnackbar({
        open: true,
        message: `Error al cargar los permisos: ${error.response?.data?.error || error.message}`,
        severity: 'error',
      });
    }
  };

  const fetchOvertimeHours = async () => {
    try {
      const response = await axios.get(`${API_URL}/api/permissions/overtime-hours`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      console.log('Horas extra obtenidas:', response.data);
      setOvertimeHours(response.data);
    } catch (error) {
      console.error('Error al obtener horas extra:', error);
      // No mostrar error si el endpoint no existe aún
    }
  };

  const fetchWorkSchedule = async () => {
    try {
      console.log('🔍 OBTENIENDO HORARIO LABORAL...');
      const response = await axios.get(`${API_URL}/api/settings/work-schedules`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      console.log('✅ HORARIO LABORAL OBTENIDO:', response.data);
      if (response.data && response.data.length > 0) {
        const schedule = response.data[0];
        setWorkSchedule({
          startTime: schedule.startTime || '09:00',
          endTime: schedule.endTime || '18:00',
          lunchBreak: schedule.lunchBreak || '13:00',
          lunchDuration: schedule.lunchDuration || 60,
        });
      }
    } catch (error) {
      console.error('❌ ERROR AL OBTENER HORARIO LABORAL:', error);
      // Mantener valores por defecto
    }
  };

  const fetchPermissionTypes = async () => {
    try {
      console.log('🔍 OBTENIENDO TIPOS DE PERMISOS...');
      const response = await axios.get(`${API_URL}/api/settings/permission-types`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      console.log('✅ TIPOS DE PERMISOS OBTENIDOS:', response.data);

      // Convertir los tipos de la base de datos al formato esperado por el frontend
      const formattedTypes = response.data
        .filter(type => type.active) // Solo tipos activos
        .map(type => ({
          value: type.name, // Usar el nombre directamente como valor
          label: type.label,
          maxDaysPerYear: type.maxDaysPerYear,
          requiresApproval: type.requiresApproval
        }));

      setPermissionTypes(formattedTypes);
    } catch (error) {
      console.error('❌ ERROR AL OBTENER TIPOS DE PERMISOS:', error);
      // Usar tipos por defecto en caso de error
      setPermissionTypes([
        { value: 'Vacaciones', label: 'Vacaciones', maxDaysPerYear: 30, requiresApproval: true },
        { value: 'Enfermedad', label: 'Descanso Médico', maxDaysPerYear: 15, requiresApproval: false },
        { value: 'Personal', label: 'Permiso Personal', maxDaysPerYear: 10, requiresApproval: true },
        { value: 'Compensacion', label: '⏰ Compensación de Horas Extra', maxDaysPerYear: 0, requiresApproval: true },
        { value: 'Otros', label: 'Otro', maxDaysPerYear: 5, requiresApproval: true },
      ]);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleChange = (event) => {
    const { name, value } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleDateChange = (name) => (date) => {
    setFormData((prev) => ({
      ...prev,
      [name]: date,
    }));
  };

  // Función para manejar cambios en la validación
  const handleValidationChange = (result) => {
    setValidationResult(result);
    setIsFormValid(result?.isValid || false);
  };

  const handleDaySelectionChange = (field) => (newValue) => {
    setDaySelection((prev) => ({
      ...prev,
      [field]: newValue,
    }));
  };

  const handleTimeModeChange = (event) => {
    const newMode = event.target.value;
    setTimeMode(newMode);

    if (newMode === 'days') {
      // Convertir las fechas actuales a días completos usando el horario laboral
      const startDate = new Date(formData.startTime);
      const endDate = new Date(formData.endTime);

      setDaySelection({
        startDate: startDate,
        endDate: endDate,
      });

      // Actualizar formData con horarios completos
      updateFormDataFromDays(startDate, endDate);
    }
  };

  const updateFormDataFromDays = (startDate, endDate) => {
    // Crear fechas con horario laboral
    const startDateTime = new Date(startDate);
    const [startHour, startMinute] = workSchedule.startTime.split(':');
    startDateTime.setHours(parseInt(startHour), parseInt(startMinute), 0, 0);

    const endDateTime = new Date(endDate);
    const [endHour, endMinute] = workSchedule.endTime.split(':');
    endDateTime.setHours(parseInt(endHour), parseInt(endMinute), 0, 0);

    setFormData((prev) => ({
      ...prev,
      startTime: startDateTime,
      endTime: endDateTime,
    }));
  };

  // Actualizar formData cuando cambian las fechas en modo días
  useEffect(() => {
    if (timeMode === 'days') {
      updateFormDataFromDays(daySelection.startDate, daySelection.endDate);
    }
  }, [daySelection, timeMode, workSchedule]);

  const calculateHoursRequested = () => {
    if (timeMode === 'days') {
      return calculateWorkingDays() * 8; // 8 horas por día (9 horas - 1 hora de almuerzo)
    } else {
      const start = new Date(formData.startTime);
      const end = new Date(formData.endTime);
      const diffInMs = end - start;
      const diffInHours = diffInMs / (1000 * 60 * 60);
      return Math.max(0, diffInHours);
    }
  };

  const calculateWorkingDays = () => {
    const start = new Date(daySelection.startDate);
    const end = new Date(daySelection.endDate);
    let workingDays = 0;

    // Iterar desde la fecha de inicio hasta la fecha de fin
    const currentDate = new Date(start);
    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay(); // 0 = Domingo, 1 = Lunes, ..., 6 = Sábado
      // Contar solo días laborales (Lunes a Viernes)
      if (dayOfWeek >= 1 && dayOfWeek <= 5) {
        workingDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return workingDays;
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    try {
      // Primero validar el permiso
      const validationData = {
        type: formData.type,
        startTime: formData.startTime?.toISOString(),
        endTime: formData.endTime?.toISOString(),
        reason: formData.reason,
        overtimeHoursUsed: formData.overtimeHoursUsed || 0,
        overtimeHoursAvailable: overtimeHours.available || 0
      };

      console.log('🔍 VALIDACIÓN TEMPORALMENTE DESHABILITADA PARA PRUEBAS');

      // VALIDACIÓN TEMPORALMENTE COMENTADA PARA PRUEBAS
      // const validationResponse = await axios.post(`${API_URL}/api/permissions/validate`, validationData, {
      //   headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      // });
      // const validation = validationResponse.data;
      // setValidationResult(validation);
      // if (!validation.isValid) {
      //   console.log('❌ Validación falló:', validation);
      //   setSnackbar({
      //     open: true,
      //     message: 'Por favor corrige los errores de validación antes de enviar',
      //     severity: 'error',
      //   });
      //   return;
      // }

      console.log('✅ Procediendo a crear permiso SIN validación');
    } catch (error) {
      console.error('Error en validación (DESHABILITADA):', error);
      // MANEJO DE ERRORES DE VALIDACIÓN TEMPORALMENTE COMENTADO
      // setValidationResult({
      //   isValid: false,
      //   errors: ['Error al validar el permiso'],
      //   warnings: [],
      //   details: {}
      // });
      // setSnackbar({
      //   open: true,
      //   message: 'Error al validar el permiso',
      //   severity: 'error',
      // });
      // return;
    }

    // La validación se realiza arriba con el endpoint de validación
    // Si llegamos aquí, significa que la validación pasó

    // Validación especial para compensación de horas extra
    if (formData.type === 'Compensacion') {
      const hoursRequested = calculateHoursRequested();
      if (hoursRequested > overtimeHours.available) {
        setSnackbar({
          open: true,
          message: `No tienes suficientes horas extra. Disponibles: ${overtimeHours.available}h, Solicitadas: ${hoursRequested.toFixed(1)}h`,
          severity: 'error',
        });
        return;
      }
    }

    try {
      // Preparar datos para enviar
      const submitData = {
        ...formData,
        startTime: formData.startTime.toISOString(),
        endTime: formData.endTime.toISOString(),
      };

      // Agregar información de horas extra si es compensación
      if (formData.type === 'OVERTIME_COMPENSATION') {
        submitData.overtimeHoursUsed = calculateHoursRequested();
        submitData.overtimeHoursAvailable = overtimeHours.available;
      }

      // Enviar solicitud de permiso a la API
      const response = await axios.post(
        `${API_URL}/api/permissions`,
        submitData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );

      console.log('Permiso creado:', response.data);

      setSnackbar({
        open: true,
        message: 'Solicitud de permiso enviada correctamente',
        severity: 'success',
      });

      // Limpiar formulario
      setFormData({
        type: '',
        startTime: new Date(),
        endTime: new Date(),
        reason: '',
      });

      // Actualizar la lista de permisos y horas extra
      fetchUserPermissions();
      fetchOvertimeHours();

      // Cambiar a la pestaña de "Mis Solicitudes"
      setTabValue(1);
    } catch (error) {
      console.error('Error al enviar la solicitud de permiso:', error);
      setSnackbar({
        open: true,
        message: 'Error al enviar la solicitud de permiso',
        severity: 'error',
      });
    }
  };



  const getStatusLabel = (status) => {
    switch (status) {
      case 'APPROVED':
        return 'Aprobado';
      case 'REJECTED':
        return 'Rechazado';
      default:
        return 'Pendiente';
    }
  };

  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: es });
    } catch (error) {
      return 'Fecha inválida';
    }
  };

  const getPermissionTypeLabel = (typeValue) => {
    const type = permissionTypes.find(t => t.value === typeValue);
    return type ? type.label : typeValue;
  };

  const formatDateOnly = (dateString) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });
    } catch (error) {
      return 'Fecha inválida';
    }
  };

  const formatTimeOnly = (dateString) => {
    try {
      return format(new Date(dateString), 'HH:mm', { locale: es });
    } catch (error) {
      return 'Hora inválida';
    }
  };

  const calculatePermissionHours = (permission) => {
    const start = new Date(permission.startTime);
    const end = new Date(permission.endTime);

    // Si es un permiso de día completo, calcular usando la configuración del horario laboral
    if (isFullDayPermission(permission)) {
      // Calcular horas de trabajo por día (descontando almuerzo)
      const [startHour, startMinute] = workSchedule.startTime.split(':').map(Number);
      const [endHour, endMinute] = workSchedule.endTime.split(':').map(Number);

      const startMinutes = startHour * 60 + startMinute;
      const endMinutes = endHour * 60 + endMinute;
      const totalMinutes = endMinutes - startMinutes;
      const totalHours = totalMinutes / 60;

      // Descontar tiempo de almuerzo
      const lunchHours = workSchedule.lunchDuration / 60;
      const workingHoursPerDay = totalHours - lunchHours;

      // Calcular días laborales
      const workingDays = calculatePermissionWorkingDays(permission);

      return Math.max(0, workingHoursPerDay * workingDays);
    } else {
      // Para permisos por horas específicas, calcular la diferencia directa
      const diffInMs = end - start;
      const diffInHours = diffInMs / (1000 * 60 * 60);
      return Math.max(0, diffInHours);
    }
  };

  const calculatePermissionWorkingDays = (permission) => {
    const start = new Date(permission.startTime);
    const end = new Date(permission.endTime);
    let workingDays = 0;

    const currentDate = new Date(start);
    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay();
      if (dayOfWeek >= 1 && dayOfWeek <= 5) {
        workingDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return workingDays;
  };

  const isFullDayPermission = (permission) => {
    const startTime = formatTimeOnly(permission.startTime);
    const endTime = formatTimeOnly(permission.endTime);

    // Usar la configuración del horario laboral para determinar si es día completo
    return (startTime === workSchedule.startTime && endTime === workSchedule.endTime);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircleIcon color="success" />;
      case 'REJECTED':
        return <CancelIcon color="error" />;
      case 'PENDING':
        return <HourglassEmptyIcon color="warning" />;
      default:
        return <InfoIcon color="info" />;
    }
  };

  const getPermissionStats = () => {
    const pending = permissions.filter(p => p.status === 'PENDING').length;
    const approved = permissions.filter(p => p.status === 'APPROVED').length;
    const rejected = permissions.filter(p => p.status === 'REJECTED').length;
    const totalHours = permissions.reduce((sum, p) => sum + calculatePermissionHours(p), 0);

    return { pending, approved, rejected, total: permissions.length, totalHours };
  };

  // Funciones para manejar el modal de detalles
  const handleOpenDetails = (permission) => {
    setDetailsModal({
      open: true,
      permission: permission,
    });
  };

  const handleCloseDetails = () => {
    setDetailsModal({
      open: false,
      permission: null,
    });
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={es}>
      <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Gestión de Permisos
        </Typography>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Solicitar Permiso" />
            <Tab label="Mis Solicitudes" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Paper sx={{ p: 3 }}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    required
                    fullWidth
                    select
                    label="Tipo de Permiso"
                    name="type"
                    value={formData.type}
                    onChange={handleChange}
                  >
                    {permissionTypes.map((type) => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>

                {/* Componente de validación - solo se muestra después de intentar enviar */}
                {validationResult && (
                  <Grid item xs={12}>
                    <PermissionValidationAlert
                      validationResult={validationResult}
                      autoValidate={false}
                    />
                  </Grid>
                )}

                {/* Mostrar información de horas extra si se selecciona compensación */}
                {formData.type === 'Compensacion' && (
                  <Grid item xs={12}>
                    <Card sx={{ bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom color="primary">
                          ⏰ Información de Horas Extra
                        </Typography>

                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={4}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" color="success.main">
                                {overtimeHours.available.toFixed(1)}h
                              </Typography>
                              <Typography variant="body2" color="textSecondary">
                                Disponibles
                              </Typography>
                            </Box>
                          </Grid>

                          <Grid item xs={12} sm={4}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" color="warning.main">
                                {overtimeHours.used.toFixed(1)}h
                              </Typography>
                              <Typography variant="body2" color="textSecondary">
                                Utilizadas
                              </Typography>
                            </Box>
                          </Grid>

                          <Grid item xs={12} sm={4}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" color="info.main">
                                {overtimeHours.total.toFixed(1)}h
                              </Typography>
                              <Typography variant="body2" color="textSecondary">
                                Total Acumuladas
                              </Typography>
                            </Box>
                          </Grid>

                          <Grid item xs={12}>
                            <Box sx={{ mt: 2 }}>
                              <Typography variant="body2" gutterBottom>
                                Progreso de utilización:
                              </Typography>
                              <LinearProgress
                                variant="determinate"
                                value={overtimeHours.total > 0 ? (overtimeHours.used / overtimeHours.total) * 100 : 0}
                                sx={{ height: 8, borderRadius: 4 }}
                              />
                              <Typography variant="caption" color="textSecondary">
                                {overtimeHours.total > 0 ?
                                  `${((overtimeHours.used / overtimeHours.total) * 100).toFixed(1)}% utilizado` :
                                  'Sin horas extra acumuladas'
                                }
                              </Typography>
                            </Box>
                          </Grid>

                          {formData.startTime && formData.endTime && (
                            <Grid item xs={12}>
                              <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                                <Typography variant="body2" gutterBottom>
                                  <strong>Horas solicitadas:</strong> {calculateHoursRequested().toFixed(1)}h
                                </Typography>
                                {calculateHoursRequested() > overtimeHours.available && (
                                  <Typography
                                    variant="caption"
                                    sx={{
                                      mt: 1,
                                      display: 'block',
                                      color: 'error.main',
                                      fontWeight: 'bold'
                                    }}
                                  >
                                    ⚠️ Excede horas disponibles
                                  </Typography>
                                )}
                                {calculateHoursRequested() <= overtimeHours.available && calculateHoursRequested() > 0 && (
                                  <Typography
                                    variant="caption"
                                    sx={{
                                      mt: 1,
                                      display: 'block',
                                      color: 'success.main',
                                      fontWeight: 'bold'
                                    }}
                                  >
                                    ✅ Horas suficientes
                                  </Typography>
                                )}
                              </Box>
                            </Grid>
                          )}
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                )}

                {/* Selector de modo de tiempo */}
                <Grid item xs={12}>
                  <FormControl component="fieldset">
                    <FormLabel component="legend">
                      <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                        📅 Tipo de Solicitud
                      </Typography>
                    </FormLabel>
                    <RadioGroup
                      row
                      value={timeMode}
                      onChange={handleTimeModeChange}
                      sx={{ mt: 1 }}
                    >
                      <FormControlLabel
                        value="hours"
                        control={<Radio />}
                        label={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography>🕐 Por Horas Específicas</Typography>
                            <Chip size="small" label="Personalizado" color="primary" variant="outlined" />
                          </Box>
                        }
                      />
                      <FormControlLabel
                        value="days"
                        control={<Radio />}
                        label={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography>📆 Por Días Completos</Typography>
                            <Chip size="small" label={`${workSchedule.startTime}-${workSchedule.endTime} (8h)`} color="secondary" variant="outlined" />
                          </Box>
                        }
                      />
                    </RadioGroup>
                  </FormControl>
                </Grid>

                {/* Campos de fecha y hora según el modo seleccionado */}
                {timeMode === 'hours' ? (
                  <>
                    <Grid item xs={12} sm={6}>
                      <DateTimePicker
                        label="Fecha y hora de inicio"
                        value={formData.startTime}
                        onChange={handleDateChange('startTime')}
                        renderInput={(params) => <TextField {...params} fullWidth />}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <DateTimePicker
                        label="Fecha y hora de fin"
                        value={formData.endTime}
                        onChange={handleDateChange('endTime')}
                        renderInput={(params) => <TextField {...params} fullWidth />}
                      />
                    </Grid>
                  </>
                ) : (
                  <>
                    <Grid item xs={12} sm={6}>
                      <DatePicker
                        label="Fecha de inicio"
                        value={daySelection.startDate}
                        onChange={handleDaySelectionChange('startDate')}
                        renderInput={(params) => <TextField {...params} fullWidth />}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <DatePicker
                        label="Fecha de fin"
                        value={daySelection.endDate}
                        onChange={handleDaySelectionChange('endDate')}
                        renderInput={(params) => <TextField {...params} fullWidth />}
                      />
                    </Grid>

                    {/* Información de días laborales */}
                    <Grid item xs={12}>
                      <Card sx={{ bgcolor: 'info.50', border: '1px solid', borderColor: 'info.200' }}>
                        <CardContent sx={{ py: 2 }}>
                          <Typography variant="body2" gutterBottom>
                            <strong>📊 Resumen del Permiso:</strong>
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
                            <Typography variant="body2">
                              📅 <strong>Días laborales:</strong> {calculateWorkingDays()} días
                            </Typography>
                            <Typography variant="body2">
                              ⏰ <strong>Horas totales:</strong> {calculateHoursRequested().toFixed(1)}h
                            </Typography>
                            <Typography variant="body2">
                              🕐 <strong>Horario:</strong> {workSchedule.startTime} - {workSchedule.endTime}
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  </>
                )}

                <Grid item xs={12}>
                  <TextField
                    required
                    fullWidth
                    label="Motivo"
                    name="reason"
                    multiline
                    rows={4}
                    value={formData.reason}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      size="large"
                      disabled={!formData.type || !formData.reason.trim() || !formData.startTime || !formData.endTime}
                    >
                      Enviar Solicitud
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Paper>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {/* Resumen estadístico */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ bgcolor: 'warning.50', border: '1px solid', borderColor: 'warning.200' }}>
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Typography variant="h4" color="warning.main" fontWeight="bold">
                    {getPermissionStats().pending}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    ⏳ Pendientes
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ bgcolor: 'success.50', border: '1px solid', borderColor: 'success.200' }}>
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Typography variant="h4" color="success.main" fontWeight="bold">
                    {getPermissionStats().approved}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    ✅ Aprobados
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ bgcolor: 'error.50', border: '1px solid', borderColor: 'error.200' }}>
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Typography variant="h4" color="error.main" fontWeight="bold">
                    {getPermissionStats().rejected}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    ❌ Rechazados
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ bgcolor: 'info.50', border: '1px solid', borderColor: 'info.200' }}>
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Typography variant="h4" color="info.main" fontWeight="bold">
                    {getPermissionStats().totalHours.toFixed(1)}h
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    ⏰ Total Horas
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Filtros de permisos */}
          <PermissionFilters
            onFiltersChange={handleFiltersChange}
            currentFilters={filters}
            showEmployeeFilter={false}
            employees={[]}
            permissionTypes={permissionTypes.map(pt => pt.label)}
          />

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Tipo</TableCell>
                  <TableCell><CalendarIcon sx={{ mr: 1, verticalAlign: 'middle' }} />Fechas</TableCell>
                  <TableCell><TimeIcon sx={{ mr: 1, verticalAlign: 'middle' }} />Duración</TableCell>
                  <TableCell>Estado</TableCell>
                  <TableCell>Motivo</TableCell>
                  <TableCell>Acciones</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {permissions.length > 0 ? (
                  permissions.map((permission) => (
                    <TableRow key={permission.id} hover>
                      <TableCell>
                        <Chip
                          label={getPermissionTypeLabel(permission.type)}
                          color={permission.type === 'Compensacion' ? 'secondary' : 'primary'}
                          variant="outlined"
                          size="small"
                        />
                      </TableCell>

                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            <strong>Inicio:</strong> {formatDateOnly(permission.startTime)}
                          </Typography>
                          <Typography variant="body2">
                            <strong>Fin:</strong> {formatDateOnly(permission.endTime)}
                          </Typography>
                          {!isFullDayPermission(permission) && (
                            <Typography variant="caption" color="textSecondary">
                              {formatTimeOnly(permission.startTime)} - {formatTimeOnly(permission.endTime)}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>

                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="bold" color="primary.main">
                            {calculatePermissionHours(permission).toFixed(1)}h
                          </Typography>
                          {isFullDayPermission(permission) ? (
                            <Chip
                              label={`${calculatePermissionWorkingDays(permission)} día${calculatePermissionWorkingDays(permission) !== 1 ? 's' : ''}`}
                              size="small"
                              color="info"
                              variant="outlined"
                            />
                          ) : (
                            <Typography variant="caption" color="textSecondary">
                              Horas específicas
                            </Typography>
                          )}
                          {permission.type === 'OVERTIME_COMPENSATION' && permission.overtimeHoursUsed && (
                            <Typography variant="caption" display="block" color="secondary.main">
                              ⏰ {permission.overtimeHoursUsed.toFixed(1)}h extra
                            </Typography>
                          )}
                        </Box>
                      </TableCell>

                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getStatusIcon(permission.status)}
                          <Box>
                            <Chip
                              label={permission.status === 'PENDING' ? 'Pendiente' :
                                     permission.status === 'APPROVED' ? 'Aprobado' : 'Rechazado'}
                              color={permission.status === 'APPROVED' ? 'success' :
                                     permission.status === 'REJECTED' ? 'error' : 'warning'}
                              size="small"
                            />
                            {permission.approvedAt && (
                              <Typography variant="caption" display="block" color="textSecondary">
                                {formatDateOnly(permission.approvedAt)}
                              </Typography>
                            )}
                            {permission.createdAt && (
                              <Typography variant="caption" display="block" color="textSecondary">
                                Solicitado: {formatDateOnly(permission.createdAt)}
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </TableCell>

                      <TableCell>
                        <Typography variant="body2" sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {permission.reason}
                        </Typography>
                      </TableCell>

                      <TableCell>
                        <Stack direction="row" spacing={1}>
                          <Tooltip title="Ver detalles">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleOpenDetails(permission)}
                            >
                              <VisibilityIcon />
                            </IconButton>
                          </Tooltip>
                          {permission.status === 'PENDING' && (
                            <>
                              <Tooltip title="Editar solicitud">
                                <IconButton size="small" color="info">
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Cancelar solicitud">
                                <IconButton size="small" color="error">
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </>
                          )}
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Box sx={{ py: 4 }}>
                        <Typography variant="h6" color="textSecondary" gutterBottom>
                          📋 No tienes solicitudes de permisos
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Utiliza la pestaña "Solicitar Permiso" para crear tu primera solicitud
                        </Typography>
                      </Box>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>

        {/* Modal de detalles del permiso */}
        <PermissionDetailsModal
          open={detailsModal.open}
          onClose={handleCloseDetails}
          permission={detailsModal.permission}
          showEmployeeInfo={false}
        />
      </Container>
    </LocalizationProvider>
  );
};

export default Permissions;
