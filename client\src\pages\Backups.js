import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Backup as BackupIcon,
  Restore as RestoreIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { API_URL } from '../config';

const Backups = () => {
  const [backups, setBackups] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    backup: null,
  });
  const [uploadDialog, setUploadDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);

  const fetchBackups = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get(`${API_URL}/api/backups/list`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      setBackups(response.data);
    } catch (err) {
      console.error('Error al obtener backups:', err);
      setError('Error al obtener la lista de backups');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBackups();
  }, []);

  const handleCreateBackup = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔧 INICIANDO CREACIÓN DE BACKUP...');

      const response = await axios.post(
        `${API_URL}/api/backups/create`,
        {},
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
          timeout: 60000 // 60 segundos de timeout
        }
      );

      console.log('✅ BACKUP CREADO EXITOSAMENTE:', response.data);
      setError('Backup creado exitosamente');
      await fetchBackups();
    } catch (err) {
      console.error('❌ ERROR AL CREAR BACKUP:', err);
      console.error('Response data:', err.response?.data);
      console.error('Response status:', err.response?.status);

      let errorMessage = 'Error al crear el backup';
      if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(`Error al crear backup: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleRestoreBackup = async (backup) => {
    try {
      setLoading(true);
      setError(null);
      await axios.post(
        `${API_URL}/api/backups/restore/${backup.filename}`,
        {},
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );
      setConfirmDialog({ open: false, backup: null });
      setError('Backup restaurado exitosamente');
    } catch (err) {
      console.error('Error al restaurar backup:', err);
      setError('Error al restaurar el backup');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadBackup = async (backup) => {
    try {
      console.log('🔽 INICIANDO DESCARGA DE BACKUP:', backup.filename);

      const response = await axios.get(
        `${API_URL}/api/backups/download/${backup.filename}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
          responseType: 'blob', // Importante para archivos
          timeout: 60000 // 60 segundos
        }
      );

      // Crear URL para descarga
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', backup.filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      console.log('✅ DESCARGA COMPLETADA:', backup.filename);
      setError('Descarga completada exitosamente');
    } catch (err) {
      console.error('❌ ERROR AL DESCARGAR BACKUP:', err);
      setError(`Error al descargar backup: ${err.response?.data?.error || err.message}`);
    }
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      console.log('📁 ARCHIVO SELECCIONADO:', {
        name: file.name,
        size: file.size,
        type: file.type
      });
      setSelectedFile(file);
    }
  };

  const handleUploadBackup = async () => {
    if (!selectedFile) {
      setError('Por favor selecciona un archivo');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log('🔼 INICIANDO CARGA DE BACKUP:', selectedFile.name);

      const formData = new FormData();
      formData.append('backup', selectedFile);

      const response = await axios.post(
        `${API_URL}/api/backups/upload`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'multipart/form-data',
          },
          timeout: 120000 // 2 minutos para archivos grandes
        }
      );

      console.log('✅ BACKUP CARGADO EXITOSAMENTE:', response.data);
      setError('Backup cargado exitosamente');
      setSelectedFile(null);
      setUploadDialog(false);

      // Actualizar lista de backups
      await fetchBackups();
    } catch (err) {
      console.error('❌ ERROR AL CARGAR BACKUP:', err);
      setError(`Error al cargar backup: ${err.response?.data?.error || err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
          <Typography variant="h4" component="h1">
            Gestión de Backups
          </Typography>
          <div>
            <Button
              variant="contained"
              startIcon={<BackupIcon />}
              onClick={handleCreateBackup}
              disabled={loading}
              sx={{ mr: 2 }}
            >
              Crear Backup
            </Button>
            <Button
              variant="outlined"
              color="success"
              onClick={() => setUploadDialog(true)}
              disabled={loading}
              startIcon={<UploadIcon />}
              sx={{ mr: 2 }}
            >
              Cargar Backup
            </Button>

            <IconButton onClick={fetchBackups} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </div>
        </div>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', padding: 20 }}>
            <CircularProgress />
          </div>
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Nombre del Archivo</TableCell>
                  <TableCell>Fecha de Creación</TableCell>
                  <TableCell>Tamaño</TableCell>
                  <TableCell align="right">Acciones</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {backups.map((backup) => (
                  <TableRow key={backup.filename}>
                    <TableCell>{backup.filename}</TableCell>
                    <TableCell>
                      {(() => {
                        try {
                          const date = new Date(backup.created);
                          console.log('📅 PROCESANDO FECHA:', {
                            original: backup.created,
                            parsed: date,
                            timestamp: date.getTime(),
                            isValid: !isNaN(date.getTime())
                          });

                          // Verificar si la fecha es válida
                          if (isNaN(date.getTime()) || date.getTime() === 0) {
                            console.warn('⚠️ FECHA INVÁLIDA, usando fecha actual');
                            return 'Fecha no disponible';
                          }

                          return format(date, 'PPpp', { locale: es });
                        } catch (error) {
                          console.error('❌ ERROR AL FORMATEAR FECHA:', error);
                          return 'Error en fecha';
                        }
                      })()}
                    </TableCell>
                    <TableCell>
                      {backup.size ? `${(backup.size / 1024 / 1024).toFixed(2)} MB` : 'N/A'}
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        color="success"
                        onClick={() => handleDownloadBackup(backup)}
                        title="Descargar backup"
                        sx={{ mr: 1 }}
                      >
                        <DownloadIcon />
                      </IconButton>
                      <IconButton
                        color="primary"
                        onClick={() =>
                          setConfirmDialog({ open: true, backup })
                        }
                        title="Restaurar backup"
                      >
                        <RestoreIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      <Dialog
        open={confirmDialog.open}
        onClose={() => setConfirmDialog({ open: false, backup: null })}
      >
        <DialogTitle>Confirmar Restauración</DialogTitle>
        <DialogContent>
          <Typography>
            ¿Estás seguro de que deseas restaurar el backup{' '}
            {confirmDialog.backup?.filename}? Esta acción reemplazará todos los
            datos actuales.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setConfirmDialog({ open: false, backup: null })}
          >
            Cancelar
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => handleRestoreBackup(confirmDialog.backup)}
            autoFocus
          >
            Restaurar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo para cargar backup */}
      <Dialog
        open={uploadDialog}
        onClose={() => {
          setUploadDialog(false);
          setSelectedFile(null);
        }}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Cargar Backup</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Selecciona un archivo de backup (.sql) para cargar al sistema.
          </Typography>

          <input
            type="file"
            accept=".sql"
            onChange={handleFileSelect}
            style={{ marginBottom: '16px' }}
          />

          {selectedFile && (
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Archivo seleccionado:</strong> {selectedFile.name}
              </Typography>
              <Typography variant="body2">
                <strong>Tamaño:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
              </Typography>
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setUploadDialog(false);
              setSelectedFile(null);
            }}
          >
            Cancelar
          </Button>
          <Button
            variant="contained"
            color="success"
            onClick={handleUploadBackup}
            disabled={!selectedFile || loading}
            startIcon={<UploadIcon />}
          >
            Cargar Backup
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default Backups;




