const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const reportService = require('../services/reportService');
const { convertPermissionsToSpanish, convertStatsToSpanish, enumToSpanish } = require('../utils/permissionTypeMapper');

// Obtener reporte de tiempo
exports.getTimeReport = async (req, res) => {
  try {
    const { startDate, endDate, userId, format = 'json' } = req.query;

    console.log('TimeReport - Parámetros recibidos:', { startDate, endDate, userId, format });

    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Se requieren fechas de inicio y fin' });
    }

    // Validar que las fechas sean válidas
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({ error: 'Fechas inválidas' });
    }

    if (format === 'json') {
      // Obtener entradas de tiempo manuales y tareas Kanban archivadas
      const [timeEntries, archivedTasks] = await Promise.all([
        prisma.timeEntry.findMany({
          where: {
            startTime: {
              gte: start,
              lte: end,
            },
            ...(userId && { userId }),
            status: 'APPROVED'
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            task: {
              select: {
                id: true,
                title: true,
                status: true
              }
            }
          },
          orderBy: {
            startTime: 'desc'
          }
        }),
        prisma.task.findMany({
          where: {
            status: 'ARCHIVED',
            updatedAt: {
              gte: start,
              lte: end
            },
            ...(userId && { assigneeId: userId })
          },
          select: {
            id: true,
            title: true,
            actualHours: true,
            timerAccumulatedSeconds: true,
            updatedAt: true,
            assigneeId: true,
            assignee: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          },
          orderBy: {
            updatedAt: 'desc'
          }
        })
      ]);

      console.log('TimeReport - Datos encontrados:', {
        totalManualEntries: timeEntries.length,
        totalKanbanTasks: archivedTasks.length,
        sampleManualEntries: timeEntries.slice(0, 2).map(e => ({
          id: e.id,
          title: e.title,
          hoursWorked: e.hoursWorked,
          status: e.status,
          startTime: e.startTime
        })),
        sampleKanbanTasks: archivedTasks.slice(0, 2).map(t => ({
          id: t.id,
          title: t.title,
          actualHours: t.actualHours,
          updatedAt: t.updatedAt
        }))
      });

      // Calcular estadísticas de registros manuales
      const totalManualHours = timeEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);
      const totalManualEntries = timeEntries.length;

      // Calcular estadísticas de tareas Kanban
      const totalKanbanHours = archivedTasks.reduce((sum, task) => {
        let hours = 0;
        if (task.actualHours) {
          hours = parseFloat(task.actualHours) || 0;
        } else if (task.timerAccumulatedSeconds) {
          hours = parseFloat((task.timerAccumulatedSeconds / 3600).toFixed(2)) || 0;
        }
        return sum + hours;
      }, 0);
      const totalKanbanTasks = archivedTasks.length;

      // Totales combinados
      const totalHours = totalManualHours + totalKanbanHours;
      const totalEntries = totalManualEntries + totalKanbanTasks;

      // Agrupar por día - PASO 1: Registros manuales
      const dailyStats = timeEntries.reduce((acc, entry) => {
        const date = new Date(entry.startTime).toISOString().split('T')[0];
        if (!acc[date]) {
          acc[date] = {
            date,
            manualHours: 0,
            kanbanHours: 0,
            totalHours: 0,
            manualEntries: 0,
            kanbanTasks: 0,
            totalEntries: 0
          };
        }
        const hours = entry.hoursWorked || 0;
        acc[date].manualHours += hours;
        acc[date].totalHours += hours;
        acc[date].manualEntries += 1;
        acc[date].totalEntries += 1;
        return acc;
      }, {});

      // PASO 2: Agregar tareas Kanban por día
      archivedTasks.forEach(task => {
        const date = new Date(task.updatedAt).toISOString().split('T')[0];
        if (!dailyStats[date]) {
          dailyStats[date] = {
            date,
            manualHours: 0,
            kanbanHours: 0,
            totalHours: 0,
            manualEntries: 0,
            kanbanTasks: 0,
            totalEntries: 0
          };
        }

        let hours = 0;
        if (task.actualHours) {
          hours = parseFloat(task.actualHours) || 0;
        } else if (task.timerAccumulatedSeconds) {
          hours = parseFloat((task.timerAccumulatedSeconds / 3600).toFixed(2)) || 0;
        }

        dailyStats[date].kanbanHours += hours;
        dailyStats[date].totalHours += hours;
        dailyStats[date].kanbanTasks += 1;
        dailyStats[date].totalEntries += 1;
      });

      // Crear lista combinada de registros de tiempo
      const allTimeRecords = [
        // Registros manuales
        ...timeEntries.map(entry => ({
          id: entry.id,
          title: entry.title,
          description: entry.description,
          startTime: entry.startTime,
          endTime: entry.endTime,
          hoursWorked: Number((entry.hoursWorked || 0).toFixed(2)),
          status: entry.status,
          user: entry.user,
          task: entry.task,
          createdAt: entry.createdAt,
          type: 'manual',
          source: 'TimeEntry'
        })),
        // Tareas Kanban
        ...archivedTasks.map(task => {
          let hours = 0;
          if (task.actualHours) {
            hours = parseFloat(task.actualHours) || 0;
          } else if (task.timerAccumulatedSeconds) {
            hours = parseFloat((task.timerAccumulatedSeconds / 3600).toFixed(2)) || 0;
          }

          return {
            id: task.id,
            title: task.title,
            description: `Tarea Kanban completada`,
            startTime: task.updatedAt,
            endTime: task.updatedAt,
            hoursWorked: Number(hours.toFixed(2)),
            status: 'ARCHIVED',
            user: task.assignee,
            task: { id: task.id, title: task.title, status: 'ARCHIVED' },
            createdAt: task.updatedAt,
            type: 'kanban',
            source: 'Task'
          };
        })
      ].sort((a, b) => new Date(b.startTime) - new Date(a.startTime));

      res.json({
        timeEntries: allTimeRecords,
        summary: {
          totalEntries,
          totalHours: Number(totalHours.toFixed(2)),
          totalManualEntries,
          totalKanbanTasks,
          totalManualHours: Number(totalManualHours.toFixed(2)),
          totalKanbanHours: Number(totalKanbanHours.toFixed(2)),
          averageHoursPerEntry: totalEntries > 0 ? Number((totalHours / totalEntries).toFixed(2)) : 0,
          dateRange: {
            start: startDate,
            end: endDate
          }
        },
        dailyStats: Object.values(dailyStats).map(day => ({
          ...day,
          manualHours: Number(day.manualHours.toFixed(2)),
          kanbanHours: Number(day.kanbanHours.toFixed(2)),
          totalHours: Number(day.totalHours.toFixed(2))
        }))
      });
    } else {
      // Para PDF/Excel, usar los mismos datos procesados
      const allTimeRecords = [
        // Registros manuales
        ...timeEntries.map(entry => ({
          id: entry.id,
          title: entry.title,
          description: entry.description,
          startTime: entry.startTime,
          endTime: entry.endTime,
          hoursWorked: Number((entry.hoursWorked || 0).toFixed(2)),
          status: entry.status,
          user: entry.user,
          task: entry.task,
          createdAt: entry.createdAt,
          type: 'Manual',
          source: 'TimeEntry'
        })),
        // Tareas Kanban
        ...archivedTasks.map(task => {
          let hours = 0;
          if (task.actualHours) {
            hours = parseFloat(task.actualHours) || 0;
          } else if (task.timerAccumulatedSeconds) {
            hours = parseFloat((task.timerAccumulatedSeconds / 3600).toFixed(2)) || 0;
          }

          return {
            id: task.id,
            title: task.title,
            description: `Tarea Kanban completada`,
            startTime: task.updatedAt,
            endTime: task.updatedAt,
            hoursWorked: Number(hours.toFixed(2)),
            status: 'ARCHIVED',
            user: task.assignee,
            task: { id: task.id, title: task.title, status: 'ARCHIVED' },
            createdAt: task.updatedAt,
            type: 'Kanban',
            source: 'Task'
          };
        })
      ].sort((a, b) => new Date(a.startTime) - new Date(b.startTime));

      const buffer = await reportService.generateTimeReportFromData(allTimeRecords, startDate, endDate, format);
      res.setHeader('Content-Type', format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=reporte-tiempo.${format}`);
      res.send(buffer);
    }
  } catch (error) {
    console.error('Error al generar reporte de tiempo:', error);
    res.status(500).json({ error: 'Error al generar el reporte', details: error.message });
  }
};

// Obtener reporte Kanban
exports.getKanbanReport = async (req, res) => {
  try {
    const { format = 'pdf' } = req.query;

    const buffer = await reportService.generateKanbanReport(format);

    res.setHeader('Content-Type', format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=reporte-kanban.${format}`);
    res.send(buffer);
  } catch (error) {
    console.error('Error al generar reporte Kanban:', error);
    res.status(500).json({ error: 'Error al generar el reporte' });
  }
};

// Obtener reporte por usuario
exports.getUserReport = async (req, res) => {
  try {
    const { startDate, endDate, format = 'json' } = req.query;

    // Validar fechas requeridas
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Se requieren fechas de inicio y fin' });
    }

    if (format === 'json') {
      // Obtener entradas de tiempo aprobadas
      const timeEntries = await prisma.timeEntry.findMany({
        where: {
          startTime: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
          status: 'APPROVED'
        },
        include: {
          user: true,
          task: true,
        },
      });

      // Obtener tareas archivadas con horas trabajadas (sistema Kanban)
      const archivedTasks = await prisma.task.findMany({
        where: {
          status: 'ARCHIVED',
          updatedAt: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          }
        },
        select: {
          id: true,
          title: true,
          actualHours: true,
          timerAccumulatedSeconds: true,
          assigneeId: true,
          assignee: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      // Obtener configuración de horarios de trabajo para determinar horas extra
      const workSchedules = await prisma.workSchedule.findMany();
      const workHours = workSchedules.reduce((acc, schedule) => {
        acc[schedule.dayOfWeek] = {
          start: schedule.startTime,
          end: schedule.endTime,
          lunch: schedule.lunchDuration || 0
        };
        return acc;
      }, {});

      // Agrupar por usuario - PASO 1: Registros manuales (TimeEntry)
      const userStats = timeEntries.reduce((acc, entry) => {
        if (!acc[entry.userId]) {
          acc[entry.userId] = {
            name: entry.user?.name || 'Usuario desconocido',
            regularHours: 0,
            overtimeHours: 0,
            kanbanHours: 0,
            totalHours: 0,
            entriesCount: 0,
            kanbanTasksCount: 0
          };
        }

        const hours = entry.hoursWorked || 0;

        // Determinar si son horas extra basado en horarios de trabajo
        // Por simplicidad, consideramos horas extra las que están fuera del horario 9-18
        const entryDate = new Date(entry.startTime);
        const dayOfWeek = entryDate.getDay();
        const entryHour = entryDate.getHours();

        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
        const isOutsideWorkHours = entryHour < 9 || entryHour >= 18;

        if (isWeekend || isOutsideWorkHours) {
          acc[entry.userId].overtimeHours += hours;
        } else {
          acc[entry.userId].regularHours += hours;
        }

        acc[entry.userId].totalHours += hours;
        acc[entry.userId].entriesCount += 1;

        return acc;
      }, {});

      // PASO 2: Agregar horas de tareas Kanban archivadas
      archivedTasks.forEach(task => {
        if (!task.assigneeId || !task.assignee) return;

        const userId = task.assigneeId;

        if (!userStats[userId]) {
          userStats[userId] = {
            name: task.assignee.name,
            regularHours: 0,
            overtimeHours: 0,
            kanbanHours: 0,
            totalHours: 0,
            entriesCount: 0,
            kanbanTasksCount: 0
          };
        }

        // Calcular horas de la tarea Kanban
        let taskHours = 0;

        // Usar actualHours si está disponible
        if (task.actualHours) {
          taskHours = parseFloat(task.actualHours) || 0;
        }
        // Si no, usar timerAccumulatedSeconds convertido a horas
        else if (task.timerAccumulatedSeconds) {
          taskHours = parseFloat((task.timerAccumulatedSeconds / 3600).toFixed(2)) || 0;
        }

        userStats[userId].kanbanHours += taskHours;
        userStats[userId].totalHours += taskHours;
        userStats[userId].kanbanTasksCount += 1;
      });

      // Convertir a array y formatear números
      const users = Object.values(userStats).map(user => ({
        ...user,
        regularHours: Number(user.regularHours.toFixed(2)),
        overtimeHours: Number(user.overtimeHours.toFixed(2)),
        kanbanHours: Number(user.kanbanHours.toFixed(2)),
        totalHours: Number(user.totalHours.toFixed(2))
      }));

      res.json({
        users,
        summary: {
          totalUsers: users.length,
          totalRegularHours: Number(users.reduce((sum, u) => sum + u.regularHours, 0).toFixed(2)),
          totalOvertimeHours: Number(users.reduce((sum, u) => sum + u.overtimeHours, 0).toFixed(2)),
          totalKanbanHours: Number(users.reduce((sum, u) => sum + u.kanbanHours, 0).toFixed(2)),
          totalHours: Number(users.reduce((sum, u) => sum + u.totalHours, 0).toFixed(2)),
          totalManualEntries: Number(users.reduce((sum, u) => sum + u.entriesCount, 0)),
          totalKanbanTasks: Number(users.reduce((sum, u) => sum + u.kanbanTasksCount, 0))
        }
      });
    } else {
      const buffer = await reportService.generateTimeReport(startDate, endDate, null, format);
      res.setHeader('Content-Type', format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=reporte-usuarios.${format}`);
      res.send(buffer);
    }
  } catch (error) {
    console.error('Error al generar reporte de usuarios:', error);
    res.status(500).json({ error: 'Error al generar el reporte', details: error.message });
  }
};

// Obtener reporte por tarea
exports.getTaskReport = async (req, res) => {
  try {
    const { startDate, endDate, format = 'json' } = req.query;

    console.log('TaskReport - Parámetros recibidos:', { startDate, endDate, format });

    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Se requieren fechas de inicio y fin' });
    }

    // Validar que las fechas sean válidas
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({ error: 'Fechas inválidas' });
    }

    if (format === 'json') {
      // Obtener entradas de tiempo manuales y tareas archivadas (Kanban)
      const [timeEntries, archivedTasks] = await Promise.all([
        prisma.timeEntry.findMany({
          where: {
            startTime: {
              gte: start,
              lte: end
            },
            taskId: {
              not: null
            },
            status: 'APPROVED'
          },
          include: {
            task: true,
            user: true
          }
        }),
        prisma.task.findMany({
          where: {
            status: 'ARCHIVED',
            updatedAt: {
              gte: start,
              lte: end
            }
          },
          select: {
            id: true,
            title: true,
            actualHours: true,
            timerAccumulatedSeconds: true,
            assigneeId: true,
            assignee: {
              select: {
                id: true,
                name: true
              }
            }
          }
        })
      ]);

      console.log('TaskReport - Datos encontrados:', {
        timeEntriesCount: timeEntries.length,
        archivedTasksCount: archivedTasks.length,
        timeEntriesSample: timeEntries.slice(0, 2).map(e => ({ id: e.id, taskId: e.taskId, title: e.title })),
        archivedTasksSample: archivedTasks.slice(0, 2).map(t => ({ id: t.id, title: t.title, actualHours: t.actualHours }))
      });

      // Validar que las tareas existan
      if (!timeEntries.length && !archivedTasks.length) {
        console.log('TaskReport - No se encontraron datos, devolviendo array vacío');
        return res.json({
          tasks: [],
          summary: {
            totalTasks: 0,
            totalHours: 0,
            totalManualEntries: 0,
            totalKanbanTasks: 0,
            averageHoursPerTask: 0
          }
        });
      }

      // PASO 1: Agrupar registros manuales por tarea
      const taskStats = timeEntries.reduce((acc, entry) => {
        if (!entry.task) return acc;

        if (!acc[entry.taskId]) {
          acc[entry.taskId] = {
            title: entry.task.title,
            totalHours: 0,
            manualHours: 0,
            kanbanHours: 0,
            manualEntries: 0,
            isKanbanTask: false
          };
        }

        const hours = entry.hoursWorked || 0;
        acc[entry.taskId].manualHours += hours;
        acc[entry.taskId].totalHours += hours;
        acc[entry.taskId].manualEntries += 1;

        return acc;
      }, {});

      // PASO 2: Agregar horas de tareas Kanban archivadas
      archivedTasks.forEach(task => {
        try {
          if (!task.id) return;

          const taskId = task.id;
          if (!taskStats[taskId]) {
            taskStats[taskId] = {
              title: task.title,
              totalHours: 0,
              manualHours: 0,
              kanbanHours: 0,
              manualEntries: 0,
              isKanbanTask: true
            };
          }

          // Calcular horas de la tarea Kanban
          let kanbanHours = 0;

          // Usar actualHours si está disponible
          if (task.actualHours) {
            kanbanHours = parseFloat(task.actualHours) || 0;
          }
          // Si no, usar timerAccumulatedSeconds convertido a horas
          else if (task.timerAccumulatedSeconds) {
            kanbanHours = parseFloat((task.timerAccumulatedSeconds / 3600).toFixed(2)) || 0;
          }

          taskStats[taskId].kanbanHours += kanbanHours;
          taskStats[taskId].totalHours += kanbanHours;
          taskStats[taskId].isKanbanTask = true;
        } catch (taskError) {
          console.error('Error procesando tarea archivada:', taskError, task);
        }
      });

      // Convertir los valores a números con dos decimales
      const tasks = Object.values(taskStats).map(task => ({
        ...task,
        totalHours: Number(task.totalHours.toFixed(2)),
        manualHours: Number(task.manualHours.toFixed(2)),
        kanbanHours: Number(task.kanbanHours.toFixed(2))
      }));

      // Calcular métricas de resumen
      const totalTasks = tasks.length;
      const totalHours = tasks.reduce((sum, task) => sum + task.totalHours, 0);
      const totalManualHours = tasks.reduce((sum, task) => sum + task.manualHours, 0);
      const totalKanbanHours = tasks.reduce((sum, task) => sum + task.kanbanHours, 0);
      const totalManualEntries = tasks.reduce((sum, task) => sum + task.manualEntries, 0);
      const totalKanbanTasks = tasks.filter(task => task.isKanbanTask).length;

      res.json({
        tasks,
        summary: {
          totalTasks,
          totalHours: Number(totalHours.toFixed(2)),
          totalManualHours: Number(totalManualHours.toFixed(2)),
          totalKanbanHours: Number(totalKanbanHours.toFixed(2)),
          totalManualEntries,
          totalKanbanTasks,
          averageHoursPerTask: totalTasks > 0 ? Number((totalHours / totalTasks).toFixed(2)) : 0
        }
      });
    } else {
      const buffer = await reportService.generateKanbanReport(format);
      res.setHeader('Content-Type', format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=reporte-tareas.${format}`);
      res.send(buffer);
    }
  } catch (error) {
    console.error('Error al generar reporte de tareas:', error.message, error.stack);
    res.status(500).json({ error: 'Error al generar el reporte', details: error.message })
  }
};

// Obtener reporte por estado de aprobación
exports.getStatusReport = async (req, res) => {
  try {
    const { startDate, endDate, format = 'json' } = req.query;

    // Validar fechas requeridas
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Se requieren fechas de inicio y fin' });
    }

    if (format === 'json') {
      // Obtener registros de tiempo por estado
      const timeEntries = await prisma.timeEntry.findMany({
        where: {
          startTime: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        },
        select: {
          status: true,
        },
      });

      // Obtener permisos por estado
      const permissions = await prisma.permission.findMany({
        where: {
          startTime: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        },
        select: {
          status: true,
        },
      });

      // Contar por estado
      const statusCount = {
        PENDING: 0,
        APPROVED: 0,
        REJECTED: 0,
      };

      // Contar registros de tiempo
      timeEntries.forEach(entry => {
        statusCount[entry.status] = (statusCount[entry.status] || 0) + 1;
      });

      // Contar permisos
      permissions.forEach(permission => {
        statusCount[permission.status] = (statusCount[permission.status] || 0) + 1;
      });

      res.json({
        statusCount,
        timeEntriesCount: timeEntries.length,
        permissionsCount: permissions.length,
      });
    } else {
      const buffer = await reportService.generateStatusReport(startDate, endDate, format);
      res.setHeader('Content-Type', format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=reporte-estados.${format}`);
      res.send(buffer);
    }
  } catch (error) {
    console.error('Error al generar reporte de estados:', error);
    res.status(500).json({ error: 'Error al generar el reporte', details: error.message });
  }
};

// Dashboard Ejecutivo
exports.getExecutiveDashboard = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Obtener datos básicos
    const [users, timeEntries, tasks, permissions] = await Promise.all([
      prisma.user.findMany({ where: { role: { not: 'ADMIN' } } }),
      prisma.timeEntry.findMany({
        where: {
          startTime: { gte: new Date(startDate), lte: new Date(endDate) },
          status: 'APPROVED'
        },
        include: { user: true, task: true }
      }),
      prisma.task.findMany({
        where: {
          updatedAt: { gte: new Date(startDate), lte: new Date(endDate) }
        }
      }),
      prisma.permission.findMany({
        where: {
          startTime: { gte: new Date(startDate), lte: new Date(endDate) }
        }
      })
    ]);

    // Calcular KPIs
    const totalHours = timeEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);
    const completedTasks = tasks.filter(task => task.status === 'ARCHIVED').length;
    const totalTasks = tasks.length;
    const productivity = totalTasks > 0 ? (completedTasks / totalTasks * 100).toFixed(1) : 0;

    // Calcular costos (ejemplo con multiplicadores)
    const totalCost = timeEntries.reduce((sum, entry) => {
      const baseRate = 25; // $25/hora base
      const multiplier = entry.multiplier || 1;
      return sum + (entry.hoursWorked * baseRate * multiplier);
    }, 0);

    // Generar alertas
    const alerts = [];

    // Alerta de sobrecarga
    const overloadedUsers = users.filter(user => {
      const userHours = timeEntries
        .filter(entry => entry.userId === user.id)
        .reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);
      return userHours > 45; // Más de 45 horas por semana
    });

    if (overloadedUsers.length > 0) {
      alerts.push({
        type: 'warning',
        title: 'Sobrecarga de trabajo detectada',
        description: `${overloadedUsers.length} empleados con más de 45h semanales`,
        priority: 'high'
      });
    }

    // Alerta de productividad baja
    if (productivity < 70) {
      alerts.push({
        type: 'warning',
        title: 'Productividad por debajo del objetivo',
        description: `Productividad actual: ${productivity}% (objetivo: 70%)`,
        priority: 'medium'
      });
    }

    // Datos para gráfico de productividad por equipo
    const teamData = users.map(user => {
      const userEntries = timeEntries.filter(entry => entry.userId === user.id);
      const userTasks = tasks.filter(task => task.assigneeId === user.id);
      const userHours = userEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);
      const userCompleted = userTasks.filter(task => task.status === 'ARCHIVED').length;
      const userEfficiency = userTasks.length > 0 ? (userCompleted / userTasks.length * 100) : 0;

      return {
        name: user.name,
        hours: userHours,
        efficiency: userEfficiency.toFixed(1),
        completed: userCompleted
      };
    });

    res.json({
      kpis: {
        productivity: `${productivity}%`,
        productivityTrend: 5.2,
        totalHours: `${totalHours.toFixed(0)}h`,
        hoursTrend: -2.1,
        totalCost: `$${totalCost.toFixed(0)}`,
        costTrend: 3.8,
        activeEmployees: users.length.toString(),
        employeesTrend: 0
      },
      alerts,
      productivityChart: {
        labels: teamData.map(t => t.name),
        datasets: [{
          label: 'Horas Trabajadas',
          data: teamData.map(t => t.hours),
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
        }]
      },
      metrics: {
        totalTasks,
        completedTasks,
        pendingTasks: totalTasks - completedTasks,
        averageHoursPerUser: (totalHours / users.length).toFixed(1),
        totalPermissions: permissions.length
      }
    });
  } catch (error) {
    console.error('Error al generar dashboard ejecutivo:', error);
    res.status(500).json({ error: 'Error al generar el dashboard ejecutivo' });
  }
};

// Reporte de Productividad
exports.getProductivityReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const [users, timeEntries, tasks] = await Promise.all([
      prisma.user.findMany({ where: { role: { not: 'ADMIN' } } }),
      prisma.timeEntry.findMany({
        where: {
          startTime: { gte: new Date(startDate), lte: new Date(endDate) },
          status: 'APPROVED'
        },
        include: { user: true, task: true }
      }),
      prisma.task.findMany({
        where: {
          updatedAt: { gte: new Date(startDate), lte: new Date(endDate) }
        }
      })
    ]);

    const teams = users.map(user => {
      const userEntries = timeEntries.filter(entry => entry.userId === user.id);
      const userTasks = tasks.filter(task => task.assigneeId === user.id);

      const totalHours = userEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);
      const estimatedHours = userTasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0);
      const completedTasks = userTasks.filter(task => task.status === 'ARCHIVED').length;

      const efficiency = estimatedHours > 0 ? (totalHours / estimatedHours * 100) : 0;
      const compliance = userTasks.length > 0 ? (completedTasks / userTasks.length * 100) : 0;

      return {
        name: user.name,
        totalHours: totalHours.toFixed(1),
        efficiency: efficiency.toFixed(1),
        compliance: compliance.toFixed(1),
        completedTasks,
        totalTasks: userTasks.length
      };
    });

    res.json({
      teams,
      metrics: {
        averageEfficiency: (teams.reduce((sum, t) => sum + parseFloat(t.efficiency), 0) / teams.length).toFixed(1),
        averageCompliance: (teams.reduce((sum, t) => sum + parseFloat(t.compliance), 0) / teams.length).toFixed(1),
        totalHours: teams.reduce((sum, t) => sum + parseFloat(t.totalHours), 0).toFixed(1),
        totalTasks: teams.reduce((sum, t) => sum + t.totalTasks, 0)
      }
    });
  } catch (error) {
    console.error('Error al generar reporte de productividad:', error);
    res.status(500).json({ error: 'Error al generar el reporte de productividad' });
  }
};

// Reporte Financiero
exports.getFinancialReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const [timeEntries, tasks, users] = await Promise.all([
      prisma.timeEntry.findMany({
        where: {
          startTime: { gte: new Date(startDate), lte: new Date(endDate) },
          status: 'APPROVED'
        },
        include: { user: true, task: true }
      }),
      prisma.task.findMany({
        where: {
          updatedAt: { gte: new Date(startDate), lte: new Date(endDate) }
        }
      }),
      prisma.user.findMany({ where: { role: { not: 'ADMIN' } } })
    ]);

    // Simular proyectos agrupando por tareas
    const projects = tasks.reduce((acc, task) => {
      const projectName = task.title.split(' ')[0] || 'Proyecto General';
      if (!acc[projectName]) {
        acc[projectName] = {
          name: projectName,
          tasks: [],
          actualCost: 0,
          estimatedCost: 0,
          actualHours: 0,
          estimatedHours: 0
        };
      }
      acc[projectName].tasks.push(task);
      acc[projectName].estimatedHours += task.estimatedHours || 0;
      acc[projectName].estimatedCost += (task.estimatedHours || 0) * 25; // $25/hora base
      return acc;
    }, {});

    // Calcular costos reales
    timeEntries.forEach(entry => {
      const task = tasks.find(t => t.id === entry.taskId);
      if (task) {
        const projectName = task.title.split(' ')[0] || 'Proyecto General';
        if (projects[projectName]) {
          const baseRate = 25;
          const multiplier = entry.multiplier || 1;
          projects[projectName].actualCost += (entry.hoursWorked * baseRate * multiplier);
          projects[projectName].actualHours += entry.hoursWorked;
        }
      }
    });

    const projectsArray = Object.values(projects).map(project => ({
      ...project,
      roi: project.estimatedCost > 0 ?
        ((project.estimatedCost - project.actualCost) / project.estimatedCost * 100).toFixed(1) : 0
    }));

    res.json({
      projects: projectsArray,
      financial: {
        totalEstimatedCost: projectsArray.reduce((sum, p) => sum + p.estimatedCost, 0).toFixed(0),
        totalActualCost: projectsArray.reduce((sum, p) => sum + p.actualCost, 0).toFixed(0),
        totalSavings: projectsArray.reduce((sum, p) => sum + (p.estimatedCost - p.actualCost), 0).toFixed(0),
        averageROI: (projectsArray.reduce((sum, p) => sum + parseFloat(p.roi), 0) / projectsArray.length).toFixed(1)
      }
    });
  } catch (error) {
    console.error('Error al generar reporte financiero:', error);
    res.status(500).json({ error: 'Error al generar el reporte financiero' });
  }
};

// Reporte de Rendimiento General
exports.getPerformanceReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const [users, timeEntries, tasks, permissions] = await Promise.all([
      prisma.user.findMany({ where: { role: { not: 'ADMIN' } } }),
      prisma.timeEntry.findMany({
        where: {
          startTime: { gte: new Date(startDate), lte: new Date(endDate) },
          status: 'APPROVED'
        }
      }),
      prisma.task.findMany({
        where: {
          updatedAt: { gte: new Date(startDate), lte: new Date(endDate) }
        }
      }),
      prisma.permission.findMany({
        where: {
          startTime: { gte: new Date(startDate), lte: new Date(endDate) }
        }
      })
    ]);

    const performance = users.map(user => {
      const userEntries = timeEntries.filter(entry => entry.userId === user.id);
      const userTasks = tasks.filter(task => task.assigneeId === user.id);
      const userPermissions = permissions.filter(perm => perm.userId === user.id);

      const totalHours = userEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);
      const completedTasks = userTasks.filter(task => task.status === 'ARCHIVED').length;
      const efficiency = userTasks.length > 0 ? (completedTasks / userTasks.length * 100) : 0;

      return {
        name: user.name,
        totalHours: totalHours.toFixed(1),
        completedTasks,
        totalTasks: userTasks.length,
        efficiency: efficiency.toFixed(1),
        permissions: userPermissions.length,
        score: ((efficiency + (totalHours > 0 ? 50 : 0)) / 2).toFixed(1)
      };
    });

    res.json({
      performance,
      summary: {
        totalUsers: users.length,
        totalHours: timeEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0).toFixed(1),
        totalTasks: tasks.length,
        completedTasks: tasks.filter(task => task.status === 'ARCHIVED').length,
        averageEfficiency: (performance.reduce((sum, p) => sum + parseFloat(p.efficiency), 0) / performance.length).toFixed(1)
      }
    });
  } catch (error) {
    console.error('Error al generar reporte de rendimiento:', error);
    res.status(500).json({ error: 'Error al generar el reporte de rendimiento' });
  }
};

// Reporte de Cumplimiento de Horarios
exports.getAttendanceReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const [users, timeEntries, permissions] = await Promise.all([
      prisma.user.findMany({ where: { role: { not: 'ADMIN' } } }),
      prisma.timeEntry.findMany({
        where: {
          startTime: { gte: new Date(startDate), lte: new Date(endDate) },
          status: 'APPROVED'
        },
        include: { user: true }
      }),
      prisma.permission.findMany({
        where: {
          startTime: { gte: new Date(startDate), lte: new Date(endDate) }
        },
        include: { user: true }
      })
    ]);

    const attendance = users.map(user => {
      const userEntries = timeEntries.filter(entry => entry.userId === user.id);
      const userPermissions = permissions.filter(perm => perm.userId === user.id);

      const totalHours = userEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);
      const regularHours = userEntries.filter(entry => (entry.multiplier || 1) === 1)
        .reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);
      const overtimeHours = totalHours - regularHours;

      const workDays = new Set(userEntries.map(entry =>
        entry.startTime.toISOString().split('T')[0]
      )).size;

      return {
        name: user.name,
        totalHours: totalHours.toFixed(1),
        regularHours: regularHours.toFixed(1),
        overtimeHours: overtimeHours.toFixed(1),
        workDays,
        permissions: userPermissions.length,
        punctuality: workDays > 0 ? ((workDays - userPermissions.length) / workDays * 100).toFixed(1) : 100
      };
    });

    res.json({
      attendance,
      summary: {
        totalUsers: users.length,
        averageHours: (attendance.reduce((sum, a) => sum + parseFloat(a.totalHours), 0) / attendance.length).toFixed(1),
        totalOvertime: attendance.reduce((sum, a) => sum + parseFloat(a.overtimeHours), 0).toFixed(1),
        totalPermissions: permissions.length,
        averagePunctuality: (attendance.reduce((sum, a) => sum + parseFloat(a.punctuality), 0) / attendance.length).toFixed(1)
      }
    });
  } catch (error) {
    console.error('Error al generar reporte de asistencia:', error);
    res.status(500).json({ error: 'Error al generar el reporte de asistencia' });
  }
};

// Reporte de Calidad y Eficiencia
exports.getEfficiencyReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const [users, timeEntries, tasks] = await Promise.all([
      prisma.user.findMany({ where: { role: { not: 'ADMIN' } } }),
      prisma.timeEntry.findMany({
        where: {
          startTime: { gte: new Date(startDate), lte: new Date(endDate) },
          status: 'APPROVED'
        },
        include: { task: true }
      }),
      prisma.task.findMany({
        where: {
          updatedAt: { gte: new Date(startDate), lte: new Date(endDate) }
        }
      })
    ]);

    const efficiency = users.map(user => {
      const userEntries = timeEntries.filter(entry => entry.userId === user.id);
      const userTasks = tasks.filter(task => task.assigneeId === user.id);

      const totalActualHours = userEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);
      const totalEstimatedHours = userTasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0);

      const completedTasks = userTasks.filter(task => task.status === 'ARCHIVED').length;
      const inProgressTasks = userTasks.filter(task => task.status === 'IN_PROGRESS').length;

      const timeEfficiency = totalEstimatedHours > 0 ?
        (totalEstimatedHours / totalActualHours * 100) : 0;
      const taskEfficiency = userTasks.length > 0 ?
        (completedTasks / userTasks.length * 100) : 0;

      return {
        name: user.name,
        actualHours: totalActualHours.toFixed(1),
        estimatedHours: totalEstimatedHours.toFixed(1),
        timeEfficiency: timeEfficiency.toFixed(1),
        taskEfficiency: taskEfficiency.toFixed(1),
        completedTasks,
        inProgressTasks,
        totalTasks: userTasks.length,
        overallScore: ((timeEfficiency + taskEfficiency) / 2).toFixed(1)
      };
    });

    res.json({
      efficiency,
      summary: {
        averageTimeEfficiency: (efficiency.reduce((sum, e) => sum + parseFloat(e.timeEfficiency), 0) / efficiency.length).toFixed(1),
        averageTaskEfficiency: (efficiency.reduce((sum, e) => sum + parseFloat(e.taskEfficiency), 0) / efficiency.length).toFixed(1),
        totalCompletedTasks: efficiency.reduce((sum, e) => sum + e.completedTasks, 0),
        totalTasks: efficiency.reduce((sum, e) => sum + e.totalTasks, 0)
      }
    });
  } catch (error) {
    console.error('Error al generar reporte de eficiencia:', error);
    res.status(500).json({ error: 'Error al generar el reporte de eficiencia' });
  }
};

// Reporte de Distribución de Carga
exports.getWorkloadReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const [users, timeEntries, tasks] = await Promise.all([
      prisma.user.findMany({ where: { role: { not: 'ADMIN' } } }),
      prisma.timeEntry.findMany({
        where: {
          startTime: { gte: new Date(startDate), lte: new Date(endDate) },
          status: 'APPROVED'
        }
      }),
      prisma.task.findMany({
        where: {
          updatedAt: { gte: new Date(startDate), lte: new Date(endDate) }
        }
      })
    ]);

    const workload = users.map(user => {
      const userEntries = timeEntries.filter(entry => entry.userId === user.id);
      const userTasks = tasks.filter(task => task.assigneeId === user.id);

      const totalHours = userEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);
      const activeTasks = userTasks.filter(task => task.status !== 'ARCHIVED').length;
      const completedTasks = userTasks.filter(task => task.status === 'ARCHIVED').length;

      // Calcular carga de trabajo (horas por tarea activa)
      const workloadRatio = activeTasks > 0 ? (totalHours / activeTasks).toFixed(1) : 0;

      // Determinar nivel de carga
      let loadLevel = 'Normal';
      if (totalHours > 45) loadLevel = 'Sobrecargado';
      else if (totalHours < 20) loadLevel = 'Subcargado';

      return {
        name: user.name,
        totalHours: totalHours.toFixed(1),
        activeTasks,
        completedTasks,
        workloadRatio,
        loadLevel,
        utilization: ((totalHours / 40) * 100).toFixed(1) // Asumiendo 40h como estándar
      };
    });

    // Calcular distribución
    const totalHours = workload.reduce((sum, w) => sum + parseFloat(w.totalHours), 0);
    const distribution = workload.map(w => ({
      ...w,
      percentage: ((parseFloat(w.totalHours) / totalHours) * 100).toFixed(1)
    }));

    res.json({
      workload: distribution,
      summary: {
        totalUsers: users.length,
        overloadedUsers: workload.filter(w => w.loadLevel === 'Sobrecargado').length,
        underloadedUsers: workload.filter(w => w.loadLevel === 'Subcargado').length,
        averageUtilization: (workload.reduce((sum, w) => sum + parseFloat(w.utilization), 0) / workload.length).toFixed(1),
        totalActiveTasks: workload.reduce((sum, w) => sum + w.activeTasks, 0)
      }
    });
  } catch (error) {
    console.error('Error al generar reporte de carga de trabajo:', error);
    res.status(500).json({ error: 'Error al generar el reporte de carga de trabajo' });
  }
};

// Reporte Predictivo
exports.getPredictiveReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const [users, timeEntries, tasks] = await Promise.all([
      prisma.user.findMany({ where: { role: { not: 'ADMIN' } } }),
      prisma.timeEntry.findMany({
        where: {
          startTime: { gte: new Date(startDate), lte: new Date(endDate) },
          status: 'APPROVED'
        }
      }),
      prisma.task.findMany({
        where: {
          updatedAt: { gte: new Date(startDate), lte: new Date(endDate) }
        }
      })
    ]);

    // Calcular velocidad promedio de completado
    const completedTasks = tasks.filter(task => task.status === 'ARCHIVED');
    const inProgressTasks = tasks.filter(task => task.status === 'IN_PROGRESS');

    const predictions = users.map(user => {
      const userEntries = timeEntries.filter(entry => entry.userId === user.id);
      const userCompletedTasks = completedTasks.filter(task => task.assigneeId === user.id);
      const userInProgressTasks = inProgressTasks.filter(task => task.assigneeId === user.id);

      const totalHours = userEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);
      const avgHoursPerTask = userCompletedTasks.length > 0 ?
        (totalHours / userCompletedTasks.length) : 8; // Default 8h por tarea

      // Predecir tiempo para completar tareas en progreso
      const estimatedHoursToComplete = userInProgressTasks.length * avgHoursPerTask;

      // Predecir fecha de finalización (asumiendo 8h por día)
      const daysToComplete = Math.ceil(estimatedHoursToComplete / 8);
      const estimatedCompletionDate = new Date();
      estimatedCompletionDate.setDate(estimatedCompletionDate.getDate() + daysToComplete);

      // Identificar riesgos
      const risks = [];
      if (userInProgressTasks.length > 5) {
        risks.push('Alto número de tareas en progreso');
      }
      if (avgHoursPerTask > 12) {
        risks.push('Tareas complejas que requieren más tiempo');
      }
      if (totalHours > 45) {
        risks.push('Posible sobrecarga de trabajo');
      }

      return {
        name: user.name,
        inProgressTasks: userInProgressTasks.length,
        avgHoursPerTask: avgHoursPerTask.toFixed(1),
        estimatedHoursToComplete: estimatedHoursToComplete.toFixed(1),
        estimatedCompletionDate: estimatedCompletionDate.toISOString().split('T')[0],
        risks,
        riskLevel: risks.length > 2 ? 'Alto' : risks.length > 0 ? 'Medio' : 'Bajo'
      };
    });

    res.json({
      predictions,
      insights: {
        totalInProgressTasks: inProgressTasks.length,
        averageCompletionTime: (predictions.reduce((sum, p) => sum + parseFloat(p.avgHoursPerTask), 0) / predictions.length).toFixed(1),
        highRiskUsers: predictions.filter(p => p.riskLevel === 'Alto').length,
        totalEstimatedHours: predictions.reduce((sum, p) => sum + parseFloat(p.estimatedHoursToComplete), 0).toFixed(1)
      }
    });
  } catch (error) {
    console.error('Error al generar reporte predictivo:', error);
    res.status(500).json({ error: 'Error al generar el reporte predictivo' });
  }
};

// Reporte resumen de permisos
exports.getPermissionsSummaryReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Se requieren fechas de inicio y fin' });
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Obtener todos los permisos en el rango de fechas
    const permissions = await prisma.permission.findMany({
      where: {
        createdAt: {
          gte: start,
          lte: end
        }
      },
      include: {
        user: {
          select: { name: true }
        }
      }
    });

    // Calcular estadísticas
    const summary = {
      total: permissions.length,
      pending: permissions.filter(p => p.status === 'PENDING').length,
      approved: permissions.filter(p => p.status === 'APPROVED').length,
      rejected: permissions.filter(p => p.status === 'REJECTED').length,
    };

    summary.approvalRate = summary.total > 0
      ? ((summary.approved / (summary.approved + summary.rejected)) * 100).toFixed(1)
      : 0;

    // Calcular días promedio
    const approvedPermissions = permissions.filter(p => p.status === 'APPROVED');
    const totalDays = approvedPermissions.reduce((sum, p) => {
      const days = Math.ceil((new Date(p.endTime) - new Date(p.startTime)) / (1000 * 60 * 60 * 24));
      return sum + days;
    }, 0);
    summary.averageDays = approvedPermissions.length > 0
      ? (totalDays / approvedPermissions.length).toFixed(1)
      : 0;

    // Tipo más solicitado
    const typeCount = {};
    permissions.forEach(p => {
      typeCount[p.type] = (typeCount[p.type] || 0) + 1;
    });
    const mostRequestedTypeEnum = Object.keys(typeCount).reduce((a, b) =>
      typeCount[a] > typeCount[b] ? a : b, 'N/A'
    );
    summary.mostRequestedType = enumToSpanish(mostRequestedTypeEnum);

    // Permisos por tipo (convertir a español)
    const byType = Object.entries(typeCount).map(([type, count]) => ({
      type: enumToSpanish(type),
      count
    }));

    // Agregar días a cada permiso para la tabla y convertir tipos a español
    const permissionsWithDays = permissions.map(p => ({
      ...p,
      type: enumToSpanish(p.type),
      days: Math.ceil((new Date(p.endTime) - new Date(p.startTime)) / (1000 * 60 * 60 * 24))
    }));

    res.json({
      summary,
      byType,
      permissions: permissionsWithDays
    });
  } catch (error) {
    console.error('Error al generar reporte resumen de permisos:', error);
    res.status(500).json({ error: 'Error al generar el reporte de permisos' });
  }
};

// Reporte de permisos por tipo
exports.getPermissionsByTypeReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Se requieren fechas de inicio y fin' });
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    const permissions = await prisma.permission.findMany({
      where: {
        createdAt: {
          gte: start,
          lte: end
        }
      }
    });

    // Agrupar por tipo
    const typeStats = {};
    permissions.forEach(p => {
      if (!typeStats[p.type]) {
        typeStats[p.type] = {
          type: p.type,
          count: 0,
          totalDays: 0,
          approved: 0,
          rejected: 0,
          pending: 0
        };
      }

      typeStats[p.type].count++;
      typeStats[p.type][p.status.toLowerCase()]++;

      const days = Math.ceil((new Date(p.endTime) - new Date(p.startTime)) / (1000 * 60 * 60 * 24));
      typeStats[p.type].totalDays += days;
    });

    // Calcular promedios y tasas, y convertir tipos a español
    const byType = Object.values(typeStats).map(stat => ({
      ...stat,
      type: enumToSpanish(stat.type),
      averageDays: stat.count > 0 ? stat.totalDays / stat.count : 0,
      approvalRate: (stat.approved + stat.rejected) > 0
        ? (stat.approved / (stat.approved + stat.rejected)) * 100
        : 0
    }));

    const summary = {
      totalTypes: byType.length,
      totalPermissions: permissions.length,
      averagePerType: permissions.length / byType.length || 0
    };

    res.json({
      byType,
      summary
    });
  } catch (error) {
    console.error('Error al generar reporte de permisos por tipo:', error);
    res.status(500).json({ error: 'Error al generar el reporte de permisos por tipo' });
  }
};

// Reporte de permisos por usuario
exports.getPermissionsByUserReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Se requieren fechas de inicio y fin' });
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    const permissions = await prisma.permission.findMany({
      where: {
        createdAt: {
          gte: start,
          lte: end
        }
      },
      include: {
        user: {
          select: { name: true }
        }
      }
    });

    // Agrupar por usuario
    const userStats = {};
    permissions.forEach(p => {
      const userName = p.user?.name || 'Usuario Desconocido';
      if (!userStats[userName]) {
        userStats[userName] = {
          name: userName,
          count: 0,
          totalDays: 0,
          pending: 0,
          approved: 0,
          rejected: 0
        };
      }

      userStats[userName].count++;
      userStats[userName][p.status.toLowerCase()]++;

      const days = Math.ceil((new Date(p.endTime) - new Date(p.startTime)) / (1000 * 60 * 60 * 24));
      userStats[userName].totalDays += days;
    });

    const byUser = Object.values(userStats);

    const summary = {
      totalUsers: byUser.length,
      totalPermissions: permissions.length,
      averagePerUser: permissions.length / byUser.length || 0,
      topUser: byUser.reduce((max, user) => user.count > max.count ? user : max, { count: 0 })?.name || 'N/A'
    };

    res.json({
      byUser,
      summary
    });
  } catch (error) {
    console.error('Error al generar reporte de permisos por usuario:', error);
    res.status(500).json({ error: 'Error al generar el reporte de permisos por usuario' });
  }
};

// Reporte de tendencias de permisos
exports.getPermissionsTrendsReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Se requieren fechas de inicio y fin' });
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    const permissions = await prisma.permission.findMany({
      where: {
        createdAt: {
          gte: start,
          lte: end
        }
      }
    });

    // Agrupar por mes
    const monthlyStats = {};
    permissions.forEach(p => {
      const month = new Date(p.createdAt).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long'
      });

      if (!monthlyStats[month]) {
        monthlyStats[month] = {
          month,
          count: 0,
          totalDays: 0,
          types: {}
        };
      }

      monthlyStats[month].count++;
      const days = Math.ceil((new Date(p.endTime) - new Date(p.startTime)) / (1000 * 60 * 60 * 24));
      monthlyStats[month].totalDays += days;

      monthlyStats[month].types[p.type] = (monthlyStats[month].types[p.type] || 0) + 1;
    });

    // Calcular tendencias
    const trends = Object.values(monthlyStats).map(stat => ({
      ...stat,
      averageDays: stat.count > 0 ? stat.totalDays / stat.count : 0,
      mostCommonType: Object.keys(stat.types).reduce((a, b) =>
        stat.types[a] > stat.types[b] ? a : b, 'N/A'
      )
    }));

    // Análisis estacional
    const seasonal = [
      { season: 'Verano', count: 0 },
      { season: 'Otoño', count: 0 },
      { season: 'Invierno', count: 0 },
      { season: 'Primavera', count: 0 }
    ];

    permissions.forEach(p => {
      const month = new Date(p.createdAt).getMonth();
      if (month >= 11 || month <= 1) seasonal[2].count++; // Invierno
      else if (month >= 2 && month <= 4) seasonal[3].count++; // Primavera
      else if (month >= 5 && month <= 7) seasonal[0].count++; // Verano
      else seasonal[1].count++; // Otoño
    });

    const summary = {
      totalMonths: trends.length,
      averagePerMonth: permissions.length / trends.length || 0,
      peakMonth: trends.reduce((max, trend) => trend.count > max.count ? trend : max, { count: 0 })?.month || 'N/A'
    };

    res.json({
      trends,
      seasonal,
      summary
    });
  } catch (error) {
    console.error('Error al generar reporte de tendencias de permisos:', error);
    res.status(500).json({ error: 'Error al generar el reporte de tendencias de permisos' });
  }
};