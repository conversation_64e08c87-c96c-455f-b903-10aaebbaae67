#!/usr/bin/env node

/**
 * Script para verificar que todas las dependencias necesarias 
 * para las integraciones API estén en package.json
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verificando dependencias para integraciones API...\n');

// Dependencias requeridas para las integraciones API
const requiredDependencies = {
  'axios': 'Para peticiones HTTP a APIs externas',
  '@prisma/client': 'Cliente de base de datos',
  'crypto': 'Módulo nativo de Node.js para encriptación',
  'express': 'Framework web',
  'jsonwebtoken': 'Para autenticación JWT'
};

// Leer package.json del servidor
const packageJsonPath = path.join(__dirname, 'package.json');

if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ No se encontró server/package.json');
  process.exit(1);
}

const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

console.log('📦 Verificando dependencias requeridas:\n');

let allDependenciesPresent = true;

for (const [dep, description] of Object.entries(requiredDependencies)) {
  if (dep === 'crypto') {
    // crypto es un módulo nativo de Node.js
    console.log(`✅ ${dep} - ${description} (módulo nativo)`);
  } else if (dependencies[dep]) {
    console.log(`✅ ${dep}@${dependencies[dep]} - ${description}`);
  } else {
    console.log(`❌ ${dep} - ${description} (FALTANTE)`);
    allDependenciesPresent = false;
  }
}

console.log('\n📋 Resumen de verificación:');

if (allDependenciesPresent) {
  console.log('✅ Todas las dependencias están presentes');
  console.log('✅ El proyecto es completamente portable');
  console.log('\n🚀 Para desplegar en otro servidor:');
  console.log('   1. Copiar toda la carpeta del proyecto');
  console.log('   2. Ejecutar: docker-compose up --build');
  console.log('   3. ¡Listo! Todas las dependencias se instalarán automáticamente');
} else {
  console.log('❌ Faltan dependencias requeridas');
  console.log('⚠️ El proyecto podría no funcionar correctamente en otro servidor');
}

console.log('\n📊 Estadísticas del proyecto:');
console.log(`   - Total dependencias: ${Object.keys(dependencies).length}`);
console.log(`   - Dependencias de producción: ${Object.keys(packageJson.dependencies || {}).length}`);
console.log(`   - Dependencias de desarrollo: ${Object.keys(packageJson.devDependencies || {}).length}`);

// Verificar archivos críticos para integraciones API
console.log('\n🗂️ Verificando archivos críticos:');

const criticalFiles = [
  'src/controllers/apiIntegrationController.js',
  'src/controllers/apiImportController.js',
  'src/routes/apiIntegrationRoutes.js',
  'src/services/apiExecutionService.js'
];

let allFilesPresent = true;

for (const file of criticalFiles) {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} (FALTANTE)`);
    allFilesPresent = false;
  }
}

if (allFilesPresent) {
  console.log('\n✅ Todos los archivos críticos están presentes');
} else {
  console.log('\n❌ Faltan archivos críticos');
}

// Verificar configuración de Docker
console.log('\n🐳 Verificando configuración de Docker:');

const dockerComposePath = path.join(__dirname, '../docker-compose.yml');
if (fs.existsSync(dockerComposePath)) {
  const dockerCompose = fs.readFileSync(dockerComposePath, 'utf8');
  
  if (dockerCompose.includes('API_KEY_ENCRYPTION_KEY')) {
    console.log('✅ Variable de entorno API_KEY_ENCRYPTION_KEY configurada');
  } else {
    console.log('❌ Variable de entorno API_KEY_ENCRYPTION_KEY no configurada');
  }
  
  console.log('✅ docker-compose.yml presente');
} else {
  console.log('❌ docker-compose.yml no encontrado');
}

console.log('\n🎯 Conclusión:');
if (allDependenciesPresent && allFilesPresent) {
  console.log('🎉 El proyecto está completamente configurado y es portable');
  console.log('📦 Todas las dependencias se instalarán automáticamente en cualquier servidor');
} else {
  console.log('⚠️ Hay problemas que podrían afectar la portabilidad');
}

console.log('\n📚 Para más información, consulta: API_INTEGRATIONS_README.md');
