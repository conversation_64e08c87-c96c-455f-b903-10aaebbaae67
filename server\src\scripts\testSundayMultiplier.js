const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testSundayMultiplier() {
  try {
    console.log('🧪 PROBANDO MULTIPLICADOR DEL DOMINGO...\n');

    // 1. Verificar configuración actual
    const workSchedules = await prisma.workSchedule.findMany({
      orderBy: { dayOfWeek: 'asc' }
    });

    const timeMultipliers = await prisma.timeMultiplier.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    });

    console.log('📅 HORARIOS CONFIGURADOS:');
    const dayNames = ['Domingo', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Mi<PERSON><PERSON><PERSON><PERSON>', 'Jueves', 'Viernes', 'Sábado'];
    workSchedules.forEach(schedule => {
      console.log(`  - ${dayNames[schedule.dayOfWeek]}: ${schedule.startTime}-${schedule.endTime} (${schedule.multiplier}x)`);
    });

    console.log('\n📊 MULTIPLICADORES ACTIVOS:');
    timeMultipliers.forEach(mult => {
      console.log(`  - ${mult.name}: ${mult.value}x ${mult.startTime ? `(${mult.startTime}-${mult.endTime})` : '(sin horario específico)'}`);
    });

    // 2. Simular la lógica del dashboard
    console.log('\n🔧 SIMULANDO LÓGICA DEL DASHBOARD...');

    // Configuración por defecto
    let defaultSchedule = {
      startTime: '09:00',
      endTime: '18:00',
      lunchBreak: '13:00',
      lunchDuration: 60,
      workDays: [1, 2, 3, 4, 5], // Lunes a Viernes
      allowedOutsideHours: 8,
      assumedWeeklyHours: 40
    };

    if (workSchedules.length > 0) {
      const template = workSchedules[0];
      
      // IMPORTANTE: Excluir domingo (día 0) de workDays
      const allWorkDays = workSchedules.map(s => s.dayOfWeek);
      const workDaysExcludingSunday = allWorkDays.filter(day => day !== 0);
      
      defaultSchedule = {
        startTime: template.startTime,
        endTime: template.endTime,
        lunchBreak: template.lunchBreak,
        lunchDuration: template.lunchDuration,
        workDays: workDaysExcludingSunday,
        allowedOutsideHours: template.allowedOutsideHours || 8,
        assumedWeeklyHours: template.assumedWeeklyHours || 40
      };
    }

    console.log('⚙️ CONFIGURACIÓN APLICADA:');
    console.log(`  - Horario: ${defaultSchedule.startTime} - ${defaultSchedule.endTime}`);
    console.log(`  - Días laborables: ${defaultSchedule.workDays.map(d => dayNames[d]).join(', ')}`);
    console.log(`  - ¿Domingo incluido en workDays?: ${defaultSchedule.workDays.includes(0) ? 'SÍ' : 'NO'}`);

    // 3. Función de cálculo de multiplicador
    const calculateMultiplier = (dayOfWeek) => {
      let applicableMultiplier = 1.0;
      let multiplierReasons = [];

      // 1. Verificar multiplicador por día de la semana
      const workSchedule = workSchedules.find(ws => ws.dayOfWeek === dayOfWeek);
      if (workSchedule && workSchedule.multiplier > 1.0) {
        applicableMultiplier = Math.max(applicableMultiplier, workSchedule.multiplier);
        multiplierReasons.push(`${dayNames[dayOfWeek]}: ${workSchedule.multiplier}x`);
      }

      // 2. Domingos automáticamente tienen multiplicador 2.0x
      if (dayOfWeek === 0) {
        applicableMultiplier = Math.max(applicableMultiplier, 2.0);
        multiplierReasons.push(`Domingo (considerado feriado): 2.0x`);
      }

      return { multiplier: applicableMultiplier, reasons: multiplierReasons };
    };

    // 4. Probar cada día de la semana
    console.log('\n🗓️ PRUEBA DE MULTIPLICADORES POR DÍA:');
    for (let day = 0; day < 7; day++) {
      const isWorkDay = defaultSchedule.workDays.includes(day);
      const { multiplier, reasons } = calculateMultiplier(day);

      console.log(`  ${dayNames[day]} (día ${day}):`);
      console.log(`    - ¿Es día laborable?: ${isWorkDay ? 'SÍ' : 'NO'}`);
      console.log(`    - Multiplicador: ${multiplier}x`);
      console.log(`    - Razones: ${reasons.join(', ') || 'Ninguna'}`);

      if (day === 0) {
        // Para domingo, el multiplicador debe ser 2.0x independientemente de si es día laborable
        console.log(`    - 🎯 RESULTADO DOMINGO: ${multiplier === 2.0 ? '✅ CORRECTO' : '❌ INCORRECTO'}`);
        if (multiplier === 2.0) {
          console.log(`    - ✅ El domingo tiene multiplicador 2.0x (independiente de configuración laborable)`);
          console.log(`    - ✅ TODAS las horas del domingo se multiplicarán por 2.0x`);
        } else {
          console.log(`    - ❌ Problema: El domingo debería tener multiplicador 2.0x`);
        }
      }
      console.log('');
    }

    // 5. Simular lógica específica del dashboard para domingo
    console.log('\n🧪 SIMULACIÓN DE LÓGICA DEL DASHBOARD PARA DOMINGO:');

    // Simular una entrada del domingo
    const sundayEntry = {
      dayOfWeek: 0,
      hoursWorked: 4.0,
      title: 'Trabajo domingo (simulado)'
    };

    console.log(`📝 Entrada simulada: ${sundayEntry.title} - ${sundayEntry.hoursWorked}h`);

    // Aplicar lógica del dashboard
    let hoursToMultiply = 0;
    let treatmentType = '';

    if (sundayEntry.dayOfWeek === 0) {
      // DOMINGO: Siempre aplicar multiplicador a TODAS las horas
      hoursToMultiply = sundayEntry.hoursWorked;
      treatmentType = 'DOMINGO - todas las horas';
    } else if (!defaultSchedule.workDays.includes(sundayEntry.dayOfWeek)) {
      // Día no laborable: aplicar multiplicador a TODAS las horas
      hoursToMultiply = sundayEntry.hoursWorked;
      treatmentType = 'día no laborable';
    } else {
      // Día laborable: aplicar multiplicador solo a las horas fuera de oficina
      hoursToMultiply = 0; // Simularíamos cálculo de horas fuera de oficina
      treatmentType = 'día laborable - solo horas fuera de oficina';
    }

    const { multiplier: sundayMultiplier } = calculateMultiplier(0);
    const weightedHours = hoursToMultiply * sundayMultiplier;

    console.log(`🔧 Tratamiento: ${treatmentType}`);
    console.log(`⚖️ Horas a multiplicar: ${hoursToMultiply}h`);
    console.log(`📊 Multiplicador aplicado: ${sundayMultiplier}x`);
    console.log(`💎 Horas ponderadas resultantes: ${weightedHours}h`);
    console.log(`🎯 ¿Resultado correcto?: ${hoursToMultiply === sundayEntry.hoursWorked && sundayMultiplier === 2.0 ? '✅ SÍ' : '❌ NO'}`);

    if (hoursToMultiply === sundayEntry.hoursWorked && sundayMultiplier === 2.0) {
      console.log(`✅ PERFECTO: El domingo usa TODAS las horas (${sundayEntry.hoursWorked}h) con multiplicador 2.0x = ${weightedHours}h`);
    } else {
      console.log(`❌ PROBLEMA: Configuración incorrecta para el domingo`);
    }

    // 6. Buscar entradas de tiempo del domingo para verificar
    console.log('\n📝 VERIFICANDO ENTRADAS REALES DEL DOMINGO...');
    const sundayEntries = await prisma.timeEntry.findMany({
      where: {
        status: 'APPROVED',
        startTime: {
          gte: new Date('2024-01-01'),
        }
      },
      include: {
        user: {
          select: { name: true }
        }
      }
    });

    const sundayTimeEntries = sundayEntries.filter(entry => {
      const entryDate = new Date(entry.startTime);
      return entryDate.getDay() === 0; // Domingo
    });

    console.log(`📊 ENTRADAS DE TIEMPO EN DOMINGO ENCONTRADAS: ${sundayTimeEntries.length}`);
    if (sundayTimeEntries.length > 0) {
      console.log('Ejemplos de entradas del domingo:');
      sundayTimeEntries.slice(0, 3).forEach(entry => {
        const entryDate = new Date(entry.startTime);
        const { multiplier } = calculateMultiplier(0);
        const expectedWeightedHours = entry.hoursWorked * multiplier;
        console.log(`  - ${entry.user.name}: "${entry.title}"`);
        console.log(`    📅 Fecha: ${entryDate.toISOString().split('T')[0]}`);
        console.log(`    ⏰ Horas trabajadas: ${entry.hoursWorked}h`);
        console.log(`    📊 Multiplicador: ${multiplier}x`);
        console.log(`    💎 Horas ponderadas esperadas: ${expectedWeightedHours}h`);
        console.log('');
      });
    } else {
      console.log('ℹ️ No se encontraron entradas del domingo para probar');
    }

    console.log('\n✅ PRUEBA COMPLETADA');
    console.log('🎯 RESUMEN DEL FIX:');
    console.log('   - El domingo SIEMPRE tiene multiplicador 2.0x');
    console.log('   - TODAS las horas del domingo se multiplican (no solo las fuera de oficina)');
    console.log('   - Esto funciona independientemente de si el domingo está configurado como día laborable');
    console.log('   - Las horas del domingo aparecerán en la columna "Horas × 2.0" del dashboard');

  } catch (error) {
    console.error('❌ ERROR EN PRUEBA:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  testSundayMultiplier();
}

module.exports = { testSundayMultiplier };
