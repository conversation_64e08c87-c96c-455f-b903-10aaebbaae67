import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Chip,
  IconButton,
  Box,
  Alert,
  Snackbar,
  CircularProgress,
  Tooltip
} from '@mui/material';
import {
  Save as SaveIcon,
  TestTube as TestIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { API_URL } from '../config';

const UserApiKeysManagement = ({ open, integration, onClose }) => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [apiKeys, setApiKeys] = useState({});
  const [showApiKeys, setShowApiKeys] = useState({});
  const [testingUsers, setTestingUsers] = useState({});
  const [savingUsers, setSavingUsers] = useState({});

  useEffect(() => {
    if (open && integration) {
      fetchUserApiKeys();
    }
  }, [open, integration]);

  const fetchUserApiKeys = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/integrations/${integration.id}/users`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data);
        
        // Inicializar estado de API keys
        const initialApiKeys = {};
        data.forEach(user => {
          if (user.userApiKeys && user.userApiKeys.length > 0) {
            initialApiKeys[user.id] = '••••••••••••'; // Mostrar como oculto
          }
        });
        setApiKeys(initialApiKeys);
      } else {
        throw new Error('Error al cargar usuarios');
      }
    } catch (error) {
      console.error('Error al cargar usuarios:', error);
      showSnackbar('Error al cargar los usuarios', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleApiKeyChange = (userId, value) => {
    setApiKeys(prev => ({
      ...prev,
      [userId]: value
    }));
  };

  const toggleShowApiKey = (userId) => {
    setShowApiKeys(prev => ({
      ...prev,
      [userId]: !prev[userId]
    }));
  };

  const saveUserApiKey = async (userId) => {
    const apiKey = apiKeys[userId];
    if (!apiKey || apiKey === '••••••••••••') {
      showSnackbar('Por favor ingrese una API key válida', 'warning');
      return;
    }

    setSavingUsers(prev => ({ ...prev, [userId]: true }));
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/integrations/${integration.id}/users/${userId}/api-key`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ apiKey })
      });

      if (response.ok) {
        showSnackbar('API key guardada exitosamente');
        fetchUserApiKeys(); // Refrescar datos
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Error al guardar API key');
      }
    } catch (error) {
      console.error('Error al guardar API key:', error);
      showSnackbar(`Error al guardar: ${error.message}`, 'error');
    } finally {
      setSavingUsers(prev => ({ ...prev, [userId]: false }));
    }
  };

  const testUserApiKey = async (userId) => {
    setTestingUsers(prev => ({ ...prev, [userId]: true }));
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/integrations/${integration.id}/users/${userId}/test`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      
      if (result.success) {
        showSnackbar('Conexión exitosa');
      } else {
        showSnackbar(`Error en la conexión: ${result.message}`, 'error');
      }
      
      // Refrescar datos para mostrar el estado actualizado
      fetchUserApiKeys();
      
    } catch (error) {
      console.error('Error al probar API key:', error);
      showSnackbar(`Error al probar conexión: ${error.message}`, 'error');
    } finally {
      setTestingUsers(prev => ({ ...prev, [userId]: false }));
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (user) => {
    if (!user.userApiKeys || user.userApiKeys.length === 0) {
      return 'No configurado';
    }
    
    const apiKey = user.userApiKeys[0];
    if (!apiKey.lastTested) {
      return 'No probado';
    }
    
    return apiKey.testStatus === 'success' ? 'Funcionando' : 'Error';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Nunca';
    return new Date(dateString).toLocaleString();
  };

  if (!integration) {
    return null;
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        Gestión de API Keys - {integration.name}
      </DialogTitle>
      
      <DialogContent>
        <Alert severity="info" sx={{ mb: 3 }}>
          Configure las API keys personales de cada usuario para esta integración. 
          Cada usuario necesita su propia API key para acceder a sus datos en el sistema externo.
        </Alert>

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Usuario</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>API Key</TableCell>
                  <TableCell>Último Test</TableCell>
                  <TableCell>Estado</TableCell>
                  <TableCell>Acciones</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Typography variant="subtitle2">{user.name}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="textSecondary">
                        {user.email}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <TextField
                          size="small"
                          type={showApiKeys[user.id] ? 'text' : 'password'}
                          placeholder="Ingrese API key personal"
                          value={apiKeys[user.id] || ''}
                          onChange={(e) => handleApiKeyChange(user.id, e.target.value)}
                          sx={{ minWidth: 200 }}
                        />
                        <IconButton
                          size="small"
                          onClick={() => toggleShowApiKey(user.id)}
                        >
                          {showApiKeys[user.id] ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDate(user.userApiKeys?.[0]?.lastTested)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={getStatusLabel(user)}
                        color={getStatusColor(user.userApiKeys?.[0]?.testStatus)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Tooltip title="Guardar API key">
                          <span>
                            <IconButton
                              size="small"
                              onClick={() => saveUserApiKey(user.id)}
                              disabled={savingUsers[user.id] || !apiKeys[user.id] || apiKeys[user.id] === '••••••••••••'}
                            >
                              {savingUsers[user.id] ? (
                                <CircularProgress size={16} />
                              ) : (
                                <SaveIcon />
                              )}
                            </IconButton>
                          </span>
                        </Tooltip>
                        
                        <Tooltip title="Probar conexión">
                          <span>
                            <IconButton
                              size="small"
                              onClick={() => testUserApiKey(user.id)}
                              disabled={testingUsers[user.id] || !user.userApiKeys?.[0]}
                            >
                              {testingUsers[user.id] ? (
                                <CircularProgress size={16} />
                              ) : (
                                <TestIcon />
                              )}
                            </IconButton>
                          </span>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Instrucciones para Usuarios
          </Typography>
          <Alert severity="warning">
            <Typography variant="body2">
              <strong>Para obtener su API key personal:</strong>
            </Typography>
            <Typography variant="body2" component="div" sx={{ mt: 1 }}>
              1. Inicie sesión en {integration.name}<br/>
              2. Vaya a Configuración → API → Generar Token<br/>
              3. Copie su API key personal<br/>
              4. Proporcione esta clave al administrador para configurarla aquí
            </Typography>
          </Alert>
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Cerrar</Button>
      </DialogActions>

      {/* Snackbar para notificaciones */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Dialog>
  );
};

export default UserApiKeysManagement;
