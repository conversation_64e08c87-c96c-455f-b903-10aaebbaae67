const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const socketService = require('../services/socketService');
const { isWithinWorkHours } = require('../utils/workHourUtils');

// Obtener todas las tareas organizadas por estado
exports.getKanbanBoard = async (req, res) => {
  try {
    // Obtener el usuario actual del token
    const userId = req.user.id;


    // Obtener tareas con sus relaciones (excluir tareas manuales y archivadas)
    const tasks = await prisma.task.findMany({
      where: {
        AND: [
          { source: { not: 'MANUAL_ENTRY' } }, // Excluir tareas de registros manuales
          { status: { not: 'ARCHIVED' } } // Excluir tareas archivadas del Kanban
        ]
      },
      include: {
        assignee: true,
        creator: true,
        timeEntries: true,
        activeTimers: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Organizar tareas por estado (solo tareas del kanban)
    const columns = {
      TODO: {
        id: 'TODO',
        title: 'Por Hacer',
        tasks: [],
      },
      IN_PROGRESS: {
        id: 'IN_PROGRESS',
        title: 'En Progreso',
        tasks: [],
      },
      REVIEW: {
        id: 'REVIEW',
        title: 'Revisión',
        tasks: [],
      },
      DONE: {
        id: 'DONE',
        title: 'Completado',
        tasks: [],
      },
    };

    // Distribuir tareas en las columnas
    tasks.forEach(task => {
      if (columns[task.status]) {
        // Calcular el tiempo acumulado de TODOS los contadores activos
        let activeTimerSeconds = 0;
        
        // Sumar el tiempo de todos los temporizadores activos
        task.activeTimers.forEach(timer => {
          let timerSeconds = timer.accumulatedTime;
          
          // Si el contador está corriendo, agregar el tiempo transcurrido desde la última vez que se inició
          if (timer.isRunning) {
            const now = new Date();
            const elapsedSeconds = Math.floor((now - new Date(timer.lastStarted)) / 1000);
            timerSeconds += elapsedSeconds;
          }
          
          activeTimerSeconds += timerSeconds;
        });
        // Buscar si hay un timer activo para esta tarea y el usuario actual
        const userActiveTimer = task.activeTimers.find(timer => timer.userId === req.user.id);
        
        if (userActiveTimer) {
          activeTimerSeconds = userActiveTimer.accumulatedTime;
          
          // Si el contador está corriendo, agregar el tiempo transcurrido desde la última vez que se inició
          if (userActiveTimer.isRunning) {
            const now = new Date();
            const elapsedSeconds = Math.floor((now - new Date(userActiveTimer.lastStarted)) / 1000);
            activeTimerSeconds += elapsedSeconds;
          }
        }
        
        columns[task.status].tasks.push({
          id: String(task.id), // Convertir a string para consistencia
          title: task.title,
          description: task.description,
          assignee: task.assignee ? task.assignee.name : 'Sin asignar',
          assigneeId: task.assigneeId,
          estimatedHours: task.estimatedHours,
          actualHours: (() => {
            // Calcular horas desde time entries
            const timeEntriesHours = task.timeEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);

            // Calcular horas desde el temporizador global
            let globalTimerSeconds = task.timerAccumulatedSeconds || 0;

            // Si el temporizador está corriendo, agregar el tiempo actual
            if (task.timerIsRunning && task.timerLastStarted) {
              const now = new Date();
              const currentElapsed = Math.floor((now - new Date(task.timerLastStarted)) / 1000);
              globalTimerSeconds += currentElapsed;
            }

            const globalTimerHours = globalTimerSeconds / 3600;
            return timeEntriesHours + globalTimerHours;
          })(),
          dueDate: task.dueDate,
          status: task.status,
          createdAt: task.createdAt,
          activeTimer: userActiveTimer ? {
            id: userActiveTimer.id,
            isRunning: userActiveTimer.isRunning,
            accumulatedTime: activeTimerSeconds,
            startTime: userActiveTimer.startTime,
            lastStarted: userActiveTimer.lastStarted
          } : null
        });
      }
    });

    res.json(columns);
  } catch (error) {
    console.error('Error al obtener el tablero Kanban:', error);
    res.status(500).json({ error: 'Error al obtener el tablero Kanban' });
  }
};

// Crear una nueva tarea
exports.createTask = async (req, res) => {
  try {
    const { title, description, assigneeId, estimatedHours, dueDate, status, dynamicFields } = req.body;
    const creatorId = req.user.id;

    console.log('Datos recibidos para crear tarea:', req.body);
    console.log('Usuario actual:', req.user);

    // Validar datos requeridos
    if (!title) {
      return res.status(400).json({ error: 'El título es obligatorio' });
    }

    // Crear la tarea (del kanban)
    const task = await prisma.task.create({
      data: {
        title,
        description: description || '',
        status: status || 'TODO',
        source: 'KANBAN', // Marcar como tarea del kanban
        estimatedHours: estimatedHours ? parseFloat(estimatedHours) : 0,
        dueDate: dueDate ? new Date(dueDate) : null,
        assigneeId: assigneeId || creatorId, // Si no se especifica assigneeId, usar el creatorId
        creatorId,
      },
      include: {
        assignee: true,
        creator: true,
        timeEntries: true,
      },
    });

    // Formatear la respuesta para mantener consistencia con el frontend
    const formattedTask = {
      id: String(task.id), // Convertir a string para consistencia
      title: task.title,
      description: task.description,
      assignee: task.assignee ? task.assignee.name : 'Sin asignar',
      assigneeId: task.assigneeId,
      estimatedHours: task.estimatedHours,
      actualHours: (() => {
        // Calcular horas desde time entries
        const timeEntriesHours = task.timeEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);

        // Calcular horas desde el temporizador global
        let globalTimerSeconds = task.timerAccumulatedSeconds || 0;

        // Si el temporizador está corriendo, agregar el tiempo actual
        if (task.timerIsRunning && task.timerLastStarted) {
          const now = new Date();
          const currentElapsed = Math.floor((now - new Date(task.timerLastStarted)) / 1000);
          globalTimerSeconds += currentElapsed;
        }

        const globalTimerHours = globalTimerSeconds / 3600;
        return timeEntriesHours + globalTimerHours;
      })(),
      dueDate: task.dueDate,
      status: task.status,
      createdAt: task.createdAt,
    };

    // Guardar valores de campos dinámicos si existen
    if (dynamicFields && Object.keys(dynamicFields).length > 0) {
      // Obtener los IDs de los campos dinámicos
      const fieldNames = Object.keys(dynamicFields);
      const fields = await prisma.dynamicField.findMany({
        where: {
          name: {
            in: fieldNames
          }
        },
        select: {
          id: true,
          name: true
        }
      });

      const dynamicFieldValues = Object.entries(dynamicFields)
        .map(([fieldName, value]) => {
          const field = fields.find(f => f.name === fieldName);
          if (!field || !value) return null;

          return {
            fieldId: field.id,
            entityType: 'task',
            entityId: task.id,
            value: String(value)
          };
        })
        .filter(Boolean);

      if (dynamicFieldValues.length > 0) {
        await prisma.dynamicFieldValue.createMany({
          data: dynamicFieldValues,
          skipDuplicates: true
        });
      }
    }

    console.log('Tarea creada:', formattedTask);
    res.status(201).json(formattedTask);
  } catch (error) {
    console.error('Error al crear tarea:', error);
    res.status(500).json({ 
      error: 'Error al crear la tarea',
      details: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

// Actualizar el estado de una tarea
exports.updateTaskStatus = async (req, res) => {
  try {
    console.log('Actualizando estado de tarea. ID:', req.params.id, 'Nuevo estado:', req.body.status);
    const taskId = req.params.id;
    
    // Validar que el estado es válido
    const validStatuses = ['TODO', 'IN_PROGRESS', 'REVIEW', 'DONE', 'ARCHIVED', 'COMPLETED'];
    if (!validStatuses.includes(req.body.status)) {
      console.error('Estado inválido:', req.body.status);
      return res.status(400).json({ message: 'Estado inválido' });
    }
    
    // Verificar que la tarea existe
    const existingTask = await prisma.task.findUnique({
      where: { id: taskId }
    });
    
    if (!existingTask) {
      console.error('Tarea no encontrada con ID:', taskId);
      return res.status(404).json({ message: 'Tarea no encontrada' });
    }
    
    console.log('Tarea encontrada:', existingTask);

    // Verificar permisos: solo el assignee, admins o managers pueden mover la tarea
    const canMoveTask = existingTask.assigneeId === req.user.id ||
                       req.user.role === 'ADMIN' ||
                       req.user.role === 'MANAGER';

    if (!canMoveTask) {
      console.log(`[TaskPermission] User ${req.user.id} (${req.user.name}, role: ${req.user.role}) cannot move task ${taskId} assigned to ${existingTask.assigneeId}`);
      return res.status(403).json({
        message: 'No tienes permisos para mover esta tarea. Solo el asignado, un manager o un administrador pueden hacerlo.',
        restrictionType: 'permission_denied'
      });
    }
    console.log(`[TaskPermission] User ${req.user.id} (${req.user.name}, role: ${req.user.role}) has permission to move task ${taskId}`);

    // Verificar horario de trabajo si se está moviendo a IN_PROGRESS
    if (req.body.status === 'IN_PROGRESS') {
      console.log(`[TaskStatus] Checking work hours for task ${taskId} move to IN_PROGRESS`);
      const { isWithinWorkHours: withinHours, message, debug } = await isWithinWorkHours();
      
      if (!withinHours) {
        console.log(`[TaskStatus] Task ${taskId} move rejected: Outside work hours`, debug);
        
        // Make the error message more explicit
        const errorMessage = `Fuera de horario laboral: ${message}`;
        
        // Add a restrictionType field to help the frontend identify this specific error
        return res.status(403).json({ 
          error: errorMessage,
          message: errorMessage,
          taskTitle: existingTask.title,
          restrictionType: 'work_hours',
          debug: process.env.NODE_ENV === 'development' ? debug : undefined
        });
      }
      
      console.log(`[TaskStatus] Task ${taskId} move allowed: Within work hours`);
    }
    
    try {
      // Manejar el temporizador global cuando la tarea se mueve a IN_PROGRESS
      if (req.body.status === 'IN_PROGRESS') {
        const now = new Date();

        // Si el temporizador no está corriendo, iniciarlo
        if (!existingTask.timerIsRunning) {
          await prisma.task.update({
            where: { id: taskId },
            data: {
              timerIsRunning: true,
              timerStartTime: existingTask.timerStartTime || now, // Mantener tiempo inicial si existe
              timerLastStarted: now,
              timerCurrentUserId: req.user.id
            }
          });
          console.log(`[GlobalTimer] Started timer for task ${taskId} by user ${req.user.name}`);
        } else {
          console.log(`[GlobalTimer] Timer already running for task ${taskId}`);
        }
      } else if (req.body.status !== 'IN_PROGRESS') {
        // Si la tarea se mueve a otro estado, pausar el temporizador global
        if (existingTask.timerIsRunning) {
          const now = new Date();
          const elapsedSeconds = Math.floor((now - new Date(existingTask.timerLastStarted)) / 1000);

          await prisma.task.update({
            where: { id: taskId },
            data: {
              timerIsRunning: false,
              timerAccumulatedSeconds: existingTask.timerAccumulatedSeconds + elapsedSeconds,
              timerCurrentUserId: null
            }
          });
          console.log(`[GlobalTimer] Paused timer for task ${taskId}. Added ${elapsedSeconds} seconds. Total: ${existingTask.timerAccumulatedSeconds + elapsedSeconds} seconds`);
        }
      }

      // Si la tarea se marca como completada, calcular las horas reales y archivar automáticamente
      let updateData = { status: req.body.status };

      // Auto-archivar tareas que se marcan como DONE
      if (req.body.status === 'DONE') {
        updateData.status = 'ARCHIVED';
        console.log(`[AutoArchive] Task ${taskId} moved from DONE to ARCHIVED automatically`);
      }

      if (updateData.status === 'ARCHIVED' || req.body.status === 'DONE') {
        // Obtener todas las entradas de tiempo asociadas a la tarea
        const timeEntries = await prisma.timeEntry.findMany({
          where: { taskId: taskId }
        });

        // Calcular horas desde time entries
        const timeEntriesHours = timeEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);

        // Calcular horas desde el temporizador global
        let globalTimerSeconds = existingTask.timerAccumulatedSeconds || 0;

        // MIGRAR ACTIVETIMERS AL TEMPORIZADOR GLOBAL
        // Obtener todos los ActiveTimers de esta tarea
        const activeTimers = await prisma.activeTimer.findMany({
          where: { taskId: taskId }
        });

        let activeTimerSeconds = 0;
        for (const timer of activeTimers) {
          let timerSeconds = timer.accumulatedTime || 0;

          // Si el timer está corriendo, agregar tiempo transcurrido
          if (timer.isRunning && timer.lastStarted) {
            const now = new Date();
            const elapsedSeconds = Math.floor((now - new Date(timer.lastStarted)) / 1000);
            timerSeconds += elapsedSeconds;

            // Pausar el ActiveTimer
            await prisma.activeTimer.update({
              where: { id: timer.id },
              data: {
                isRunning: false,
                accumulatedTime: timerSeconds
              }
            });
          }

          activeTimerSeconds += timerSeconds;
        }

        // Sumar tiempo de ActiveTimers al temporizador global
        globalTimerSeconds += activeTimerSeconds;

        if (activeTimerSeconds > 0) {
          console.log(`[ActiveTimerMigration] Task ${taskId}: Migrated ${activeTimerSeconds} seconds from ActiveTimers to global timer`);

          // Actualizar el temporizador global con el tiempo migrado
          updateData.timerAccumulatedSeconds = globalTimerSeconds;
        }

        // Si el temporizador está corriendo, agregar el tiempo actual
        if (existingTask.timerIsRunning && existingTask.timerLastStarted) {
          const now = new Date();
          const currentElapsed = Math.floor((now - new Date(existingTask.timerLastStarted)) / 1000);
          globalTimerSeconds += currentElapsed;

          // Pausar el temporizador al completar la tarea
          updateData.timerIsRunning = false;
          updateData.timerAccumulatedSeconds = globalTimerSeconds;
          updateData.timerCurrentUserId = null;
        }

        // Convertir segundos a horas
        const globalTimerHours = globalTimerSeconds / 3600;
        const totalHours = timeEntriesHours + globalTimerHours;

        updateData.actualHours = Math.max(totalHours, 0);

        console.log(`[TaskHours] Task ${taskId} completed with ${totalHours.toFixed(2)} hours (${timeEntriesHours.toFixed(2)}h from entries + ${globalTimerHours.toFixed(2)}h from global timer)`);
      }
      
      // Actualizar el estado de la tarea y las horas reales si corresponde
      // Update task with include to get all relationships
      const updatedTask = await prisma.task.update({
        where: { id: taskId },
        data: updateData,
        include: {
          assignee: true,
          creator: true,
          timeEntries: true,
          activeTimers: true,
          timerCurrentUser: true,
        }
      });
      
      // Format the response to match the frontend expectations
      const formattedTask = {
        id: String(updatedTask.id),
        title: updatedTask.title,
        description: updatedTask.description,
        assignee: updatedTask.assignee ? updatedTask.assignee.name : 'Sin asignar',
        assigneeId: updatedTask.assigneeId,
        estimatedHours: updatedTask.estimatedHours,
        actualHours: updatedTask.actualHours || 0,
        dueDate: updatedTask.dueDate,
        status: updatedTask.status,
        createdAt: updatedTask.createdAt,
        globalTimer: null // Initialize global timer
      };

      // Add global timer information
      if (updatedTask.timerIsRunning || updatedTask.timerAccumulatedSeconds > 0) {
        let totalSeconds = updatedTask.timerAccumulatedSeconds || 0;

        // Si está corriendo, agregar tiempo transcurrido
        if (updatedTask.timerIsRunning && updatedTask.timerLastStarted) {
          const now = new Date();
          const elapsedSeconds = Math.floor((now - new Date(updatedTask.timerLastStarted)) / 1000);
          totalSeconds += elapsedSeconds;
        }

        formattedTask.globalTimer = {
          isRunning: updatedTask.timerIsRunning,
          accumulatedTime: totalSeconds,
          startTime: updatedTask.timerStartTime,
          lastStarted: updatedTask.timerLastStarted,
          currentUser: updatedTask.timerCurrentUser ? updatedTask.timerCurrentUser.name : null,
          currentUserId: updatedTask.timerCurrentUserId
        };
      }
      
      // Notify all clients about the task movement
      socketService.notifyTaskMovement(formattedTask);
      
      res.json(formattedTask);
      
    } catch (updateError) {
      console.error('Error específico al actualizar la tarea:', updateError);
      res.status(500).json({ 
        message: 'Error al actualizar el estado de la tarea', 
        error: updateError.message, 
        stack: updateError.stack,
        taskId: taskId,
        requestedStatus: req.body.status
      });
    }
  } catch (error) {
    console.error('Error general al actualizar estado de tarea:', error);
    res.status(500).json({ 
      message: 'Error al actualizar el estado de la tarea', 
      error: error.message, 
      stack: error.stack 
    });
  }
};

// Actualizar una tarea
exports.updateTask = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, assigneeId, estimatedHours, dueDate } = req.body;

    // Convertir id a string para consistencia
    const taskId = String(id);

    const task = await prisma.task.update({
      where: { id: taskId },
      data: {
        title,
        description,
        assigneeId,
        estimatedHours: parseFloat(estimatedHours),
        dueDate: dueDate ? new Date(dueDate) : null,
      },
      include: {
        assignee: true,
      },
    });

    // Formatear la respuesta para el cliente
    const formattedTask = {
      id: String(task.id), // Convertir a string para consistencia
      title: task.title,
      description: task.description,
      assignee: task.assignee ? task.assignee.name : null,
      assigneeId: task.assigneeId,
      estimatedHours: task.estimatedHours,
      dueDate: task.dueDate,
      createdAt: task.createdAt,
    };

    res.json(formattedTask);
  } catch (error) {
    console.error('Error al actualizar tarea:', error);
    res.status(500).json({ error: 'Error al actualizar la tarea' });
  }
};

// Eliminar una tarea
exports.deleteTask = async (req, res) => {
  try {
    const { id } = req.params;

    // Convertir id a string para consistencia
    const taskId = String(id);

    await prisma.task.delete({
      where: { id: taskId },
    });

    res.json({ message: 'Tarea eliminada correctamente' });
  } catch (error) {
    console.error('Error al eliminar tarea:', error);
    res.status(500).json({ error: 'Error al eliminar la tarea' });
  }
};

// Obtener tareas archivadas (incluye tareas del kanban archivadas y tareas manuales completadas)
exports.getArchivedTasks = async (req, res) => {
  try {
    const tasks = await prisma.task.findMany({
      where: {
        OR: [
          { status: 'ARCHIVED', source: 'KANBAN' }, // Tareas del kanban archivadas (legacy)
          { status: 'DONE', source: 'KANBAN' }, // Tareas del kanban completadas (nuevo flujo)
          { status: 'COMPLETED', source: 'MANUAL_ENTRY' } // Tareas manuales completadas
        ]
      },
      include: {
        assignee: true,
        creator: true,
        timeEntries: true,
        activeTimers: true,
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    // Formatear los datos para el frontend
    const formattedTasks = tasks.map(task => {
      // Calculate total time from time entries
      const timeEntriesTotal = task.timeEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);

      // Calculate total time from global timer (NEW SYSTEM)
      let globalTimerSeconds = task.timerAccumulatedSeconds || 0;

      // If timer is still running, add elapsed time
      if (task.timerIsRunning && task.timerLastStarted) {
        const now = new Date();
        const elapsedSeconds = Math.floor((now - new Date(task.timerLastStarted)) / 1000);
        globalTimerSeconds += elapsedSeconds;
      }

      // Convert seconds from global timer to hours
      const globalTimerHours = globalTimerSeconds / 3600;

      // Use actualHours if available (already calculated), otherwise calculate manually
      const totalHours = task.actualHours || (timeEntriesTotal + globalTimerHours);
      
      return {
        id: task.id,
        title: task.title,
        description: task.description,
        assignee: task.assignee ? task.assignee.name : 'Sin asignar',
        assigneeId: task.assigneeId,
        estimatedHours: task.estimatedHours,
        actualHours: totalHours || 0, // Ensure we always return a number, not null
        dueDate: task.dueDate,
        status: task.status,
        source: task.source, // Agregar el campo source
        timerStartTime: task.source === 'MANUAL_ENTRY' && task.timeEntries.length > 0
          ? task.timeEntries[0].startTime // Para tareas manuales, usar startTime del timeEntry
          : task.timerStartTime, // Para tareas Kanban, usar timerStartTime
        timerEndTime: task.source === 'MANUAL_ENTRY' && task.timeEntries.length > 0
          ? task.timeEntries[0].endTime // Para tareas manuales, usar endTime del timeEntry
          : (task.timerStartTime && totalHours > 0
              ? new Date(new Date(task.timerStartTime).getTime() + (totalHours * 60 * 60 * 1000)) // Para Kanban: Inicio + horas reales
              : null), // Para tareas sin timer o sin horas, no hay tiempo de fin
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
        creator: task.creator ? task.creator.name : 'Desconocido'
      };
    });

    res.json(formattedTasks);
  } catch (error) {
    console.error('Error al obtener tareas archivadas:', error);
    res.status(500).json({ error: 'Error al obtener tareas archivadas' });
  }
};

// Test endpoint to check work hour restrictions
exports.checkWorkHours = async (req, res) => {
  try {
    const { isWithinWorkHours: withinHours, message, debug } = await isWithinWorkHours();
    
    res.json({
      isWithinWorkHours: withinHours,
      message,
      debug,
      currentTime: new Date().toLocaleString(),
      user: req.user.name
    });
  } catch (error) {
    console.error('Error checking work hours:', error);
    res.status(500).json({ error: 'Error checking work hours' });
  }
};

// Añadir al final del archivo kanbanController.js

// Endpoint para mover automáticamente tareas de IN_PROGRESS a REVIEW fuera del horario laboral
exports.moverTareasARevision = async (req, res) => {
  try {
    const { moverTareasARevision } = require('../utils/workHourUtils');
    const resultado = await moverTareasARevision();
    
    res.json({
      success: resultado.success,
      message: resultado.message,
      count: resultado.count
    });
  } catch (error) {
    console.error('Error al mover tareas a revisión:', error);
    res.status(500).json({ error: 'Error al mover tareas a revisión' });
  }
};
