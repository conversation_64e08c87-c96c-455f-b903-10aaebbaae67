import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Stack,
  Chip,
  Snackbar,
  Alert,
  TextField,
  Box,
  MenuItem,
  Card,
  CardContent,
  Grid,
  Tooltip,
  IconButton,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  AccessTime as TimeIcon,
  CalendarToday as CalendarIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import axios from 'axios';
import { API_URL } from '../config';
import PermissionDetailsModal from '../components/PermissionDetailsModal';
import PermissionFilters from '../components/PermissionFilters';

// Los tipos de permisos ahora se cargan dinámicamente desde Settings
// const permissionTypes = {}; // Removido - ahora se carga desde API

const PermissionApproval = () => {
  const [permissions, setPermissions] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    employeeId: '',
    startDate: null,
    endDate: null,
    permissionStartDate: null,
    permissionEndDate: null,
    searchText: '',
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // Estado para el modal de detalles
  const [detailsModal, setDetailsModal] = useState({
    open: false,
    permission: null,
  });

  // Estado para configuración del horario laboral
  const [workSchedule, setWorkSchedule] = useState({
    startTime: '09:00',
    endTime: '18:00',
    lunchBreak: '13:00',
    lunchDuration: 60,
  });

  // Estado para tipos de permisos dinámicos
  const [permissionTypes, setPermissionTypes] = useState([]);

  useEffect(() => {
    fetchPermissions();
    fetchWorkSchedule();
    fetchEmployees();
    fetchPermissionTypes();
  }, []);

  const fetchPermissions = async (appliedFilters = {}) => {
    try {
      console.log('🔍 OBTENIENDO PERMISOS PARA ADMIN...');
      console.log('🔍 FILTROS APLICADOS:', appliedFilters);

      // Construir parámetros de consulta
      const queryParams = new URLSearchParams();

      // Agregar filtros si existen
      Object.entries(appliedFilters).forEach(([key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
          if (value instanceof Date) {
            queryParams.append(key, value.toISOString());
          } else {
            queryParams.append(key, value);
          }
        }
      });

      const response = await axios.get(`${API_URL}/api/permissions/pending?${queryParams.toString()}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      console.log('✅ PERMISOS OBTENIDOS:', response.data);
      setPermissions(response.data);
    } catch (error) {
      console.error('❌ ERROR AL OBTENER PERMISOS:', error);
      setSnackbar({
        open: true,
        message: 'Error al cargar los permisos',
        severity: 'error',
      });
    }
  };

  const fetchEmployees = async () => {
    try {
      const response = await axios.get(`${API_URL}/api/users`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      setEmployees(response.data);
    } catch (error) {
      console.error('Error al obtener empleados:', error);
    }
  };

  // Manejar cambios en los filtros
  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    fetchPermissions(newFilters);
  };

  const fetchWorkSchedule = async () => {
    try {
      const response = await axios.get(`${API_URL}/api/settings/work-schedules`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      if (response.data && response.data.length > 0) {
        const schedule = response.data[0];
        setWorkSchedule({
          startTime: schedule.startTime || '09:00',
          endTime: schedule.endTime || '18:00',
          lunchBreak: schedule.lunchBreak || '13:00',
          lunchDuration: schedule.lunchDuration || 60,
        });
      }
    } catch (error) {
      console.error('Error al obtener horario laboral:', error);
      // Mantener valores por defecto
    }
  };

  const fetchPermissionTypes = async () => {
    try {
      const response = await axios.get(`${API_URL}/api/settings/permission-types`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });

      // Convertir los tipos de la base de datos al formato esperado por el frontend
      const formattedTypes = response.data
        .filter(type => type.active) // Solo tipos activos
        .map(type => ({
          value: type.name, // Usar el nombre directamente como valor
          label: type.label,
          maxDaysPerYear: type.maxDaysPerYear,
          requiresApproval: type.requiresApproval
        }));

      setPermissionTypes(formattedTypes);
    } catch (error) {
      console.error('Error al obtener tipos de permisos:', error);
      // Usar tipos por defecto en caso de error
      setPermissionTypes([
        { value: 'Vacaciones', label: 'Vacaciones', maxDaysPerYear: 30, requiresApproval: true },
        { value: 'Enfermedad', label: 'Descanso Médico', maxDaysPerYear: 15, requiresApproval: false },
        { value: 'Personal', label: 'Permiso Personal', maxDaysPerYear: 10, requiresApproval: true },
        { value: 'Compensacion', label: '⏰ Compensación de Horas Extra', maxDaysPerYear: 0, requiresApproval: true },
        { value: 'Otros', label: 'Otro', maxDaysPerYear: 5, requiresApproval: true },
      ]);
    }
  };

  const getPermissionTypeLabel = (typeValue) => {
    const type = permissionTypes.find(t => t.value === typeValue);
    return type ? type.label : typeValue;
  };

  const handleStatusChange = async (permission, newStatus) => {
    try {
      await axios.patch(
        `${API_URL}/api/permissions/${permission.id}/status`,
        { 
          status: newStatus
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );

      setPermissions(perms =>
        perms.map(perm =>
          perm.id === permission.id ? { ...perm, status: newStatus } : perm
        )
      );

      setSnackbar({
        open: true,
        message: `Permiso ${newStatus === 'APPROVED' ? 'aprobado' : 'rechazado'} correctamente`,
        severity: 'success',
      });
      
      fetchPermissions(); // Recargar la lista
    } catch (error) {
      console.error('Error al actualizar estado:', error);
      setSnackbar({
        open: true,
        message: 'Error al actualizar el estado del permiso',
        severity: 'error',
      });
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'APPROVED':
        return 'success';
      case 'REJECTED':
        return 'error';
      default:
        return 'warning';
    }
  };

  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: es });
    } catch (error) {
      return 'Fecha inválida';
    }
  };

  const formatDateOnly = (dateString) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });
    } catch (error) {
      return 'Fecha inválida';
    }
  };

  const formatTimeOnly = (dateString) => {
    try {
      return format(new Date(dateString), 'HH:mm', { locale: es });
    } catch (error) {
      return 'Hora inválida';
    }
  };

  const calculatePermissionHours = (permission) => {
    const start = new Date(permission.startTime);
    const end = new Date(permission.endTime);

    // Si es un permiso de día completo, calcular usando la configuración del horario laboral
    if (isFullDayPermission(permission)) {
      // Calcular horas de trabajo por día (descontando almuerzo)
      const [startHour, startMinute] = workSchedule.startTime.split(':').map(Number);
      const [endHour, endMinute] = workSchedule.endTime.split(':').map(Number);

      const startMinutes = startHour * 60 + startMinute;
      const endMinutes = endHour * 60 + endMinute;
      const totalMinutes = endMinutes - startMinutes;
      const totalHours = totalMinutes / 60;

      // Descontar tiempo de almuerzo
      const lunchHours = workSchedule.lunchDuration / 60;
      const workingHoursPerDay = totalHours - lunchHours;

      // Calcular días laborales
      const workingDays = calculateWorkingDays(permission);

      return Math.max(0, workingHoursPerDay * workingDays);
    } else {
      // Para permisos por horas específicas, calcular la diferencia directa
      const diffInMs = end - start;
      const diffInHours = diffInMs / (1000 * 60 * 60);
      return Math.max(0, diffInHours);
    }
  };

  const calculateWorkingDays = (permission) => {
    const start = new Date(permission.startTime);
    const end = new Date(permission.endTime);
    let workingDays = 0;

    const currentDate = new Date(start);
    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay();
      if (dayOfWeek >= 1 && dayOfWeek <= 5) {
        workingDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return workingDays;
  };

  const isFullDayPermission = (permission) => {
    const startTime = formatTimeOnly(permission.startTime);
    const endTime = formatTimeOnly(permission.endTime);

    // Usar la configuración del horario laboral para determinar si es día completo
    return (startTime === workSchedule.startTime && endTime === workSchedule.endTime);
  };

  const getPermissionStats = () => {
    const pending = permissions.filter(p => p.status === 'PENDING').length;
    const approved = permissions.filter(p => p.status === 'APPROVED').length;
    const rejected = permissions.filter(p => p.status === 'REJECTED').length;
    const totalHours = permissions.reduce((sum, p) => sum + calculatePermissionHours(p), 0);

    return { pending, approved, rejected, total: permissions.length, totalHours };
  };

  // Funciones para manejar el modal de detalles
  const handleOpenDetails = (permission) => {
    setDetailsModal({
      open: true,
      permission: permission,
    });
  };

  const handleCloseDetails = () => {
    setDetailsModal({
      open: false,
      permission: null,
    });
  };

  const stats = getPermissionStats();

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        📋 Aprobación de Permisos
      </Typography>

      {/* Resumen estadístico */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'warning.50', border: '1px solid', borderColor: 'warning.200' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h4" color="warning.main" fontWeight="bold">
                {stats.pending}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                ⏳ Pendientes
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'success.50', border: '1px solid', borderColor: 'success.200' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h4" color="success.main" fontWeight="bold">
                {stats.approved}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                ✅ Aprobados
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'error.50', border: '1px solid', borderColor: 'error.200' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h4" color="error.main" fontWeight="bold">
                {stats.rejected}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                ❌ Rechazados
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'info.50', border: '1px solid', borderColor: 'info.200' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h4" color="info.main" fontWeight="bold">
                {stats.totalHours.toFixed(1)}h
              </Typography>
              <Typography variant="body2" color="textSecondary">
                ⏰ Total Horas
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filtros de permisos */}
      <PermissionFilters
        onFiltersChange={handleFiltersChange}
        currentFilters={filters}
        showEmployeeFilter={true}
        employees={employees}
        permissionTypes={permissionTypes.map(pt => pt.label)}
      />

      <Paper sx={{ p: 2, mb: 3 }}>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell><PersonIcon sx={{ mr: 1, verticalAlign: 'middle' }} />Empleado</TableCell>
                <TableCell>Tipo</TableCell>
                <TableCell><CalendarIcon sx={{ mr: 1, verticalAlign: 'middle' }} />Fechas</TableCell>
                <TableCell><TimeIcon sx={{ mr: 1, verticalAlign: 'middle' }} />Duración</TableCell>
                <TableCell>Motivo</TableCell>
                <TableCell>Estado</TableCell>
                <TableCell>Acciones</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {permissions.length > 0 ? (
                permissions.map((permission) => (
                  <TableRow key={permission.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {permission.user.name || permission.user.email}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {permission.user.email}
                        </Typography>
                      </Box>
                    </TableCell>

                    <TableCell>
                      <Chip
                        label={getPermissionTypeLabel(permission.type)}
                        color={permission.type === 'Compensacion' ? 'secondary' : 'primary'}
                        variant="outlined"
                        size="small"
                      />
                    </TableCell>

                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          <strong>Inicio:</strong> {formatDateOnly(permission.startTime)}
                        </Typography>
                        <Typography variant="body2">
                          <strong>Fin:</strong> {formatDateOnly(permission.endTime)}
                        </Typography>
                        {!isFullDayPermission(permission) && (
                          <Typography variant="caption" color="textSecondary">
                            {formatTimeOnly(permission.startTime)} - {formatTimeOnly(permission.endTime)}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>

                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold" color="primary.main">
                          {calculatePermissionHours(permission).toFixed(1)}h
                        </Typography>
                        {isFullDayPermission(permission) ? (
                          <Chip
                            label={`${calculateWorkingDays(permission)} día${calculateWorkingDays(permission) !== 1 ? 's' : ''}`}
                            size="small"
                            color="info"
                            variant="outlined"
                          />
                        ) : (
                          <Typography variant="caption" color="textSecondary">
                            Horas específicas
                          </Typography>
                        )}
                        {permission.type === 'OVERTIME_COMPENSATION' && permission.overtimeHoursUsed && (
                          <Typography variant="caption" display="block" color="secondary.main">
                            ⏰ {permission.overtimeHoursUsed.toFixed(1)}h extra
                          </Typography>
                        )}
                      </Box>
                    </TableCell>

                    <TableCell>
                      <Typography variant="body2" sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {permission.reason}
                      </Typography>
                    </TableCell>

                    <TableCell>
                      <Chip
                        label={permission.status === 'PENDING' ? 'Pendiente' :
                               permission.status === 'APPROVED' ? 'Aprobado' : 'Rechazado'}
                        color={getStatusColor(permission.status)}
                        size="small"
                      />
                      {permission.approvedAt && (
                        <Typography variant="caption" display="block" color="textSecondary">
                          {formatDateOnly(permission.approvedAt)}
                        </Typography>
                      )}
                    </TableCell>

                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        {permission.status === 'PENDING' && (
                          <>
                            <Tooltip title="Aprobar permiso">
                              <Button
                                size="small"
                                variant="contained"
                                color="success"
                                onClick={() => handleStatusChange(permission, 'APPROVED')}
                              >
                                ✓ Aprobar
                              </Button>
                            </Tooltip>
                            <Tooltip title="Rechazar permiso">
                              <Button
                                size="small"
                                variant="contained"
                                color="error"
                                onClick={() => handleStatusChange(permission, 'REJECTED')}
                              >
                                ✗ Rechazar
                              </Button>
                            </Tooltip>
                          </>
                        )}
                        <Tooltip title="Ver detalles">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleOpenDetails(permission)}
                          >
                            <VisibilityIcon />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <Typography variant="body2" color="textSecondary">
                      No hay permisos que coincidan con los filtros
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Modal de detalles del permiso */}
      <PermissionDetailsModal
        open={detailsModal.open}
        onClose={handleCloseDetails}
        permission={detailsModal.permission}
        showEmployeeInfo={true}
      />
    </Container>
  );
};

export default PermissionApproval;
