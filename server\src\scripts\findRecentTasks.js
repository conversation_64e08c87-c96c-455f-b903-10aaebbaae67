const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function findRecentTasks() {
  try {
    console.log('🔍 BUSCANDO TAREAS RECIENTES');
    console.log('='.repeat(60));

    // Buscar las 10 tareas más recientes
    const recentEntries = await prisma.timeEntry.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    console.log(`\n📝 ÚLTIMAS ${recentEntries.length} ENTRADAS:`);
    
    recentEntries.forEach((entry, index) => {
      console.log(`\n${index + 1}. ID: ${entry.id}`);
      console.log(`   Título: "${entry.title}"`);
      console.log(`   Usuario: ${entry.user?.name || 'Desconocido'}`);
      console.log(`   Estado: ${entry.status}`);
      console.log(`   Creado: ${entry.createdAt}`);
      console.log(`   Inicio: ${entry.startTime}`);
      console.log(`   Fin: ${entry.endTime}`);
      console.log(`   Horas: ${entry.hoursWorked}`);
    });

    // Buscar específicamente tareas que contengan "prueba"
    const pruebaEntries = await prisma.timeEntry.findMany({
      where: {
        title: {
          contains: 'prueba',
          mode: 'insensitive'
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`\n🧪 TAREAS QUE CONTIENEN "PRUEBA": ${pruebaEntries.length}`);
    
    pruebaEntries.forEach((entry, index) => {
      console.log(`\n${index + 1}. ID: ${entry.id}`);
      console.log(`   Título: "${entry.title}"`);
      console.log(`   Usuario: ${entry.user?.name || 'Desconocido'}`);
      console.log(`   Estado: ${entry.status}`);
      console.log(`   Creado: ${entry.createdAt}`);
      console.log(`   Inicio: ${entry.startTime}`);
      console.log(`   Fin: ${entry.endTime}`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

findRecentTasks();
