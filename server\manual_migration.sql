-- Manual migration to add task source and completed status
-- Run this in your Docker PostgreSQL database

-- Step 1: Add new values to TaskStatus enum
ALTER TYPE "TaskStatus" ADD VALUE IF NOT EXISTS 'DONE';
ALTER TYPE "TaskStatus" ADD VALUE IF NOT EXISTS 'COMPLETED';

-- Step 2: Add source column to Task table
ALTER TABLE "Task" ADD COLUMN IF NOT EXISTS "source" TEXT NOT NULL DEFAULT 'KANBAN';

-- Step 3: Update existing tasks to have KANBAN source
UPDATE "Task" SET "source" = 'KANBAN' WHERE "source" IS NULL OR "source" = '';

-- Step 4: Add comment for documentation
COMMENT ON COLUMN "Task"."source" IS 'Source of the task: KANBAN, MANUAL_ENTRY, IMPORT';

-- Step 5: Verify the changes
SELECT 
    column_name, 
    data_type, 
    column_default, 
    is_nullable 
FROM information_schema.columns 
WHERE table_name = 'Task' 
ORDER BY ordinal_position;

-- Step 6: Show enum values
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid 
    FROM pg_type 
    WHERE typname = 'TaskStatus'
);

-- Step 7: Show sample data
SELECT id, title, status, source, "createdAt" 
FROM "Task" 
ORDER BY "createdAt" DESC 
LIMIT 5;
