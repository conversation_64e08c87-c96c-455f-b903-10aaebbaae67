{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-25T21:00:00.048Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-25T21:00:00.051Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-25T21:00:00.051Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-25T22:00:00.367Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-25T22:00:00.369Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-25T22:00:00.369Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T04:00:00.956Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T04:00:00.963Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-26T04:00:00.965Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T05:00:00.216Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T05:00:00.218Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-26T05:00:00.218Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T07:00:00.669Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T07:00:00.674Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-26T07:00:00.675Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T08:00:00.621Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T08:00:00.625Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-26T08:00:00.626Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T09:00:00.696Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T09:00:00.697Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-26T09:00:00.698Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T10:00:00.879Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T10:00:00.880Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-26T10:00:00.880Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T11:00:00.836Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T11:00:00.839Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-26T11:00:00.840Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T12:00:00.621Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T12:00:00.622Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-26T12:00:00.622Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T13:00:00.403Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T13:00:00.404Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-26T13:00:00.405Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T14:00:00.215Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T14:00:00.216Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-26T14:00:00.217Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T15:00:00.930Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-26T15:00:00.931Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-26T15:00:00.932Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-27T02:00:00.673Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-27T02:00:00.675Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-27T02:00:00.675Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-28T17:00:00.469Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-28T17:00:00.472Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-28T17:00:00.472Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-28T19:00:00.295Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-28T19:00:00.297Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-28T19:00:00.297Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-28T20:00:00.196Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-28T20:00:00.197Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-28T20:00:00.198Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-28T21:00:00.692Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-02-28T21:00:00.694Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-02-28T21:00:00.719Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-01T02:00:00.111Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-01T02:00:00.114Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-03-01T02:00:00.114Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-01T03:00:00.768Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-01T03:00:00.778Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-03-01T03:00:00.779Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-01T04:00:00.761Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-01T04:00:00.764Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-03-01T04:00:00.765Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-04T20:00:00.845Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-04T20:00:00.847Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-03-04T20:00:00.847Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at exports.getLDAPStatus (/app/src/controllers/ldapController.js:18:43)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at exports.isAdmin (/app/src/middleware/auth.js:37:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/app/node_modules/express/lib/router/index.js:328:13)\n    at /app/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)","timestamp":"2025-03-04T20:10:37.858Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at exports.getLDAPStatus (/app/src/controllers/ldapController.js:18:43)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at exports.isAdmin (/app/src/middleware/auth.js:37:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/app/node_modules/express/lib/router/index.js:328:13)\n    at /app/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)","timestamp":"2025-03-04T20:10:37.876Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-04T21:00:00.938Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-04T21:00:00.941Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-03-04T21:00:00.942Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at exports.getLDAPStatus (/app/src/controllers/ldapController.js:18:43)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at exports.isAdmin (/app/src/middleware/auth.js:37:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/app/node_modules/express/lib/router/index.js:328:13)\n    at /app/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)","timestamp":"2025-03-04T21:30:44.840Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at exports.getLDAPStatus (/app/src/controllers/ldapController.js:18:43)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at exports.isAdmin (/app/src/middleware/auth.js:37:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/app/node_modules/express/lib/router/index.js:328:13)\n    at /app/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)","timestamp":"2025-03-04T21:30:44.896Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at exports.getLDAPStatus (/app/src/controllers/ldapController.js:18:43)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at exports.isAdmin (/app/src/middleware/auth.js:37:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/app/node_modules/express/lib/router/index.js:328:13)\n    at /app/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)","timestamp":"2025-03-04T21:51:39.015Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at exports.getLDAPStatus (/app/src/controllers/ldapController.js:18:43)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at exports.isAdmin (/app/src/middleware/auth.js:37:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/app/node_modules/express/lib/router/index.js:328:13)\n    at /app/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)","timestamp":"2025-03-04T21:51:39.030Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-04T22:00:00.538Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-04T22:00:00.539Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-03-04T22:00:00.540Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-04T23:00:00.748Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-04T23:00:00.749Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-03-04T23:00:00.749Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-05T00:00:00.435Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-05T00:00:00.436Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-03-05T00:00:00.436Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-05T01:00:00.097Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-05T01:00:00.099Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-03-05T01:00:00.100Z"}
{"level":"error","message":"Error al conectar con LDAP: options.url ^ options.socketPath (String) required","stack":"TypeError: options.url ^ options.socketPath (String) required\n    at Object.createClient (/app/node_modules/ldapjs/lib/client/index.js:12:92)\n    at LDAPService.connect (/app/src/services/ldapService.js:27:26)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-05T02:00:00.779Z"}
{"level":"error","message":"Error al buscar usuarios LDAP: Error al conectar con el servidor LDAP","stack":"Error: Error al conectar con el servidor LDAP\n    at LDAPService.connect (/app/src/services/ldapService.js:47:13)\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:54:20)\n    at LDAPService.syncUsers (/app/src/services/ldapService.js:91:36)\n    at Task._execution (/app/src/services/cronService.js:37:27)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-05T02:00:00.780Z"}
{"level":"error","message":"Error en sincronización LDAP: Error al buscar usuarios en LDAP","stack":"Error: Error al buscar usuarios en LDAP\n    at LDAPService.searchUsers (/app/src/services/ldapService.js:85:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async LDAPService.syncUsers (/app/src/services/ldapService.js:91:25)\n    at async Task._execution (/app/src/services/cronService.js:37:9)","timestamp":"2025-03-05T02:00:00.780Z"}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-03-11T04:00:00.215Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T04:00:00.222Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-03-11T06:00:00.726Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T06:00:00.736Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T07:00:00.739Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T08:00:00.584Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T09:00:00.474Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T10:00:00.272Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T11:00:00.253Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T12:00:00.275Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T13:00:00.200Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T14:00:00.153Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-03-11T15:00:00.213Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T15:00:00.222Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-03-11T17:00:00.549Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T17:00:00.558Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T18:00:00.213Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T19:00:00.789Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T20:00:00.345Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T21:00:00.026Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T22:00:00.766Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-11T23:00:00.336Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-12T00:00:00.968Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-12T01:00:00.681Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-03-12T02:00:00.288Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-04-05T21:00:00.541Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-05T21:00:00.552Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-04-05T22:00:00.490Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-05T22:00:00.500Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-04-06T01:00:00.170Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T01:00:00.183Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-04-06T02:00:00.690Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T02:00:00.699Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-04-06T04:00:01.024Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T04:00:01.044Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T05:00:01.001Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T06:00:00.772Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T07:00:00.599Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T08:00:00.507Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T09:00:00.232Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T10:00:01.000Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T11:00:00.866Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T12:00:00.767Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T13:00:00.884Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T14:00:00.812Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T15:00:00.706Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T16:00:00.686Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T17:00:00.651Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T18:00:00.631Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T19:00:00.680Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T20:00:00.575Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T21:00:00.397Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T22:00:00.058Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-06T23:00:00.904Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T00:00:00.654Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T01:00:00.848Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T02:00:00.564Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T03:00:00.346Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T04:00:00.083Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T05:00:00.072Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T06:00:00.063Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T07:00:00.143Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T08:00:00.233Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T09:00:00.983Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T10:00:00.763Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T11:00:00.749Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T12:00:00.639Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T13:00:00.640Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T14:00:00.614Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T15:00:00.415Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T16:00:00.962Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T17:00:00.589Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T18:00:00.163Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T19:00:00.799Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T20:00:00.361Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T21:00:00.861Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T22:00:00.311Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-07T23:00:00.939Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T00:00:00.690Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T01:00:00.372Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T02:00:00.143Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T03:00:00.977Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T04:00:00.939Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T05:00:00.877Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T06:00:00.659Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T07:00:00.463Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T08:00:00.348Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T09:00:00.062Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T10:00:00.062Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T11:00:01.006Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T12:00:00.966Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T13:00:00.977Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T14:00:00.709Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T15:00:00.214Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T16:00:00.745Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T17:00:00.371Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T18:00:00.989Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T19:00:00.438Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T20:00:00.857Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T21:00:00.343Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T22:00:00.686Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-08T23:00:00.067Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T00:00:00.614Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T01:00:00.997Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T02:00:00.750Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T03:00:00.663Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T04:00:00.565Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T05:00:00.541Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T06:00:00.352Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T07:00:00.373Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T08:00:00.426Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T09:00:00.330Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T10:00:00.401Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T11:00:00.406Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T12:00:00.426Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T13:00:00.392Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T14:00:00.330Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T15:00:00.809Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T16:00:00.408Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T17:00:00.031Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T18:00:00.553Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T19:00:00.135Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T20:00:00.682Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T21:00:00.181Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-04-09T22:00:00.800Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-05-08T01:00:00.777Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-05-08T01:00:00.797Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-05-08T02:00:00.458Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-05-08T03:00:00.384Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-05-31T22:00:00.981Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-05-31T22:00:01.000Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-05-31T23:00:00.614Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T00:00:00.663Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T01:00:00.514Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-01T02:00:00.089Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T02:00:00.100Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-01T03:00:00.196Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T03:00:00.204Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-01T04:00:00.185Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T04:00:00.219Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-01T05:00:00.046Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T05:00:00.060Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-01T06:00:00.567Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T06:00:00.574Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-01T07:00:00.179Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T07:00:00.190Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-01T08:00:00.209Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T08:00:00.220Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-01T09:00:00.668Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T09:00:00.679Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T10:00:00.015Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T11:00:00.216Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T12:00:00.730Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T13:00:00.231Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T14:00:00.497Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T15:00:00.972Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T16:00:00.391Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T17:00:00.989Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T18:00:00.151Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-01T19:00:00.687Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T19:00:00.696Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T20:00:00.036Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T21:00:00.326Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-01T22:00:00.100Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T22:00:00.108Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-01T23:00:00.026Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-01T23:00:00.037Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-02T00:00:00.361Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T00:00:00.376Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-02T01:00:00.346Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T01:00:00.355Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T02:00:00.063Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T03:00:00.332Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T04:00:00.350Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T05:00:00.438Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T06:00:00.338Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T07:00:00.391Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T08:00:00.655Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T09:00:00.679Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T10:00:00.759Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T11:00:00.819Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T12:00:00.998Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T13:00:00.072Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T14:00:00.383Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T15:00:00.587Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T16:00:00.306Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T17:00:00.391Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T18:00:00.583Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T19:00:00.114Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T20:00:00.684Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T21:00:00.807Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T22:00:00.730Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-02T23:00:00.374Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T00:00:00.883Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T01:00:00.060Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T02:00:00.706Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T03:00:00.425Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-03T04:00:00.373Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T04:00:00.381Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-06-03T05:00:00.932Z"}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T05:00:00.940Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T06:00:00.977Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T07:00:00.813Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T08:00:00.229Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T09:00:00.567Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T10:00:00.845Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T11:00:00.077Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T12:00:00.519Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T13:00:00.782Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T14:00:00.052Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T15:00:00.888Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T16:00:00.519Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T17:00:00.880Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T18:00:00.915Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T19:00:00.679Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T20:00:00.410Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T21:00:00.702Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T22:00:00.504Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-03T23:00:00.357Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T00:00:00.120Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T01:00:00.658Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T02:00:00.463Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T03:00:00.378Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T04:00:00.086Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T05:00:00.697Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T06:00:00.452Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T07:00:00.132Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T08:00:00.830Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T09:00:00.624Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T10:00:00.493Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T11:00:00.462Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T12:00:00.358Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T13:00:00.242Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T14:00:00.902Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T15:00:00.969Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T16:00:00.527Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T17:00:00.398Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T18:00:00.444Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T19:00:00.452Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T20:00:00.824Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T21:00:00.309Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T22:00:00.557Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-04T23:00:00.530Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T00:00:00.383Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T01:00:01.009Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T02:00:00.662Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T03:00:00.565Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T04:00:00.450Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T05:00:00.310Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T06:00:00.261Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T07:00:00.051Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T08:00:00.868Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T09:00:00.735Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T10:00:00.719Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T11:00:00.661Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T12:00:00.514Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T13:00:00.375Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T14:00:00.275Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T15:00:00.380Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T16:00:00.524Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T17:00:00.156Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T18:00:00.266Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T19:00:00.158Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T20:00:00.294Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T21:00:00.590Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T22:00:00.323Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-05T23:00:00.371Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T00:00:00.504Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T01:00:00.242Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T02:00:00.145Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T03:00:00.134Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T04:00:00.138Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T05:00:00.160Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T06:00:00.136Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T07:00:00.117Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T08:00:00.148Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T09:00:00.119Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T10:00:00.088Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T11:00:00.085Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T12:00:00.118Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T13:00:00.121Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T14:00:00.036Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T15:00:00.700Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T16:00:00.832Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T17:00:00.888Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T18:00:00.061Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T19:00:00.836Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T20:00:00.041Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T21:00:00.888Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T22:00:00.313Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-06T23:00:00.897Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T00:00:00.969Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T01:00:00.379Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T02:00:00.212Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T03:00:00.968Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T04:00:00.746Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T05:00:00.525Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T06:00:00.272Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T07:00:00.102Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T08:00:00.931Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T09:00:00.688Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T10:00:00.355Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T11:00:00.094Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T12:00:00.928Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T13:00:00.795Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T14:00:00.684Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T15:00:00.461Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T16:00:00.239Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T17:00:00.042Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T18:00:00.839Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T19:00:00.546Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T20:00:00.341Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T21:00:00.153Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T22:00:00.016Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-07T23:00:00.791Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-08T00:00:00.515Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-08T01:00:00.617Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-08T02:00:00.256Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-08T03:00:00.083Z","updated":0}
{"created":0,"errors":0,"level":"info","message":"Sincronización LDAP completada","timestamp":"2025-06-08T04:00:00.880Z","updated":0}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-07-08T03:38:46.025Z"}
{"level":"info","message":"Conexión LDAP establecida exitosamente","timestamp":"2025-07-08T03:38:46.033Z"}
