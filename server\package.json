{"name": "worktrack-server", "version": "1.0.0", "description": "Backend para sistema de gestión de horas de trabajo", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"@prisma/client": "^6.4.1", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "exceljs": "^4.4.0", "express": "^4.18.2", "googleapis": "^129.0.0", "jsonwebtoken": "^9.0.0", "ldapjs": "^3.0.7", "ldapjs-promise": "^1.0.6", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.9", "pdfkit": "^0.14.0", "prisma": "^6.4.1", "socket.io": "^4.7.4", "winston": "^3.8.2"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22"}, "prisma": {"seed": "node prisma/seed.js"}}