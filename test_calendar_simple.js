// Script simple para probar las APIs del calendario
const axios = require('axios');

async function testCalendarAPIs() {
  console.log('🔍 Probando APIs del calendario...');
  
  try {
    // Probar conectividad básica del backend
    const healthResponse = await axios.get('http://localhost:5000/');
    console.log('✅ Backend responde:', healthResponse.data);
    
    // Probar con un token de ejemplo (necesitarás uno real)
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test';
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    const params = {
      startDate: '2025-01-13',
      endDate: '2025-01-19'
    };
    
    // Test APIs del calendario
    console.log('\n📅 Probando APIs del calendario...');
    
    try {
      const tasksResponse = await axios.get('http://localhost:5000/api/tasks/completed-by-date', {
        headers,
        params
      });
      console.log('✅ /api/tasks/completed-by-date - Status:', tasksResponse.status);
    } catch (error) {
      console.log('❌ /api/tasks/completed-by-date - Error:', error.response?.status, error.response?.data?.error || error.message);
    }
    
    try {
      const permissionsResponse = await axios.get('http://localhost:5000/api/permissions/approved-by-date', {
        headers,
        params
      });
      console.log('✅ /api/permissions/approved-by-date - Status:', permissionsResponse.status);
    } catch (error) {
      console.log('❌ /api/permissions/approved-by-date - Error:', error.response?.status, error.response?.data?.error || error.message);
    }
    
    try {
      const kanbanResponse = await axios.get('http://localhost:5000/api/tasks/archived-kanban-tasks', {
        headers,
        params
      });
      console.log('✅ /api/tasks/archived-kanban-tasks - Status:', kanbanResponse.status);
    } catch (error) {
      console.log('❌ /api/tasks/archived-kanban-tasks - Error:', error.response?.status, error.response?.data?.error || error.message);
    }
    
  } catch (error) {
    console.log('❌ Error general:', error.message);
  }
}

testCalendarAPIs();
