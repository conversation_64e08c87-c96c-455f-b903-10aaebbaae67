const { PrismaClient } = require('@prisma/client');
const axios = require('axios');
const crypto = require('crypto');
const apiExecutionService = require('../services/apiExecutionService_simple');

const prisma = new PrismaClient();

// Clave para encriptar API keys (en producción debería estar en variables de entorno)
const ENCRYPTION_KEY = process.env.API_KEY_ENCRYPTION_KEY || 'your-32-char-secret-key-here-123';

// Funciones de encriptación/desencriptación
const encrypt = (text) => {
  const cipher = crypto.createCipher('aes-256-cbc', ENCRYPTION_KEY);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
};

const decrypt = (encryptedText) => {
  const decipher = crypto.createDecipher('aes-256-cbc', ENCRYPTION_KEY);
  let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};

// Obtener todas las integraciones API
exports.getApiIntegrations = async (req, res) => {
  try {
    const integrations = await prisma.apiIntegration.findMany({
      include: {
        userApiKeys: {
          select: {
            id: true,
            userId: true,
            isActive: true,
            lastTested: true,
            testStatus: true,
            user: {
              select: {
                name: true,
                email: true
              }
            }
          }
        },
        _count: {
          select: {
            userApiKeys: true,
            tasks: true,
            importLogs: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.json(integrations);
  } catch (error) {
    console.error('Error al obtener integraciones API:', error);
    res.status(500).json({ error: 'Error al obtener las integraciones API' });
  }
};

// Crear nueva integración API
exports.createApiIntegration = async (req, res) => {
  try {
    const {
      name,
      description,
      baseUrl,
      authType,
      authConfig,
      endpoints
    } = req.body;

    const integration = await prisma.apiIntegration.create({
      data: {
        name,
        description,
        baseUrl,
        authType,
        authConfig,
        endpoints,
        createdBy: req.user.id
      }
    });

    res.status(201).json(integration);
  } catch (error) {
    console.error('Error al crear integración API:', error);
    res.status(500).json({ error: 'Error al crear la integración API' });
  }
};

// Actualizar integración API
exports.updateApiIntegration = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      baseUrl,
      authType,
      authConfig,
      endpoints,
      isActive
    } = req.body;

    const integration = await prisma.apiIntegration.update({
      where: { id },
      data: {
        name,
        description,
        baseUrl,
        authType,
        authConfig,
        endpoints,
        isActive
      }
    });

    res.json(integration);
  } catch (error) {
    console.error('Error al actualizar integración API:', error);
    res.status(500).json({ error: 'Error al actualizar la integración API' });
  }
};

// Eliminar integración API
exports.deleteApiIntegration = async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.apiIntegration.delete({
      where: { id }
    });

    res.json({ message: 'Integración API eliminada exitosamente' });
  } catch (error) {
    console.error('Error al eliminar integración API:', error);
    res.status(500).json({ error: 'Error al eliminar la integración API' });
  }
};

// Obtener API keys de usuarios para una integración
exports.getUserApiKeys = async (req, res) => {
  try {
    const { integrationId } = req.params;

    const users = await prisma.user.findMany({
      where: {
        isActive: true
      },
      select: {
        id: true,
        name: true,
        email: true,
        userApiKeys: {
          where: {
            integrationId
          },
          select: {
            id: true,
            isActive: true,
            lastTested: true,
            testStatus: true,
            testMessage: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    res.json(users);
  } catch (error) {
    console.error('Error al obtener API keys de usuarios:', error);
    res.status(500).json({ error: 'Error al obtener las API keys de usuarios' });
  }
};

// Configurar API key para un usuario
exports.setUserApiKey = async (req, res) => {
  try {
    const { integrationId, userId } = req.params;
    const { apiKey } = req.body;

    if (!apiKey || apiKey.trim() === '') {
      return res.status(400).json({ error: 'API key es requerida' });
    }

    // Encriptar la API key
    const encryptedApiKey = encrypt(apiKey);

    const userApiKey = await prisma.userApiKey.upsert({
      where: {
        userId_integrationId: {
          userId,
          integrationId
        }
      },
      update: {
        apiKey: encryptedApiKey,
        isActive: true,
        testStatus: null,
        testMessage: null
      },
      create: {
        userId,
        integrationId,
        apiKey: encryptedApiKey,
        isActive: true
      }
    });

    res.json({ 
      message: 'API key configurada exitosamente',
      id: userApiKey.id 
    });
  } catch (error) {
    console.error('Error al configurar API key:', error);
    res.status(500).json({ error: 'Error al configurar la API key' });
  }
};

// Probar API key de un usuario
exports.testUserApiKey = async (req, res) => {
  try {
    const { integrationId, userId } = req.params;

    // Obtener la integración y la API key del usuario
    const integration = await prisma.apiIntegration.findUnique({
      where: { id: integrationId }
    });

    const userApiKey = await prisma.userApiKey.findUnique({
      where: {
        userId_integrationId: {
          userId,
          integrationId
        }
      }
    });

    if (!integration || !userApiKey) {
      return res.status(404).json({ error: 'Integración o API key no encontrada' });
    }

    // Desencriptar la API key
    const decryptedApiKey = decrypt(userApiKey.apiKey);

    // Obtener información del usuario para variables dinámicas
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    // Intentar hacer una petición de prueba usando el primer endpoint
    const endpoints = integration.endpoints;
    if (!endpoints || endpoints.length === 0) {
      return res.status(400).json({ error: 'No hay endpoints configurados para probar' });
    }

    const testEndpoint = endpoints[0];
    const testResult = await apiExecutionService.executeRequest(
      integration,
      testEndpoint,
      decryptedApiKey,
      user,
      true // isTest = true
    );

    // Actualizar el estado del test
    await prisma.userApiKey.update({
      where: { id: userApiKey.id },
      data: {
        lastTested: new Date(),
        testStatus: testResult.success ? 'success' : 'error',
        testMessage: testResult.message
      }
    });

    res.json(testResult);
  } catch (error) {
    console.error('Error al probar API key:', error);
    
    // Actualizar el estado del test como error
    try {
      const userApiKey = await prisma.userApiKey.findUnique({
        where: {
          userId_integrationId: {
            userId: req.params.userId,
            integrationId: req.params.integrationId
          }
        }
      });

      if (userApiKey) {
        await prisma.userApiKey.update({
          where: { id: userApiKey.id },
          data: {
            lastTested: new Date(),
            testStatus: 'error',
            testMessage: error.message
          }
        });
      }
    } catch (updateError) {
      console.error('Error al actualizar estado de test:', updateError);
    }

    res.status(500).json({ 
      success: false,
      error: 'Error al probar la API key',
      message: error.message 
    });
  }
};

// Función auxiliar para ejecutar peticiones API (wrapper del servicio)
const executeApiRequest = async (integration, endpoint, apiKey, user, isTest = false) => {
  return await apiExecutionService.executeRequest(integration, endpoint, apiKey, user, isTest);
};

// Exportar todas las funciones del controlador
module.exports = {
  getApiIntegrations: exports.getApiIntegrations,
  createApiIntegration: exports.createApiIntegration,
  updateApiIntegration: exports.updateApiIntegration,
  deleteApiIntegration: exports.deleteApiIntegration,
  getUserApiKeys: exports.getUserApiKeys,
  setUserApiKey: exports.setUserApiKey,
  testUserApiKey: exports.testUserApiKey,
  executeApiRequest // Mantener para compatibilidad
};
