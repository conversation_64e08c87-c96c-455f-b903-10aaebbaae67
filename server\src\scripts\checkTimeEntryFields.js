const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkTimeEntryFields() {
  try {
    console.log('🔍 VERIFICANDO CAMPOS DE LA TABLA TimeEntry');
    console.log('='.repeat(50));

    // Obtener una entrada de tiempo para ver qué campos tiene
    const sampleEntry = await prisma.timeEntry.findFirst({
      where: {
        status: 'APPROVED'
      }
    });

    if (sampleEntry) {
      console.log('📊 Campos disponibles en TimeEntry:');
      Object.keys(sampleEntry).forEach(field => {
        console.log(`  - ${field}: ${typeof sampleEntry[field]} = ${sampleEntry[field]}`);
      });
    } else {
      console.log('❌ No se encontraron entradas de tiempo aprobadas');
    }

    console.log('\n🔍 VERIFICANDO ESQUEMA DE PRISMA');
    console.log('='.repeat(50));
    
    // Intentar hacer una consulta que incluya los campos que estamos buscando
    try {
      const testQuery = await prisma.timeEntry.findFirst({
        select: {
          id: true,
          title: true,
          hoursWorked: true,
          multipliedHours: true,
          // Intentar campos que podrían no existir
        }
      });
      console.log('✅ Campos básicos funcionan correctamente');
    } catch (error) {
      console.log('❌ Error en consulta básica:', error.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkTimeEntryFields();
