import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Grid,
  Chip,
  Divider,
  Card,
  CardContent,
  Stack,
  IconButton,
} from '@mui/material';
import {
  Close as CloseIcon,
  CalendarToday as CalendarIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  Description as DescriptionIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  HourglassEmpty as PendingIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import axios from 'axios';
import { API_URL } from '../config';

// Los tipos de permisos ahora se cargan dinámicamente desde Settings
// const permissionTypes = {}; // Removido - ahora se carga desde API

const PermissionDetailsModal = ({ open, onClose, permission, showEmployeeInfo = false }) => {
  const [workSchedule, setWorkSchedule] = useState({
    startTime: '09:00',
    endTime: '18:00',
    lunchBreak: '13:00',
    lunchDuration: 60,
  });

  // Estado para tipos de permisos dinámicos
  const [permissionTypes, setPermissionTypes] = useState([]);

  // Obtener configuración del horario laboral al abrir el modal
  useEffect(() => {
    if (open && permission) {
      fetchWorkSchedule();
      fetchPermissionTypes();
    }
  }, [open, permission]);

  const fetchWorkSchedule = async () => {
    try {
      const response = await axios.get(`${API_URL}/api/settings/work-schedules`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      if (response.data && response.data.length > 0) {
        const schedule = response.data[0];
        setWorkSchedule({
          startTime: schedule.startTime || '09:00',
          endTime: schedule.endTime || '18:00',
          lunchBreak: schedule.lunchBreak || '13:00',
          lunchDuration: schedule.lunchDuration || 60,
        });
      }
    } catch (error) {
      console.error('Error al obtener horario laboral:', error);
      // Mantener valores por defecto
    }
  };

  const fetchPermissionTypes = async () => {
    try {
      const response = await axios.get(`${API_URL}/api/settings/permission-types`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });

      // Convertir los tipos de la base de datos al formato esperado por el frontend
      const formattedTypes = response.data
        .filter(type => type.active) // Solo tipos activos
        .map(type => ({
          value: type.name, // Usar el nombre directamente como valor
          label: type.label,
          maxDaysPerYear: type.maxDaysPerYear,
          requiresApproval: type.requiresApproval
        }));

      setPermissionTypes(formattedTypes);
    } catch (error) {
      console.error('Error al obtener tipos de permisos:', error);
      // Usar tipos por defecto en caso de error
      setPermissionTypes([
        { value: 'Vacaciones', label: 'Vacaciones', maxDaysPerYear: 30, requiresApproval: true },
        { value: 'Enfermedad', label: 'Descanso Médico', maxDaysPerYear: 15, requiresApproval: false },
        { value: 'Personal', label: 'Permiso Personal', maxDaysPerYear: 10, requiresApproval: true },
        { value: 'Compensacion', label: '⏰ Compensación de Horas Extra', maxDaysPerYear: 0, requiresApproval: true },
        { value: 'Otros', label: 'Otro', maxDaysPerYear: 5, requiresApproval: true },
      ]);
    }
  };

  if (!permission) return null;

  // Utility functions
  const formatDateTime = (dateString) => {
    return format(new Date(dateString), "dd 'de' MMMM 'de' yyyy 'a las' HH:mm", { locale: es });
  };

  const formatDateOnly = (dateString) => {
    return format(new Date(dateString), "dd 'de' MMMM 'de' yyyy", { locale: es });
  };

  const formatTimeOnly = (dateString) => {
    return format(new Date(dateString), 'HH:mm', { locale: es });
  };

  const calculatePermissionHours = () => {
    const start = new Date(permission.startTime);
    const end = new Date(permission.endTime);

    // Si es un permiso de día completo, calcular usando la configuración del horario laboral
    if (isFullDayPermission()) {
      // Calcular horas de trabajo por día (descontando almuerzo)
      const [startHour, startMinute] = workSchedule.startTime.split(':').map(Number);
      const [endHour, endMinute] = workSchedule.endTime.split(':').map(Number);

      const startMinutes = startHour * 60 + startMinute;
      const endMinutes = endHour * 60 + endMinute;
      const totalMinutes = endMinutes - startMinutes;
      const totalHours = totalMinutes / 60;

      // Descontar tiempo de almuerzo
      const lunchHours = workSchedule.lunchDuration / 60;
      const workingHoursPerDay = totalHours - lunchHours;

      // Calcular días laborales
      const workingDays = calculateWorkingDays();

      return Math.max(0, workingHoursPerDay * workingDays);
    } else {
      // Para permisos por horas específicas, calcular la diferencia directa
      const diffInMs = end - start;
      const diffInHours = diffInMs / (1000 * 60 * 60);
      return Math.max(0, diffInHours);
    }
  };

  const calculateWorkingDays = () => {
    const start = new Date(permission.startTime);
    const end = new Date(permission.endTime);
    let workingDays = 0;
    
    const currentDate = new Date(start);
    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay();
      if (dayOfWeek >= 1 && dayOfWeek <= 5) {
        workingDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return workingDays;
  };

  const isFullDayPermission = () => {
    const startTime = formatTimeOnly(permission.startTime);
    const endTime = formatTimeOnly(permission.endTime);

    // Usar la configuración del horario laboral para determinar si es día completo
    return (startTime === workSchedule.startTime && endTime === workSchedule.endTime);
  };

  const getStatusIcon = () => {
    switch (permission.status) {
      case 'APPROVED':
        return <CheckCircleIcon color="success" />;
      case 'REJECTED':
        return <CancelIcon color="error" />;
      case 'PENDING':
        return <PendingIcon color="warning" />;
      default:
        return <PendingIcon />;
    }
  };

  const getStatusColor = () => {
    switch (permission.status) {
      case 'APPROVED':
        return 'success';
      case 'REJECTED':
        return 'error';
      case 'PENDING':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusLabel = () => {
    switch (permission.status) {
      case 'APPROVED':
        return 'Aprobado';
      case 'REJECTED':
        return 'Rechazado';
      case 'PENDING':
        return 'Pendiente';
      default:
        return 'Desconocido';
    }
  };

  const getPermissionTypeLabel = () => {
    const type = permissionTypes.find(t => t.value === permission.type);
    return type ? type.label : permission.type;
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
            📋 Detalles del Permiso
          </Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Employee Information (only for admin view) */}
          {showEmployeeInfo && permission.user && (
            <Grid item xs={12}>
              <Card sx={{ bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <PersonIcon color="primary" />
                    <Box>
                      <Typography variant="h6" color="primary">
                        {permission.user.name || permission.user.email}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {permission.user.email}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* Permission Type and Status */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                  Tipo de Permiso
                </Typography>
                <Chip
                  label={getPermissionTypeLabel()}
                  color={permission.type === 'Compensacion' ? 'secondary' : 'primary'}
                  variant="outlined"
                  size="medium"
                  sx={{ fontWeight: 'bold' }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                  Estado
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {getStatusIcon()}
                  <Chip
                    label={getStatusLabel()}
                    color={getStatusColor()}
                    variant="filled"
                    size="medium"
                    sx={{ fontWeight: 'bold' }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Date and Time Information */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CalendarIcon color="primary" />
                  Fechas y Horarios
                </Typography>
                <Divider sx={{ mb: 2 }} />
                
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      📅 Fecha de Inicio
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {formatDateOnly(permission.startTime)}
                    </Typography>
                    {!isFullDayPermission() && (
                      <Typography variant="body2" color="textSecondary">
                        🕐 {formatTimeOnly(permission.startTime)}
                      </Typography>
                    )}
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      📅 Fecha de Fin
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {formatDateOnly(permission.endTime)}
                    </Typography>
                    {!isFullDayPermission() && (
                      <Typography variant="body2" color="textSecondary">
                        🕐 {formatTimeOnly(permission.endTime)}
                      </Typography>
                    )}
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Duration Information */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TimeIcon color="primary" />
                  Duración del Permiso
                </Typography>
                <Divider sx={{ mb: 2 }} />
                
                <Stack direction="row" spacing={3} flexWrap="wrap">
                  <Box>
                    <Typography variant="subtitle2" color="textSecondary">
                      {isFullDayPermission() ? '⏰ Horas Netas (sin almuerzo)' : '⏰ Horas Totales'}
                    </Typography>
                    <Typography variant="h5" color="primary" fontWeight="bold">
                      {calculatePermissionHours().toFixed(1)}h
                    </Typography>
                    {isFullDayPermission() && (
                      <Typography variant="caption" color="textSecondary" display="block">
                        Horario: {workSchedule.startTime} - {workSchedule.endTime}<br />
                        Almuerzo: {workSchedule.lunchDuration}min descontados
                      </Typography>
                    )}
                  </Box>
                  
                  {isFullDayPermission() && (
                    <Box>
                      <Typography variant="subtitle2" color="textSecondary">
                        📅 Días Laborales
                      </Typography>
                      <Typography variant="h5" color="secondary" fontWeight="bold">
                        {calculateWorkingDays()} día{calculateWorkingDays() !== 1 ? 's' : ''}
                      </Typography>
                    </Box>
                  )}
                  
                  <Box>
                    <Typography variant="subtitle2" color="textSecondary">
                      📋 Tipo de Solicitud
                    </Typography>
                    <Chip
                      label={isFullDayPermission() ? 'Día Completo' : 'Horas Específicas'}
                      color={isFullDayPermission() ? 'info' : 'default'}
                      variant="outlined"
                      size="small"
                    />
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {/* Reason */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <DescriptionIcon color="primary" />
                  Motivo del Permiso
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {permission.reason || 'No se especificó motivo'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Timestamps */}
          <Grid item xs={12}>
            <Card sx={{ bgcolor: 'grey.50' }}>
              <CardContent>
                <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                  📊 Información del Sistema
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2">
                      <strong>Solicitud creada:</strong><br />
                      {formatDateTime(permission.createdAt)}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2">
                      <strong>Última actualización:</strong><br />
                      {formatDateTime(permission.updatedAt)}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} variant="contained" color="primary">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PermissionDetailsModal;
