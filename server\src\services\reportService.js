const PDFDocument = require('pdfkit');
const ExcelJS = require('exceljs');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { formatHoursAndMinutes } = require('../utils/timeFormatter');

class ReportService {
  // Método nuevo que usa datos ya procesados
  async generateTimeReportFromData(timeRecords, startDate, endDate, format = 'pdf') {
    try {
      if (format === 'pdf') {
        return this.generatePDFTimeReport(timeRecords, startDate, endDate);
      } else {
        return this.generateExcelTimeReport(timeRecords, startDate, endDate);
      }
    } catch (error) {
      console.error('Error generando reporte desde datos:', error);
      throw error;
    }
  }

  // Método original mantenido para compatibilidad
  async generateTimeReport(startDate, endDate, userId = null, format = 'pdf') {
    try {
      const timeEntries = await prisma.timeEntry.findMany({
        where: {
          startTime: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
          ...(userId && { userId }),
          status: 'APPROVED',
        },
        include: {
          user: true,
          task: true,
        },
        orderBy: {
          startTime: 'asc',
        },
      });

      if (format === 'pdf') {
        return this.generatePDFTimeReport(timeEntries, startDate, endDate);
      } else {
        return this.generateExcelTimeReport(timeEntries, startDate, endDate);
      }
    } catch (error) {
      console.error('Error generando reporte:', error);
      throw error;
    }
  }

  async generatePDFTimeReport(timeEntries, startDate, endDate) {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument();
        const chunks = [];

        doc.on('data', chunk => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));

        // Título y encabezado
        doc.fontSize(20).text('Reporte de Horas Trabajadas', { align: 'center' });
        doc.moveDown();
        doc.fontSize(12).text(`Período: ${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}`, { align: 'center' });
        doc.moveDown();
        doc.fontSize(10).text(`Total de registros: ${timeEntries.length}`, { align: 'center' });
        doc.moveDown();

        // Línea separadora
        doc.moveTo(50, 140)
           .lineTo(550, 140)
           .stroke();

        // Tabla de datos
        const tableTop = 160;
        let currentY = tableTop;

        // Encabezados de columna con mejor distribución
        doc.fontSize(10).fillColor('black')
           .text('Fecha', 50, currentY)
           .text('Usuario', 120, currentY)
           .text('Tarea', 200, currentY)
           .text('Horas', 380, currentY)
           .text('Tipo', 450, currentY);

        currentY += 15;

        // Línea separadora bajo encabezados
        doc.moveTo(50, currentY)
           .lineTo(550, currentY)
           .stroke();

        currentY += 10;

        // Datos
        let totalHours = 0;
        timeEntries.forEach(entry => {
          // Verificar si necesitamos nueva página (con más margen para texto multilínea)
          if (currentY > 680) {
            doc.addPage();
            currentY = 50;
            // Repetir encabezados en nueva página
            doc.fontSize(10).fillColor('black')
               .text('Fecha', 50, currentY)
               .text('Usuario', 120, currentY)
               .text('Tarea', 200, currentY)
               .text('Horas', 380, currentY)
               .text('Tipo', 450, currentY);
            currentY += 15;
            doc.moveTo(50, currentY).lineTo(550, currentY).stroke();
            currentY += 10;
          }

          const taskTitle = entry.task?.title || 'N/A';
          const userName = entry.user?.name || 'N/A';
          const startY = currentY;

          // Escribir campos que no cambian de altura
          doc.fontSize(9)
             .text(new Date(entry.startTime).toLocaleDateString(), 50, currentY)
             .text(userName.length > 15 ? userName.substring(0, 12) + '...' : userName, 120, currentY)
             .text(formatHoursAndMinutes(entry.hoursWorked), 380, currentY)
             .text(entry.type || 'Manual', 450, currentY);

          // Escribir tarea con salto de línea automático
          const taskHeight = doc.heightOfString(taskTitle, { width: 170 });
          doc.text(taskTitle, 200, currentY, {
            width: 170,
            lineGap: 2,
            continued: false
          });

          // Calcular la altura necesaria para esta fila
          const lineHeight = Math.max(15, taskHeight + 5);

          totalHours += entry.hoursWorked;
          currentY += lineHeight;
        });

        // Línea separadora antes del total
        currentY += 10;
        doc.moveTo(50, currentY)
           .lineTo(550, currentY)
           .stroke();

        // Total
        currentY += 15;
        doc.fontSize(12).fillColor('black')
           .text(`Total de Horas: ${formatHoursAndMinutes(totalHours)}`, 380, currentY)
           .text(`Total de Registros: ${timeEntries.length}`, 50, currentY);

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }

  async generateExcelTimeReport(timeEntries, startDate, endDate) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Reporte de Horas');

    // Estilo para encabezados
    const headerStyle = {
      font: { bold: true },
      fill: {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF9BC2E6' }
      }
    };

    // Definir columnas
    worksheet.columns = [
      { header: 'Fecha', key: 'date', width: 15 },
      { header: 'Usuario', key: 'user', width: 20 },
      { header: 'Tarea', key: 'task', width: 30 },
      { header: 'Descripción', key: 'description', width: 40 },
      { header: 'Horas', key: 'hours', width: 10 },
      { header: 'Tipo', key: 'type', width: 15 },
      { header: 'Horas Ponderadas', key: 'weightedHours', width: 15 }
    ];

    // Aplicar estilo a encabezados
    worksheet.getRow(1).eachCell(cell => {
      cell.style = headerStyle;
    });

    // Agregar datos
    timeEntries.forEach(entry => {
      const row = worksheet.addRow({
        date: new Date(entry.startTime).toLocaleDateString(),
        user: entry.user?.name || 'N/A',
        task: entry.task?.title || 'N/A',
        description: entry.description || '',
        hours: Number((entry.hoursWorked || 0).toFixed(2)),
        type: entry.type || 'Manual',
        weightedHours: Number((entry.multipliedHours || entry.hoursWorked || 0).toFixed(2))
      });

      // Formato para las columnas de horas
      row.getCell('hours').numFmt = '0.00';
      row.getCell('weightedHours').numFmt = '0.00';
    });

    // Agregar totales - Compatible con LibreOffice
    const totalRow = worksheet.rowCount + 2;
    worksheet.getCell(`D${totalRow}`).value = 'Total:';
    worksheet.getCell(`D${totalRow}`).font = { bold: true };

    // Calcular totales manualmente para evitar problemas de fórmulas
    let totalHours = 0;
    let totalWeightedHours = 0;

    timeEntries.forEach(entry => {
      totalHours += entry.hoursWorked || 0;
      totalWeightedHours += entry.multipliedHours || entry.hoursWorked || 0;
    });

    worksheet.getCell(`E${totalRow}`).value = Number(totalHours.toFixed(2));
    worksheet.getCell(`E${totalRow}`).font = { bold: true };
    worksheet.getCell(`E${totalRow}`).numFmt = '0.00';

    worksheet.getCell(`G${totalRow}`).value = Number(totalWeightedHours.toFixed(2));
    worksheet.getCell(`G${totalRow}`).font = { bold: true };
    worksheet.getCell(`G${totalRow}`).numFmt = '0.00';

    // Generar buffer
    return await workbook.xlsx.writeBuffer();
  }

  async generateKanbanReport(format = 'pdf') {
    try {
      const tasks = await prisma.task.findMany({
        include: {
          assignee: true,
          timeEntries: {
            where: {
              status: 'APPROVED'
            }
          },
          activeTimers: true
        },
        orderBy: {
          status: 'asc',
        },
      });

      if (format === 'pdf') {
        return this.generatePDFKanbanReport(tasks);
      } else {
        return this.generateExcelKanbanReport(tasks);
      }
    } catch (error) {
      console.error('Error generando reporte Kanban:', error);
      throw error;
    }
  }

  async generatePDFKanbanReport(tasks) {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument();
        const chunks = [];

        doc.on('data', chunk => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));

        // Título
        doc.fontSize(20).text('Reporte de Tablero Kanban', { align: 'center' });
        doc.moveDown();

        // Agrupar tareas por estado
        const tasksByStatus = tasks.reduce((acc, task) => {
          if (!acc[task.status]) acc[task.status] = [];
          acc[task.status].push(task);
          return acc;
        }, {});

        // Generar reporte por estado
        Object.entries(tasksByStatus).forEach(([status, statusTasks]) => {
          doc.addPage();
          doc.fontSize(16).text(status, { underline: true });
          doc.moveDown();

          statusTasks.forEach(task => {
            doc.fontSize(12).text(task.title, { bold: true });
            doc.fontSize(10)
               .text(`Asignado a: ${task.assignee?.name || 'No asignado'}`)
               .text(`Fecha límite: ${task.dueDate ? new Date(task.dueDate).toLocaleDateString() : 'No definida'}`)
               .text(`Horas estimadas: ${task.estimatedHours}`)
               .text(`Horas trabajadas: ${formatHoursAndMinutes(task.timeEntries.reduce((sum, entry) => sum + entry.hoursWorked, 0))}`);
            doc.moveDown();
          });
        });

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }

  async generateExcelKanbanReport(tasks) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Reporte Kanban');

    // Estilo para encabezados
    const headerStyle = {
      font: { bold: true },
      fill: {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF9BC2E6' }
      }
    };

    // Definir columnas
    worksheet.columns = [
      { header: 'ID', key: 'id', width: 10 },
      { header: 'Título', key: 'title', width: 30 },
      { header: 'Estado', key: 'status', width: 15 },
      { header: 'Asignado a', key: 'assignee', width: 20 },
      { header: 'Fecha Creación', key: 'createdAt', width: 15 },
      { header: 'Horas Registradas', key: 'hours', width: 15 }
    ];

    // Aplicar estilo a encabezados
    worksheet.getRow(1).eachCell(cell => {
      cell.style = headerStyle;
    });

    // Agregar datos
    tasks.forEach(task => {
      const totalHours = task.timeEntries.reduce((sum, entry) => sum + (entry.hoursWorked || 0), 0);
      
      worksheet.addRow({
        id: task.id,
        title: task.title,
        status: task.status,
        assignee: task.assignee?.name || 'Sin asignar',
        createdAt: new Date(task.createdAt).toLocaleDateString(),
        hours: formatHoursAndMinutes(totalHours)
      });
    });

    // Generar buffer
    return await workbook.xlsx.writeBuffer();
  }

  async generateStatusReport(startDate, endDate, format = 'pdf') {
    try {
      // Obtener registros de tiempo por estado
      const timeEntries = await prisma.timeEntry.findMany({
        where: {
          startTime: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        },
        select: {
          status: true,
        },
      });

      // Obtener permisos por estado
      const permissions = await prisma.permission.findMany({
        where: {
          startTime: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        },
        select: {
          status: true,
        },
      });

      // Contar por estado
      const statusCount = {
        PENDING: 0,
        APPROVED: 0,
        REJECTED: 0,
      };

      // Contar registros de tiempo
      timeEntries.forEach(entry => {
        statusCount[entry.status] = (statusCount[entry.status] || 0) + 1;
      });

      // Contar permisos
      permissions.forEach(permission => {
        statusCount[permission.status] = (statusCount[permission.status] || 0) + 1;
      });

      if (format === 'pdf') {
        return this.generatePDFStatusReport(statusCount, startDate, endDate);
      } else {
        return this.generateExcelStatusReport(statusCount, startDate, endDate);
      }
    } catch (error) {
      console.error('Error generando reporte de estados:', error);
      throw error;
    }
  }

  async generatePDFStatusReport(statusCount, startDate, endDate) {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument();
        const chunks = [];

        doc.on('data', chunk => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));

        // Título y encabezado
        doc.fontSize(20).text('Reporte de Estados de Aprobación', { align: 'center' });
        doc.moveDown();
        doc.fontSize(12).text(`Período: ${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}`);
        doc.moveDown();

        // Tabla de datos
        const tableTop = 150;
        let currentY = tableTop;

        // Encabezados de columna
        doc.fontSize(12)
           .text('Estado', 100, currentY)
           .text('Cantidad', 300, currentY);

        doc.moveTo(50, currentY - 10)
           .lineTo(500, currentY - 10)
           .lineTo(500, currentY + 10)
           .lineTo(50, currentY + 10)
           .lineTo(50, currentY - 10)
           .stroke();

        currentY += 30;

        // Datos
        const estados = {
          'PENDING': 'Pendiente',
          'APPROVED': 'Aprobado',
          'REJECTED': 'Rechazado'
        };

        Object.entries(statusCount).forEach(([status, count]) => {
          doc.fontSize(11)
             .text(estados[status], 100, currentY)
             .text(count.toString(), 300, currentY);

          doc.moveTo(50, currentY - 10)
             .lineTo(500, currentY - 10)
             .lineTo(500, currentY + 10)
             .lineTo(50, currentY + 10)
             .lineTo(50, currentY - 10)
             .stroke();

          currentY += 30;
        });

        // Total
        doc.moveDown()
           .fontSize(12)
           .text(`Total de Registros: ${Object.values(statusCount).reduce((a, b) => a + b, 0)}`, { align: 'right' });

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }

  async generateExcelStatusReport(statusCount, startDate, endDate) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Reporte de Estados');

    // Estilo para encabezados
    const headerStyle = {
      font: { bold: true },
      fill: {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF9BC2E6' }
      }
    };

    // Definir columnas
    worksheet.columns = [
      { header: 'Estado', key: 'status', width: 20 },
      { header: 'Cantidad', key: 'count', width: 15 }
    ];

    // Aplicar estilo a encabezados
    worksheet.getRow(1).eachCell(cell => {
      cell.style = headerStyle;
    });

    // Agregar datos
    const estados = {
      'PENDING': 'Pendiente',
      'APPROVED': 'Aprobado',
      'REJECTED': 'Rechazado'
    };

    Object.entries(statusCount).forEach(([status, count]) => {
      worksheet.addRow({
        status: estados[status],
        count: count
      });
    });

    // Agregar total - Compatible con LibreOffice
    const totalRow = worksheet.rowCount + 2;
    worksheet.getCell(`A${totalRow}`).value = 'Total';
    worksheet.getCell(`A${totalRow}`).font = { bold: true };

    // Calcular total manualmente
    const total = Object.values(statusCount).reduce((sum, count) => sum + count, 0);
    worksheet.getCell(`B${totalRow}`).value = total;
    worksheet.getCell(`B${totalRow}`).font = { bold: true };

    // Generar buffer
    return await workbook.xlsx.writeBuffer();
  }
}

module.exports = new ReportService();
