const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function generateKanbanData() {
  try {
    // Verificar si ya existen usuarios para usar como asignados y creadores
    let adminUser = await prisma.user.findFirst({ where: { role: 'ADMIN' } });
    let managerUser = await prisma.user.findFirst({ where: { role: 'MANAGER' } });
    let employees = await prisma.user.findMany({ where: { role: 'EMPLOYEE' }, take: 3 });
    
    // Si no hay suficientes usuarios, crear algunos nuevos
    if (!adminUser) {
      adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin User',
          password: await bcrypt.hash('admin123', 10),
          role: 'ADMIN'
        }
      });
    }
    
    if (!managerUser) {
      managerUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Manager User',
          password: await bcrypt.hash('manager123', 10),
          role: 'MANAGER'
        }
      });
    }
    
    if (employees.length < 3) {
      for (let i = employees.length + 1; i <= 3; i++) {
        employees.push(await prisma.user.create({
          data: {
            email: `employee${i}@test.com`,
            name: `Employee ${i}`,
            password: await bcrypt.hash('emp123', 10),
            role: 'EMPLOYEE'
          }
        }));
      }
    }
    
    // Crear tareas para cada estado del Kanban
    const statuses = ['TODO', 'IN_PROGRESS', 'REVIEW', 'ARCHIVED'];
    const priorities = ['LOW', 'MEDIUM', 'HIGH', 'URGENT'];
    const tags = [
      ['frontend', 'ui', 'design'],
      ['backend', 'api', 'database'],
      ['testing', 'qa', 'bugs'],
      ['documentation', 'research'],
      ['devops', 'deployment', 'infrastructure']
    ];
    
    // Crear múltiples tareas para cada estado
    for (const status of statuses) {
      // Crear entre 3 y 5 tareas para cada estado
      const taskCount = Math.floor(Math.random() * 3) + 3;
      
      for (let i = 0; i < taskCount; i++) {
        const priority = priorities[Math.floor(Math.random() * priorities.length)];
        const tagSet = tags[Math.floor(Math.random() * tags.length)];
        const assignee = employees[Math.floor(Math.random() * employees.length)];
        const estimatedHours = Math.floor(Math.random() * 20) + 4; // Entre 4 y 24 horas
        const actualHours = status === 'ARCHIVED' ? estimatedHours + (Math.random() > 0.5 ? 2 : -2) : null;
        
        // Crear la tarea
        const task = await prisma.task.create({
          data: {
            title: `${status} Task ${i + 1}: ${getTaskTitle(status, tagSet[0])}`,
            description: getTaskDescription(status, tagSet),
            status: status,
            estimatedHours: estimatedHours,
            actualHours: actualHours,
            priority: priority,
            dueDate: new Date(Date.now() + (Math.floor(Math.random() * 30) + 1) * 24 * 60 * 60 * 1000),
            tags: tagSet,
            assigneeId: assignee.id,
            creatorId: managerUser.id
          }
        });
        
        // Crear comentarios para la tarea
        const commentCount = Math.floor(Math.random() * 3) + 1;
        for (let j = 0; j < commentCount; j++) {
          await prisma.comment.create({
            data: {
              content: getRandomComment(status),
              taskId: task.id
            }
          });
        }
        
        // Crear adjuntos para algunas tareas
        if (Math.random() > 0.7) {
          await prisma.attachment.create({
            data: {
              name: getRandomAttachmentName(tagSet[0]),
              url: `https://example.com/files/${tagSet[0]}_doc_${i}.pdf`,
              type: 'application/pdf',
              size: Math.floor(Math.random() * 5 * 1024 * 1024), // Hasta 5MB
              taskId: task.id
            }
          });
        }
        
        // Crear entradas de tiempo para tareas en progreso o completadas
        if (status === 'IN_PROGRESS' || status === 'REVIEW' || status === 'ARCHIVED') {
          const entryCount = Math.floor(Math.random() * 3) + 1;
          
          for (let j = 0; j < entryCount; j++) {
            const hoursWorked = Math.floor(Math.random() * 4) + 1; // 1-4 horas por entrada
            const startTime = new Date(Date.now() - (Math.floor(Math.random() * 10) + 1) * 24 * 60 * 60 * 1000);
            const endTime = new Date(startTime.getTime() + hoursWorked * 60 * 60 * 1000);
            
            const timeEntry = await prisma.timeEntry.create({
              data: {
                title: `Trabajo en ${task.title}`,
                description: `Sesión de trabajo #${j + 1} para esta tarea`,
                startTime: startTime,
                endTime: endTime,
                hoursWorked: hoursWorked,
                status: status === 'ARCHIVED' ? 'APPROVED' : (Math.random() > 0.5 ? 'PENDING' : 'APPROVED'),
                type: Math.random() > 0.8 ? 'OVERTIME' : 'REGULAR',
                userId: assignee.id,
                taskId: task.id,
                approverId: status === 'ARCHIVED' || Math.random() > 0.5 ? managerUser.id : null,
                approvedAt: status === 'ARCHIVED' || Math.random() > 0.5 ? new Date() : null
              }
            });
            
            // Crear registros de auditoría para algunas entradas de tiempo
            if (status === 'ARCHIVED' || Math.random() > 0.7) {
              await prisma.auditLog.create({
                data: {
                  action: 'UPDATE',
                  entityType: 'TIME_ENTRY',
                  entityId: timeEntry.id,
                  oldValue: JSON.stringify({ status: 'PENDING' }),
                  newValue: JSON.stringify({ status: 'APPROVED' }),
                  timeEntryId: timeEntry.id
                }
              });
            }
          }
        }
      }
    }
    
    // Crear algunas notificaciones adicionales
    for (const employee of employees) {
      await prisma.notification.create({
        data: {
          type: 'TASK_ASSIGNED',
          title: 'Nueva tarea asignada',
          message: 'Se te ha asignado una nueva tarea de alta prioridad',
          userId: employee.id
        }
      });
    }
    
    console.log('¡Datos de Kanban generados exitosamente!');
  } catch (error) {
    console.error('Error generando datos de Kanban:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Funciones auxiliares para generar contenido aleatorio
function getTaskTitle(status, tag) {
  const titles = {
    'TODO': [
      `Implementar ${tag}`,
      `Diseñar nueva funcionalidad de ${tag}`,
      `Investigar soluciones para ${tag}`,
      `Planificar integración de ${tag}`
    ],
    'IN_PROGRESS': [
      `Desarrollo de ${tag} en curso`,
      `Trabajando en ${tag}`,
      `Implementación de ${tag}`,
      `Avanzando con ${tag}`
    ],
    'REVIEW': [
      `Revisión de ${tag}`,
      `Validación de ${tag}`,
      `Testing de ${tag}`,
      `QA para ${tag}`
    ],
    'ARCHIVED': [
      `${tag} completado`,
      `Finalización de ${tag}`,
      `${tag} implementado`,
      `${tag} entregado`
    ]
  };
  
  const options = titles[status] || titles['TODO'];
  return options[Math.floor(Math.random() * options.length)];
}

function getTaskDescription(status, tags) {
  const descriptions = [
    `Esta tarea implica trabajar con ${tags.join(', ')} para mejorar la funcionalidad existente.`,
    `Se requiere implementar nuevas características relacionadas con ${tags.join(', ')}.`,
    `Optimización y mejora de componentes de ${tags.join(', ')}.`,
    `Desarrollo y pruebas de funcionalidades de ${tags.join(', ')}.`,
    `Investigación e implementación de soluciones para ${tags.join(', ')}.`
  ];
  
  return descriptions[Math.floor(Math.random() * descriptions.length)];
}

function getRandomComment(status) {
  const comments = {
    'TODO': [
      'Deberíamos comenzar esto pronto.',
      '¿Alguien tiene experiencia previa con esto?',
      'Tengo algunas ideas para implementar esto.',
      'Necesitamos definir mejor los requisitos.'
    ],
    'IN_PROGRESS': [
      'Avanzando bien con esta tarea.',
      'Me encontré con algunos problemas, pero los estoy solucionando.',
      'Necesito ayuda con un aspecto específico.',
      'Estoy a mitad de camino aproximadamente.'
    ],
    'REVIEW': [
      'Por favor, revisen mi PR cuando tengan tiempo.',
      'He completado la implementación, necesito feedback.',
      'Todos los tests están pasando.',
      'Listo para revisión final.'
    ],
    'ARCHIVED': [
      'Tarea completada según los requisitos.',
      'Implementación finalizada y aprobada.',
      'Cerrado después de la validación final.',
      'Completado dentro del tiempo estimado.'
    ]
  };
  
  const options = comments[status] || comments['TODO'];
  return options[Math.floor(Math.random() * options.length)];
}

function getRandomAttachmentName(tag) {
  const names = [
    `${tag}_especificaciones.pdf`,
    `${tag}_documentacion.pdf`,
    `${tag}_diagrama.pdf`,
    `${tag}_manual.pdf`,
    `${tag}_guia_implementacion.pdf`
  ];
  
  return names[Math.floor(Math.random() * names.length)];
}

generateKanbanData();