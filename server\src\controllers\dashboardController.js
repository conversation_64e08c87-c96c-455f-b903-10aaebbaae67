const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Obtener resumen del dashboard
exports.getDashboardSummary = async (req, res) => {
  try {
    const userId = req.user.id;
    const today = new Date();
    const startOfWeek = new Date(today);
    // startOfWeek.setDate(today.getDate() - today.getDay()); // Original: Domingo como inicio
    const dayOfWeek = today.getDay(); // 0 para Domingo, 1 para Lunes, ..., 6 para Sábado
    const diffToMonday = (dayOfWeek === 0) ? 6 : (dayOfWeek - 1); // Días a restar para llegar al Lunes
    startOfWeek.setDate(today.getDate() - diffToMonday);
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6); // Lunes + 6 días = Domingo
    endOfWeek.setHours(23, 59, 59, 999);

    // Obtener horas trabajadas esta semana desde timeEntry
    const weeklyTimeEntries = await prisma.timeEntry.findMany({
      where: {
        userId,
        startTime: {
          gte: startOfWeek,
          lte: endOfWeek,
        },
        status: 'APPROVED',
      },
    });

    const weeklyHoursFromTimeEntries = weeklyTimeEntries.reduce((sum, entry) => sum + entry.hoursWorked, 0);
    const weeklyWeightedHours = weeklyTimeEntries.reduce((sum, entry) => sum + (entry.multipliedHours || entry.hoursWorked), 0);

    // Obtener horas trabajadas esta semana desde tareas archivadas
    const archivedTasks = await prisma.task.findMany({
      where: {
        assigneeId: userId,
        status: 'ARCHIVED',
        updatedAt: {
          gte: startOfWeek,
          lte: endOfWeek,
        }
      },
      select: {
        id: true,
        title: true,
        actualHours: true
      }
    });

    // Sumar las horas de tareas archivadas
    const weeklyHoursFromArchivedTasks = archivedTasks.reduce((sum, task) => {
      // Asegurarse de que actualHours sea un número
      const hours = parseFloat(task.actualHours || 0);
      return sum + hours;
    }, 0);

    // Sumar ambas fuentes de horas
    const weeklyHours = weeklyHoursFromTimeEntries + weeklyHoursFromArchivedTasks;

    // Obtener tareas pendientes
    const pendingTasks = await prisma.task.count({
      where: {
        assigneeId: userId,
        status: {
          in: ['TODO', 'IN_PROGRESS'],
        },
      },
    });

    // Obtener solicitudes pendientes (para administradores)
    let pendingApprovals = 0;
    if (req.user.role === 'ADMIN' || req.user.role === 'MANAGER') {
      const pendingTimeEntries = await prisma.timeEntry.count({
        where: {
          status: 'PENDING',
        },
      });

      const pendingPermissions = await prisma.permission.count({
        where: {
          status: 'PENDING',
        },
      });

      pendingApprovals = pendingTimeEntries + pendingPermissions;
    }

    // Obtener próximas tareas con fecha límite
    const upcomingTasks = await prisma.task.findMany({
      where: {
        assigneeId: userId,
        status: {
          not: 'ARCHIVED',
        },
        dueDate: {
          gte: today,
          lte: endOfWeek,
        },
      },
      orderBy: {
        dueDate: 'asc',
      },
      take: 5,
      include: {
        timeEntries: true,
      },
    });

    // Obtener distribución de estados de tareas
    const taskStatusDistribution = await prisma.task.groupBy({
      by: ['status'],
      where: {
        assigneeId: userId,
      },
      _count: true,
    });

    // Obtener notificaciones no leídas
    const unreadNotifications = await prisma.notification.count({
      where: {
        userId,
        read: false,
      },
    });

    // Obtener permisos recientes
    const recentPermissions = await prisma.permission.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 5,
    });

    // Calcular tiempo trabajado por tipo
    const timeByType = await prisma.timeEntry.groupBy({
      by: ['type'],
      where: {
        userId,
        startTime: {
          gte: startOfWeek,
          lte: endOfWeek,
        },
        status: 'APPROVED',
      },
      _sum: {
        hoursWorked: true,
        multipliedHours: true,
      },
    });

    res.json({
      weeklyHours,
      weeklyWeightedHours,
      weeklyHoursFromTimeEntries,
      weeklyHoursFromArchivedTasks,
      pendingTasks,
      pendingApprovals,
      upcomingTasks: upcomingTasks.map(task => ({
        ...task,
        actualHours: task.timeEntries.reduce((sum, entry) => sum + entry.hoursWorked, 0),
      })),
      taskStatusDistribution,
      unreadNotifications,
      recentPermissions,
      timeByType,
    });
  } catch (error) {
    console.error('Error al obtener resumen del dashboard:', error);
    res.status(500).json({ error: 'Error al obtener datos del dashboard' });
  }
};

// Obtener estadísticas detalladas
exports.getDashboardStats = async (req, res) => {
  try {
    const userId = req.user.id;
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    // Obtener estadísticas de tiempo por día del mes actual
    const dailyStats = await prisma.timeEntry.groupBy({
      by: ['startTime'],
      where: {
        userId,
        startTime: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
        status: 'APPROVED',
      },
      _sum: {
        hoursWorked: true,
        multipliedHours: true,
      },
    });

    // Obtener progreso de tareas del mes
    const taskProgress = await prisma.task.findMany({
      where: {
        assigneeId: userId,
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
      },
      include: {
        timeEntries: true,
      },
    });

    // Calcular eficiencia (horas estimadas vs reales)
    const taskEfficiency = taskProgress.map(task => ({
      id: task.id,
      title: task.title,
      estimatedHours: task.estimatedHours,
      actualHours: task.timeEntries.reduce((sum, entry) => sum + entry.hoursWorked, 0),
      efficiency: task.estimatedHours > 0
        ? (task.timeEntries.reduce((sum, entry) => sum + entry.hoursWorked, 0) / task.estimatedHours) * 100
        : 0,
    }));

    res.json({
      dailyStats,
      taskProgress,
      taskEfficiency,
    });
  } catch (error) {
    console.error('Error al obtener estadísticas del dashboard:', error);
    res.status(500).json({ error: 'Error al obtener estadísticas' });
  }
};

// Nueva función para obtener horas semanales de todos los usuarios (solo para administradores)
exports.getUsersWeeklyHours = async (req, res) => {
  try {
    // Verificar que el usuario sea administrador
    if (req.user.role !== 'ADMIN' && req.user.role !== 'MANAGER') {
      return res.status(403).json({ error: 'No autorizado para ver esta información' });
    }

    console.log('Processing request for users weekly hours from user:', req.user.id, req.user.role);

    // Obtener parámetros de fecha si se proporcionan
    const { startDate, endDate } = req.query;
    console.log('📅 PARÁMETROS DE FECHA RECIBIDOS:', { startDate, endDate });

    // Obtener todos los usuarios
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    console.log(`Found ${users.length} users`);

    // Calcular fechas para la semana seleccionada o actual
    let currentWeekStart, currentWeekEnd;

    if (startDate && endDate) {
      // Usar fechas proporcionadas
      currentWeekStart = new Date(startDate);
      currentWeekStart.setHours(0, 0, 0, 0);

      currentWeekEnd = new Date(endDate);
      currentWeekEnd.setHours(23, 59, 59, 999);

      console.log('📅 USANDO FECHAS PERSONALIZADAS:', {
        start: currentWeekStart.toISOString(),
        end: currentWeekEnd.toISOString()
      });
    } else {
      // Usar semana actual (lógica original)
      const today = new Date();

      // Semana actual
      currentWeekStart = new Date(today);
      // currentWeekStart.setDate(today.getDate() - today.getDay()); // Original: Domingo como inicio
      const day = currentWeekStart.getDay(); // 0 para Domingo, 1 para Lunes, etc.
      const diffToMonday = (day === 0) ? 6 : (day - 1); // Días a restar para llegar al Lunes
      currentWeekStart.setDate(currentWeekStart.getDate() - diffToMonday);
      currentWeekStart.setHours(0, 0, 0, 0);

      currentWeekEnd = new Date(currentWeekStart);
      currentWeekEnd.setDate(currentWeekStart.getDate() + 6); // Lunes + 6 días = Domingo
      currentWeekEnd.setHours(23, 59, 59, 999);

      console.log('📅 USANDO SEMANA ACTUAL:', {
        start: currentWeekStart.toISOString(),
        end: currentWeekEnd.toISOString()
      });
    }

    // Procesar cada usuario
    const usersWithHours = [];

    for (const user of users) {
      try {
        // Obtener horas de la semana actual
        const currentWeekEntries = await prisma.timeEntry.findMany({
          where: {
            userId: user.id,
            startTime: {
              gte: currentWeekStart,
              lte: currentWeekEnd,
            },
            status: 'APPROVED',
          },
          select: {
            hoursWorked: true,
          },
        });

        // Obtener horas de tareas archivadas para la semana actual
        const currentWeekArchivedTasks = await prisma.task.findMany({
          where: {
            assigneeId: user.id,
            status: 'ARCHIVED',
            updatedAt: {
              gte: currentWeekStart,
              lte: currentWeekEnd,
            }
          },
          select: {
            id: true,
            actualHours: true
          }
        });

        // Calcular horas de timeEntries
        const currentWeekHoursFromEntries = currentWeekEntries.reduce(
          (sum, entry) => sum + (parseFloat(entry.hoursWorked) || 0),
          0
        );

        // Calcular horas de tareas archivadas
        const currentWeekHoursFromArchived = currentWeekArchivedTasks.reduce(
          (sum, task) => sum + (parseFloat(task.actualHours) || 0),
          0
        );

        // Sumar ambas fuentes de horas
        const currentWeekHours = currentWeekHoursFromEntries + currentWeekHoursFromArchived;

        // Calcular horas para las 4 semanas anteriores
        const previousWeeks = [];

        for (let i = 1; i <= 4; i++) {
          const weekStart = new Date(currentWeekStart); // currentWeekStart ya es Lunes
          weekStart.setDate(weekStart.getDate() - (7 * i));

          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekEnd.getDate() + 6); // Lunes de semana anterior + 6 días = Domingo de semana anterior
          weekEnd.setHours(23, 59, 59, 999);

          // Obtener entradas de tiempo para esta semana
          const weekEntries = await prisma.timeEntry.findMany({
            where: {
              userId: user.id,
              startTime: {
                gte: weekStart,
                lte: weekEnd,
              },
              status: 'APPROVED',
            },
            select: {
              hoursWorked: true,
            },
          });

          // Obtener tareas archivadas para esta semana
          const weekArchivedTasks = await prisma.task.findMany({
            where: {
              assigneeId: user.id,
              status: 'ARCHIVED',
              updatedAt: {
                gte: weekStart,
                lte: weekEnd,
              }
            },
            select: {
              id: true,
              actualHours: true
            }
          });

          // Calcular horas de timeEntries
          const weekHoursFromEntries = weekEntries.reduce(
            (sum, entry) => sum + (parseFloat(entry.hoursWorked) || 0),
            0
          );

          // Calcular horas de tareas archivadas
          const weekHoursFromArchived = weekArchivedTasks.reduce(
            (sum, task) => sum + (parseFloat(task.actualHours) || 0),
            0
          );

          // Sumar ambas fuentes de horas
          const weekHours = weekHoursFromEntries + weekHoursFromArchived;

          // Formatear fechas para mostrar en formato DD/MM - DD/MM
          const formatDate = (date) => {
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            return `${day}/${month}`;
          };

          const weekStartFormatted = formatDate(weekStart);
          const weekEndFormatted = formatDate(weekEnd);

          previousWeeks.push({
            weekStart: `${weekStartFormatted} - ${weekEndFormatted}`,
            hours: weekHours.toFixed(1),
            hoursFromEntries: weekHoursFromEntries.toFixed(1),
            hoursFromArchived: weekHoursFromArchived.toFixed(1)
          });
        }

        // Formatear la semana actual de la misma manera
        const formatDate = (date) => {
          const day = date.getDate().toString().padStart(2, '0');
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          return `${day}/${month}`;
        };

        const currentWeekStartFormatted = formatDate(currentWeekStart);
        const currentWeekEndFormatted = formatDate(currentWeekEnd);

        usersWithHours.push({
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          currentWeekHours: currentWeekHours.toFixed(1),
          currentWeekRange: `${currentWeekStartFormatted} - ${currentWeekEndFormatted}`,
          currentWeekDetails: {
            fromEntries: currentWeekHoursFromEntries.toFixed(1),
            fromArchived: currentWeekHoursFromArchived.toFixed(1)
          },
          previousWeeks
        });
      } catch (userError) {
        console.error(`Error processing user ${user.id}:`, userError);
        // Continuar con el siguiente usuario en lugar de fallar toda la solicitud
        usersWithHours.push({
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          currentWeekHours: "0.0",
          currentWeekRange: "N/A",
          previousWeeks: [
            { weekStart: "N/A", hours: "0.0" },
            { weekStart: "N/A", hours: "0.0" },
            { weekStart: "N/A", hours: "0.0" },
            { weekStart: "N/A", hours: "0.0" }
          ],
          error: "Error al procesar datos del usuario"
        });
      }
    }

    console.log(`Returning data for ${usersWithHours.length} users with hours`);
    return res.json(usersWithHours);
  } catch (error) {
    console.error('Error al obtener horas semanales de usuarios:', error);
    return res.status(500).json({
      error: 'Error al obtener datos de usuarios',
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

// Obtener tabla de horas extra y compensaciones
exports.getOvertimeCompensationTable = async (req, res) => {
  try {
    console.log('🔍 CALCULANDO TABLA DE HORAS EXTRA Y COMPENSACIONES...');

    // PASO 1: Obtener parámetros de consulta
    const { startDate, endDate, userId } = req.query;

    // PASO 2: Control de acceso por rol
    const isAdminOrManager = req.user.role === 'ADMIN' || req.user.role === 'MANAGER';
    let targetUserId = null;
    let shouldShowAllUsers = false;

    if (isAdminOrManager) {
      if (!userId || userId === 'ALL') {
        shouldShowAllUsers = true;
        console.log('👨‍💼 ADMIN/MANAGER consultando TODOS los usuarios');
      } else {
        targetUserId = userId;
        shouldShowAllUsers = false;
        console.log(`👨‍💼 ADMIN/MANAGER consultando usuario: ${targetUserId}`);
      }
    } else {
      targetUserId = req.user.id;
      shouldShowAllUsers = false;
      console.log(`👤 USUARIO consultando solo sus datos: ${targetUserId}`);
    }

    // PASO 3: Calcular fechas de la semana
    let weekStart, weekEnd;

    if (startDate && endDate) {
      weekStart = new Date(startDate);
      weekStart.setHours(0, 0, 0, 0);
      weekEnd = new Date(endDate);
      weekEnd.setHours(23, 59, 59, 999);
      console.log('📅 USANDO FECHAS PERSONALIZADAS:', { start: startDate, end: endDate });
    } else {
      // Usar semana actual (Lunes a Domingo)
      const today = new Date();
      weekStart = new Date(today);
      const dayOfWeek = weekStart.getDay();
      const diffToMonday = (dayOfWeek === 0) ? 6 : (dayOfWeek - 1);
      weekStart.setDate(today.getDate() - diffToMonday);
      weekStart.setHours(0, 0, 0, 0);

      weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      weekEnd.setHours(23, 59, 59, 999);
      console.log('📅 USANDO SEMANA ACTUAL');
    }

    console.log(`📅 SEMANA: ${weekStart.toISOString().split('T')[0]} - ${weekEnd.toISOString().split('T')[0]}`);

    // PASO 4: Obtener usuarios (incluir todos los roles)
    let users;
    if (shouldShowAllUsers) {
      users = await prisma.user.findMany({
        select: { id: true, name: true, email: true, role: true }
      });
    } else {
      users = await prisma.user.findMany({
        where: {
          id: targetUserId
        },
        select: { id: true, name: true, email: true, role: true }
      });
    }

    console.log(`👥 USUARIOS ENCONTRADOS: ${users.length}`);

    // PASO 5: Obtener configuración de horarios estándar desde WorkSchedule
    const workSchedules = await prisma.workSchedule.findMany({
      orderBy: { dayOfWeek: 'asc' }
    });

    // PASO 5.1: Obtener multiplicadores de tiempo activos
    const timeMultipliers = await prisma.timeMultiplier.findMany({
      where: { isActive: true }
    });

    console.log('⏰ HORARIOS ESTÁNDAR CONFIGURADOS:', workSchedules.length > 0 ? 'Sí' : 'No');
    console.log('💰 MULTIPLICADORES ACTIVOS:', timeMultipliers.length);

    // Si no hay horarios configurados, usar valores por defecto
    let defaultSchedule = {
      startTime: '09:00',
      endTime: '18:00',
      lunchBreak: '13:00',
      lunchDuration: 60,
      workDays: [1, 2, 3, 4, 5], // Lunes a Viernes
      allowedOutsideHours: 8,
      assumedWeeklyHours: 40
    };

    if (workSchedules.length > 0) {
      // Usar el primer horario como referencia (asumiendo que todos son iguales)
      const template = workSchedules[0];
      defaultSchedule = {
        startTime: template.startTime,
        endTime: template.endTime,
        lunchBreak: template.lunchBreak,
        lunchDuration: template.lunchDuration,
        workDays: workSchedules.map(s => s.dayOfWeek),
        allowedOutsideHours: template.allowedOutsideHours || 8,
        assumedWeeklyHours: template.assumedWeeklyHours || 40
      };
    }

    console.log('⏰ HORARIOS APLICADOS:', {
      start: defaultSchedule.startTime,
      end: defaultSchedule.endTime,
      lunch: defaultSchedule.lunchBreak,
      lunchDuration: defaultSchedule.lunchDuration,
      workDays: defaultSchedule.workDays
    });

    // FUNCIÓN AUXILIAR: Calcular multiplicador aplicable para una entrada
    const calculateMultiplier = (entryStartTime, entryEndTime) => {
      // APLICAR ZONA HORARIA DE LIMA, PERÚ (UTC-5)
      const entryDateUTC = new Date(entryStartTime);
      const entryDate = new Date(entryDateUTC.getTime() - 5 * 60 * 60 * 1000);
      const dayOfWeek = entryDate.getDay(); // 0 = Domingo, 1 = Lunes, etc.

      const startHour = entryDate.getHours();
      const startMinute = entryDate.getMinutes();

      let applicableMultiplier = 1.0;
      let multiplierReasons = [];

      // 1. Verificar multiplicador por día de la semana
      const workSchedule = workSchedules.find(ws => ws.dayOfWeek === dayOfWeek);
      if (workSchedule && workSchedule.multiplier > 1.0) {
        applicableMultiplier = Math.max(applicableMultiplier, workSchedule.multiplier);
        const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
        multiplierReasons.push(`${dayNames[dayOfWeek]}: ${workSchedule.multiplier}x`);
      }

      // 1.1. NUEVO: Domingos automáticamente tienen multiplicador 2.0x (considerados feriados)
      if (dayOfWeek === 0) { // Domingo
        console.log(`🔥 DEBUG: Detectado DOMINGO para entrada ${entryStartTime}`);
        console.log(`🔥 DEBUG: Multiplicador antes: ${applicableMultiplier}`);
        applicableMultiplier = Math.max(applicableMultiplier, 2.0);
        console.log(`🔥 DEBUG: Multiplicador después: ${applicableMultiplier}`);
        multiplierReasons.push(`Domingo (considerado feriado): 2.0x`);
        console.log(`🔥 DEBUG: Razones: ${multiplierReasons.join(', ')}`);
      }

      // 2. Verificar multiplicadores por horario
      timeMultipliers.forEach(mult => {
        if (mult.startTime && mult.endTime) {
          const [multStartHour, multStartMin] = mult.startTime.split(':').map(Number);
          const [multEndHour, multEndMin] = mult.endTime.split(':').map(Number);

          const multStartMinutes = multStartHour * 60 + multStartMin;
          const multEndMinutes = multEndHour * 60 + multEndMin;
          const entryStartMinutes = startHour * 60 + startMinute;

          // Manejar horarios que cruzan medianoche (ej: 22:00 - 06:00)
          let isInTimeRange = false;
          if (multStartMinutes > multEndMinutes) {
            // Cruza medianoche
            isInTimeRange = (entryStartMinutes >= multStartMinutes) || (entryStartMinutes <= multEndMinutes);
          } else {
            // No cruza medianoche
            isInTimeRange = (entryStartMinutes >= multStartMinutes) && (entryStartMinutes <= multEndMinutes);
          }

          if (isInTimeRange) {
            applicableMultiplier = Math.max(applicableMultiplier, mult.value);
            multiplierReasons.push(`${mult.name} (${mult.startTime}-${mult.endTime}): ${mult.value}x`);
          }
        }
      });

      return { multiplier: applicableMultiplier, reasons: multiplierReasons };
    };

    // PASO 6: Procesar cada usuario - IMPLEMENTANDO COLUMNAS 1 Y 2
    const results = [];

    for (const user of users) {
      console.log(`📊 Procesando usuario: ${user.name}`);

      // Log especial para Ericsson
      if (user.name.toLowerCase().includes('ericsson')) {
        console.log(`🔍 ANÁLISIS DETALLADO PARA ERICSSON - Semana: ${weekStart.toISOString().split('T')[0]} a ${weekEnd.toISOString().split('T')[0]}`);
      }

      // COLUMNA 1: Calcular horas fuera de oficina (solo registros manuales)
      const manualTimeEntries = await prisma.timeEntry.findMany({
        where: {
          userId: user.id,
          startTime: { gte: weekStart, lte: weekEnd },
          status: 'APPROVED'
          // TODO: Agregar filtro para solo entradas manuales cuando tengamos ese campo
        }
      });

      console.log(`📝 Entradas de tiempo encontradas para ${user.name}:`, manualTimeEntries.length);

      let outsideOfficeHours = 0;
      let weightedHours1_5 = 0; // NUEVA COLUMNA 2: Horas × 1.5
      let weightedHours2_0 = 0; // NUEVA COLUMNA 3: Horas × 2.0

      for (const entry of manualTimeEntries) {
        // APLICAR ZONA HORARIA DE LIMA, PERÚ (UTC-5) como el resto del sistema
        const entryStartUTC = new Date(entry.startTime);
        const entryEndUTC = new Date(entry.endTime);

        // Convertir a hora peruana (UTC-5)
        const entryStart = new Date(entryStartUTC.getTime() - 5 * 60 * 60 * 1000);
        const entryEnd = new Date(entryEndUTC.getTime() - 5 * 60 * 60 * 1000);
        const entryDayOfWeek = entryStart.getDay(); // 0=Domingo, 1=Lunes, etc.

        console.log(`📅 Procesando entrada del ${entryStart.toISOString().split('T')[0]} (día ${entryDayOfWeek})`);

        // Log especial para Ericsson
        if (user.name.toLowerCase().includes('ericsson')) {
          console.log(`🔍 ERICSSON - Entrada: "${entry.title}"`);
          console.log(`🔍 ERICSSON - Horario: ${entryStart.toISOString()} a ${entryEnd.toISOString()}`);
          console.log(`🔍 ERICSSON - Horas trabajadas: ${entry.hoursWorked}h`);
          console.log(`🔍 ERICSSON - Estado: ${entry.status}`);
        }

        // TRATAMIENTO ESPECIAL PARA DOMINGO: Siempre se considera fuera de horario para multiplicador 2.0x
        if (entryDayOfWeek === 0) { // Domingo
          const totalHours = (entryEnd - entryStart) / (1000 * 60 * 60);
          outsideOfficeHours += totalHours;
          console.log(`🌅 DOMINGO DETECTADO: Todas las horas (${totalHours.toFixed(2)}h) se consideran fuera de horario para multiplicador 2.0x`);

          // CALCULAR HORAS PONDERADAS PARA DOMINGO
          const { multiplier, reasons } = calculateMultiplier(entry.startTime, entry.endTime);
          const hoursToMultiply = entry.hoursWorked; // DOMINGO: Usar TODAS las horas

          console.log(`🔥 DEBUG DOMINGO: Entrada "${entry.title}"`);
          console.log(`🔥 DEBUG DOMINGO: hoursToMultiply = ${hoursToMultiply}, multiplier = ${multiplier}`);
          console.log(`🔥 DEBUG DOMINGO: Razones = ${reasons.join(', ')}`);

          if (hoursToMultiply > 0 && multiplier >= 2.0) {
            const entryWeightedHours2_0 = hoursToMultiply * multiplier;
            weightedHours2_0 += entryWeightedHours2_0;
            console.log(`💎 DOMINGO - Entrada ${entry.id}: ${hoursToMultiply}h (todas las horas) × ${multiplier}x = ${entryWeightedHours2_0.toFixed(2)}h (Columna 2.0x)`);
            console.log(`📋 DOMINGO - Razones: ${reasons.join(', ')}`);
          } else if (hoursToMultiply > 0 && multiplier >= 1.5) {
            const entryWeightedHours1_5 = hoursToMultiply * multiplier;
            weightedHours1_5 += entryWeightedHours1_5;
            console.log(`💰 DOMINGO - Entrada ${entry.id}: ${hoursToMultiply}h (todas las horas) × ${multiplier}x = ${entryWeightedHours1_5.toFixed(2)}h (Columna 1.5x)`);
            console.log(`📋 DOMINGO - Razones: ${reasons.join(', ')}`);
          }

          continue;
        }

        // Verificar si es un día laborable (excluyendo domingo que ya se procesó arriba)
        if (!defaultSchedule.workDays.includes(entryDayOfWeek)) {
          // Si no es día laborable, TODA la entrada es fuera de horario
          const totalHours = (entryEnd - entryStart) / (1000 * 60 * 60);
          outsideOfficeHours += totalHours;

          const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
          console.log(`🚫 Día NO laborable (${dayNames[entryDayOfWeek]}): ${totalHours.toFixed(2)}h completamente fuera de horario`);

          continue;
        }

        // Es día laborable, calcular horas fuera del horario estándar
        const [workStartHour, workStartMin] = defaultSchedule.startTime.split(':').map(Number);
        const [workEndHour, workEndMin] = defaultSchedule.endTime.split(':').map(Number);
        const [lunchStartHour, lunchStartMin] = defaultSchedule.lunchBreak.split(':').map(Number);

        const workStartMinutes = workStartHour * 60 + workStartMin;
        const workEndMinutes = workEndHour * 60 + workEndMin;
        const lunchStartMinutes = lunchStartHour * 60 + lunchStartMin;
        const lunchEndMinutes = lunchStartMinutes + defaultSchedule.lunchDuration;

        const entryStartMinutes = entryStart.getHours() * 60 + entryStart.getMinutes();
        const entryEndMinutes = entryEnd.getHours() * 60 + entryEnd.getMinutes();

        let entryOutsideHours = 0;

        // 1. Horas antes del horario de trabajo
        if (entryStartMinutes < workStartMinutes) {
          const endBeforeWork = Math.min(entryEndMinutes, workStartMinutes);
          const hoursBeforeWork = Math.max(0, (endBeforeWork - entryStartMinutes) / 60);
          entryOutsideHours += hoursBeforeWork;
          console.log(`🌅 Antes del trabajo: ${hoursBeforeWork.toFixed(2)}h`);
        }

        // 2. Horas después del horario de trabajo
        if (entryEndMinutes > workEndMinutes) {
          const startAfterWork = Math.max(entryStartMinutes, workEndMinutes);
          const hoursAfterWork = Math.max(0, (entryEndMinutes - startAfterWork) / 60);
          entryOutsideHours += hoursAfterWork;
          console.log(`🌙 Después del trabajo: ${hoursAfterWork.toFixed(2)}h`);
        }

        // 3. Horas durante el almuerzo - REMOVIDO
        // Ya no consideramos el almuerzo como fuera de horario
        // El almuerzo es parte del horario laboral normal

        outsideOfficeHours += entryOutsideHours;

        // CALCULAR HORAS PONDERADAS SEPARADAS (COLUMNAS 2 Y 3)
        const { multiplier, reasons } = calculateMultiplier(entry.startTime, entry.endTime);

        // Determinar qué horas usar para el cálculo (domingo ya se procesó arriba)
        let hoursToMultiply = 0;
        if (!defaultSchedule.workDays.includes(entryDayOfWeek)) {
          // Día no laborable: aplicar multiplicador a TODAS las horas
          hoursToMultiply = entry.hoursWorked;
        } else if (entryOutsideHours > 0) {
          // Día laborable: aplicar multiplicador solo a las horas fuera de oficina
          hoursToMultiply = entryOutsideHours;
        }

        // Separar por tipo de multiplicador
        let entryWeightedHours1_5 = 0;
        let entryWeightedHours2_0 = 0;

        console.log(`🔥 DEBUG MULTIPLICADOR: Entrada "${entry.title}"`);
        console.log(`🔥 DEBUG: hoursToMultiply = ${hoursToMultiply}, multiplier = ${multiplier}`);
        console.log(`🔥 DEBUG: Día de semana = ${entryDayOfWeek}, Razones = ${reasons.join(', ')}`);

        if (hoursToMultiply > 0) {
          if (multiplier >= 2.0) {
            // Multiplicador 2.0 o mayor (Feriados, etc.)
            entryWeightedHours2_0 = hoursToMultiply * multiplier;
            console.log(`🔥 DEBUG: Asignado a columna 2.0x: ${entryWeightedHours2_0}h`);
          } else if (multiplier >= 1.5) {
            // Multiplicador 1.5 (Horas Extra, Nocturnas, etc.)
            entryWeightedHours1_5 = hoursToMultiply * multiplier;
            console.log(`🔥 DEBUG: Asignado a columna 1.5x: ${entryWeightedHours1_5}h`);
          }
          // Si multiplier < 1.5, no se agrega a ninguna columna ponderada
        } else {
          console.log(`🔥 DEBUG: hoursToMultiply = 0, no se aplica multiplicador`);
        }

        weightedHours1_5 += entryWeightedHours1_5;
        weightedHours2_0 += entryWeightedHours2_0;

        if (entryOutsideHours > 0) {
          console.log(`⏰ Entrada ${entry.id}: ${entryOutsideHours.toFixed(2)}h fuera de horario estándar`);

          // Log especial para Ericsson
          if (user.name.toLowerCase().includes('ericsson')) {
            console.log(`🔍 ERICSSON - Horas fuera de oficina para esta entrada: ${entryOutsideHours.toFixed(2)}h`);
          }
        } else {
          console.log(`✅ Entrada ${entry.id}: Completamente dentro del horario estándar`);
        }

        if (entryWeightedHours1_5 > 0 || entryWeightedHours2_0 > 0) {
          let baseHoursText;
          if (entryDayOfWeek === 0) {
            baseHoursText = `${entry.hoursWorked}h (domingo - todas las horas)`;
          } else if (!defaultSchedule.workDays.includes(entryDayOfWeek)) {
            baseHoursText = `${entry.hoursWorked}h (día no laborable)`;
          } else {
            baseHoursText = `${entryOutsideHours.toFixed(2)}h (fuera de oficina)`;
          }

          if (entryWeightedHours1_5 > 0) {
            console.log(`💰 Entrada ${entry.id}: ${baseHoursText} × ${multiplier}x = ${entryWeightedHours1_5.toFixed(2)}h (Columna 1.5x)`);
          }
          if (entryWeightedHours2_0 > 0) {
            console.log(`💎 Entrada ${entry.id}: ${baseHoursText} × ${multiplier}x = ${entryWeightedHours2_0.toFixed(2)}h (Columna 2.0x)`);
          }
          console.log(`📋 Razones: ${reasons.join(', ')}`);
        } else if (multiplier > 1.0) {
          console.log(`ℹ️ Entrada ${entry.id}: Multiplicador ${multiplier}x disponible pero no hay horas aplicables`);
        }
      }

      console.log(`📊 Total horas fuera de oficina para ${user.name}: ${outsideOfficeHours.toFixed(2)}h`);
      console.log(`💰 Total horas ponderadas 1.5x para ${user.name}: ${weightedHours1_5.toFixed(2)}h`);
      console.log(`💎 Total horas ponderadas 2.0x para ${user.name}: ${weightedHours2_0.toFixed(2)}h`);

      results.push({
        userId: user.id,
        userName: user.name,
        outsideOfficeHours: parseFloat(outsideOfficeHours.toFixed(2)), // COLUMNA 1: ✅ IMPLEMENTADA
        weightedHours1_5: parseFloat(weightedHours1_5.toFixed(2)),     // COLUMNA 2: ✅ IMPLEMENTADA (1.5x)
        weightedHours2_0: parseFloat(weightedHours2_0.toFixed(2)),     // COLUMNA 3: ✅ IMPLEMENTADA (2.0x)
        compensatedHours: 0,   // Columna 4: Por implementar
        balanceHours: 0,       // Columna 5: Por implementar
        // Removido: Sin límites de horas extra
        // exceededLimit: outsideOfficeHours > defaultSchedule.allowedOutsideHours,
        // allowedHours: defaultSchedule.allowedOutsideHours
      });
    }

    // PASO 6: Formatear fechas para mostrar
    const formatDate = (date) => {
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      return `${day}/${month}`;
    };

    const response = {
      users: results,
      weekRange: `${formatDate(weekStart)} - ${formatDate(weekEnd)}`,
      weekStart: weekStart.toISOString().split('T')[0],
      weekEnd: weekEnd.toISOString().split('T')[0],
      isAdminView: isAdminOrManager,
      summary: {
        totalUsers: users.length,
        // Removido: Sin límites de horas extra
        // usersExceedingLimit: results.filter(u => u.exceededLimit).length,
        usersWithBalance: results.filter(u => u.balanceHours > 0).length,
        totalOutsideHours: parseFloat(results.reduce((sum, u) => sum + u.outsideOfficeHours, 0).toFixed(2)),
        totalWeightedHours1_5: parseFloat(results.reduce((sum, u) => sum + u.weightedHours1_5, 0).toFixed(2)),
        totalWeightedHours2_0: parseFloat(results.reduce((sum, u) => sum + u.weightedHours2_0, 0).toFixed(2)),
        totalCompensatedHours: parseFloat(results.reduce((sum, u) => sum + u.compensatedHours, 0).toFixed(2)),
        totalBalanceHours: parseFloat(results.reduce((sum, u) => sum + u.balanceHours, 0).toFixed(2)),
        officeHours: {
          start: defaultSchedule.startTime,
          end: defaultSchedule.endTime,
          lunch: defaultSchedule.lunchBreak,
          lunchDuration: defaultSchedule.lunchDuration,
          workDays: defaultSchedule.workDays
        },
        // Removido: Sin límites de horas extra
        // allowedOutsideHours: defaultSchedule.allowedOutsideHours,
        assumedWeeklyHours: defaultSchedule.assumedWeeklyHours
      }
    };

    console.log('✅ TABLA BÁSICA CALCULADA');
    res.json(response);

  } catch (error) {
    console.error('❌ ERROR AL CALCULAR HORAS EXTRA:', error);
    res.status(500).json({ error: 'Error al calcular tabla de horas extra' });
  }
};

// Endpoint de prueba para verificar conectividad
exports.testOvertimeEndpoint = async (req, res) => {
  try {
    console.log('🧪 TEST ENDPOINT LLAMADO');
    res.json({
      message: '✅ Endpoint de horas extra funcionando correctamente',
      timestamp: new Date().toISOString(),
      user: {
        id: req.user.id,
        role: req.user.role,
        email: req.user.email
      }
    });
  } catch (error) {
    console.error('❌ ERROR EN TEST ENDPOINT:', error);
    res.status(500).json({ error: 'Error en endpoint de prueba' });
  }
};

// Endpoint de prueba específico para verificar acceso de usuarios no admin
exports.testUserAccess = async (req, res) => {
  try {
    console.log('🔍 VERIFICANDO ACCESO DE USUARIO:', {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role
    });

    const isAdminOrManager = req.user.role === 'ADMIN' || req.user.role === 'MANAGER';

    res.json({
      message: '✅ Usuario puede acceder al endpoint',
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.user.role,
        isAdminOrManager: isAdminOrManager,
        canViewAllUsers: isAdminOrManager,
        canViewOwnData: true
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ ERROR AL VERIFICAR ACCESO:', error);
    res.status(500).json({ error: 'Error al verificar acceso de usuario' });
  }
};
