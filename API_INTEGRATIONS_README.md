# 🔗 Integraciones API Dinámicas - Guía de Implementación

## 📋 Resumen de la Funcionalidad

Se ha implementado un sistema completo de integraciones API dinámicas que permite:

- **Configuración Visual**: Los administradores pueden configurar APIs externas desde la interfaz web
- **Gestión de API Keys**: Configuración de claves personales por usuario
- **Importación Automática**: Los usuarios pueden importar sus tickets con un solo click
- **Soporte Multi-Sistema**: Compatible con ManageEngine, Jira, ServiceNow, etc.

## 🏗️ Arquitectura Implementada

### Backend
- **Modelos de BD**: `ApiIntegration`, `UserApiKey`, `ApiImportLog`
- **Controladores**: `apiIntegrationController.js`, `apiImportController.js`
- **Servicios**: `apiExecutionService.js`
- **Rutas**: `/api/integrations/*`

### Frontend
- **Settings**: Nuevo tab "Integraciones API"
- **Componentes**: `ApiIntegrationsSettings`, `ApiBuilderDialog`, `UserApiKeysManagement`
- **Kanban**: Botón "Mis Tickets" para importación

## 🚀 Pasos para Completar la Implementación

### 1. Aplicar Migración de Base de Datos

```bash
# Opción A: Usando Docker (recomendado)
docker-compose exec db psql -U postgres -d worktrack -f /app/add_api_integrations_migration.sql

# Opción B: Usando Prisma (si funciona)
docker-compose exec backend npx prisma migrate dev --name add_api_integrations

# Opción C: Manualmente desde pgAdmin o cliente PostgreSQL
# Ejecutar el contenido de: server/add_api_integrations_migration.sql
```

### 2. Instalar Dependencias

```bash
# Instalar axios en el backend
docker-compose exec backend npm install axios

# O reconstruir los contenedores
docker-compose down
docker-compose up --build
```

### 3. Restaurar Backup de Datos

```bash
# Desde el menú Backup en la aplicación web
# 1. Ir a Settings → Backup
# 2. Subir tu archivo de backup
# 3. Hacer click en "Restore"
```

### 4. Verificar Instalación

```bash
# Ejecutar script de pruebas
docker-compose exec backend node test_api_integrations.js
```

## 🔧 Configuración de ManageEngine (Ejemplo)

### 1. Configurar Integración (Admin)

1. Ir a **Settings → Integraciones API**
2. Click **"Nueva Integración"**
3. Completar wizard:

**Paso 1 - Información Básica:**
```
Nombre: ManageEngine ServiceDesk Plus
URL Base: https://tu-empresa.manageengine.com/api/v3
Tipo Auth: API Key
Headers: {"Content-Type": "application/json"}
```

**Paso 2 - Endpoints:**
```
Nombre: Obtener Tickets Asignados
Método: GET
Ruta: /requests
Menú: kanban
Query Params: {
  "input_data": {
    "list_info": {
      "row_count": 50,
      "search_criteria": [{
        "field": "technician.email_id",
        "condition": "is",
        "value": "{{USER_EMAIL}}"
      }]
    }
  }
}
```

**Paso 3 - Mapeo:**
```
Título: subject
Descripción: description
Estado: status.name
Prioridad: priority.name
```

**Paso 4 - Probar:**
- Ingresar API key de prueba
- Verificar conexión exitosa

### 2. Configurar API Keys por Usuario

1. Ir a **Settings → Integraciones API → API Keys por Usuario**
2. Para cada usuario, ingresar su API key personal de ManageEngine
3. Probar conexión

### 3. Uso por Usuario Final

1. Ir a **Kanban**
2. Click botón **"Mis Tickets"** (aparece solo si tiene APIs configuradas)
3. Los tickets se importan automáticamente a la columna "Por Hacer"

## 🔍 Estructura de Archivos Creados/Modificados

### Backend
```
server/
├── src/
│   ├── controllers/
│   │   ├── apiIntegrationController.js     [NUEVO]
│   │   └── apiImportController.js          [NUEVO]
│   ├── routes/
│   │   └── apiIntegrationRoutes.js         [NUEVO]
│   ├── services/
│   │   └── apiExecutionService.js          [NUEVO]
│   └── index.js                            [MODIFICADO]
├── prisma/
│   └── schema.prisma                       [MODIFICADO]
├── package.json                            [MODIFICADO]
├── add_api_integrations_migration.sql     [NUEVO]
└── test_api_integrations.js               [NUEVO]
```

### Frontend
```
client/src/
├── components/
│   ├── ApiIntegrationsSettings.js         [NUEVO]
│   ├── ApiBuilderDialog.js                [NUEVO]
│   └── UserApiKeysManagement.js           [NUEVO]
├── pages/
│   ├── Settings.js                        [MODIFICADO]
│   └── KanbanBoard.js                     [MODIFICADO]
```

### Configuración
```
docker-compose.yml                         [MODIFICADO]
```

## 🧪 Testing

### Pruebas Automáticas
```bash
# Ejecutar suite de pruebas
docker-compose exec backend node test_api_integrations.js
```

### Pruebas Manuales

1. **Configuración de Integración**:
   - Crear nueva integración desde Settings
   - Probar conexión con API key válida

2. **Gestión de API Keys**:
   - Configurar API keys para usuarios
   - Probar conexiones individuales

3. **Importación de Datos**:
   - Desde Kanban, click "Mis Tickets"
   - Verificar que se crean tareas en TODO

## 🔒 Seguridad

- **API Keys Encriptadas**: Todas las API keys se almacenan encriptadas
- **Validación de Entrada**: Validación completa de configuraciones
- **Logs de Auditoría**: Registro completo de todas las importaciones
- **Permisos por Rol**: Solo admins pueden configurar integraciones

## 🐛 Troubleshooting

### Error: "No hay integraciones configuradas"
- Verificar que el admin haya configurado al menos una integración
- Verificar que el usuario tenga API key configurada

### Error: "Error al conectar con la API"
- Verificar URL base de la integración
- Verificar API key del usuario
- Verificar conectividad de red

### Error: "No se encontraron tickets"
- Verificar filtros en la configuración del endpoint
- Verificar que el usuario tenga tickets asignados en el sistema externo

## 📈 Próximas Mejoras

- [ ] Soporte para OAuth2
- [ ] Sincronización bidireccional
- [ ] Programación automática de importaciones
- [ ] Soporte para webhooks
- [ ] Integración con más sistemas (Jira, ServiceNow, etc.)

## 🎯 Estado Actual

✅ **Completado**:
- Modelos de base de datos
- Controladores backend
- Rutas API
- Componentes frontend
- Interfaz de configuración
- Sistema de encriptación
- Validaciones
- Logs de auditoría

⏳ **Pendiente**:
- Aplicar migración de BD
- Instalar dependencias
- Pruebas end-to-end
- Configuración de ejemplo con ManageEngine

La implementación está **95% completa** y lista para pruebas.
