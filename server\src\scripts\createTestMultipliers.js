const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createTestMultipliers() {
  try {
    console.log('🔧 CREANDO MULTIPLICADORES DE PRUEBA...');

    // Limpiar multiplicadores existentes
    await prisma.timeMultiplier.deleteMany({});
    console.log('✅ Multiplicadores existentes eliminados');

    // Crear multiplicadores de prueba
    const multipliers = [
      {
        name: 'Horas Nocturnas',
        value: 1.5,
        startTime: '22:00',
        endTime: '06:00',
        description: 'Multiplicador para trabajo nocturno',
        isActive: true
      },
      {
        name: 'Horas de Madrugada',
        value: 1.75,
        startTime: '00:00',
        endTime: '05:00',
        description: 'Multiplicador para trabajo de madrugada',
        isActive: true
      },
      {
        name: 'Horas Extra',
        value: 1.5,
        description: 'Multiplicador para horas extra sin horario específico',
        isActive: true
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        value: 2.0,
        description: 'Multiplicador para días feriados',
        isActive: true
      }
    ];

    for (const mult of multipliers) {
      const created = await prisma.timeMultiplier.create({
        data: mult
      });
      console.log(`✅ Creado: ${created.name} - ${created.value}x`);
    }

    // Crear horarios de trabajo
    console.log('\n🕐 CREANDO HORARIOS DE TRABAJO...');
    
    // Limpiar horarios existentes
    await prisma.workSchedule.deleteMany({});
    console.log('✅ Horarios existentes eliminados');

    // Crear horarios para cada día de la semana
    const workSchedules = [
      { dayOfWeek: 1, startTime: '09:00', endTime: '18:00', lunchBreak: '13:00', lunchDuration: 60, multiplier: 1.0 }, // Lunes
      { dayOfWeek: 2, startTime: '09:00', endTime: '18:00', lunchBreak: '13:00', lunchDuration: 60, multiplier: 1.0 }, // Martes
      { dayOfWeek: 3, startTime: '09:00', endTime: '18:00', lunchBreak: '13:00', lunchDuration: 60, multiplier: 1.0 }, // Miércoles
      { dayOfWeek: 4, startTime: '09:00', endTime: '18:00', lunchBreak: '13:00', lunchDuration: 60, multiplier: 1.0 }, // Jueves
      { dayOfWeek: 5, startTime: '09:00', endTime: '18:00', lunchBreak: '13:00', lunchDuration: 60, multiplier: 1.0 }, // Viernes
      { dayOfWeek: 6, startTime: '09:00', endTime: '14:00', lunchBreak: '12:00', lunchDuration: 60, multiplier: 1.5 }, // Sábado
      { dayOfWeek: 0, startTime: '10:00', endTime: '15:00', lunchBreak: '12:30', lunchDuration: 60, multiplier: 2.0 }  // Domingo
    ];

    for (const schedule of workSchedules) {
      const created = await prisma.workSchedule.create({
        data: schedule
      });
      const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
      console.log(`✅ Creado horario: ${dayNames[created.dayOfWeek]} - ${created.startTime} a ${created.endTime} (${created.multiplier}x)`);
    }

    // Crear algunos feriados
    console.log('\n🎉 CREANDO FERIADOS...');
    
    // Limpiar feriados existentes
    await prisma.holiday.deleteMany({});
    console.log('✅ Feriados existentes eliminados');

    const holidays = [
      {
        name: 'Año Nuevo',
        date: new Date('2025-01-01'),
        multiplier: 2.5,
        isRecurring: true
      },
      {
        name: 'Día del Trabajo',
        date: new Date('2025-05-01'),
        multiplier: 2.0,
        isRecurring: true
      },
      {
        name: 'Fiestas Patrias',
        date: new Date('2025-07-28'),
        multiplier: 2.5,
        isRecurring: true
      },
      {
        name: 'Navidad',
        date: new Date('2025-12-25'),
        multiplier: 3.0,
        isRecurring: true
      }
    ];

    for (const holiday of holidays) {
      const created = await prisma.holiday.create({
        data: holiday
      });
      console.log(`✅ Creado feriado: ${created.name} - ${created.multiplier}x`);
    }

    console.log('\n🎯 RESUMEN DE CONFIGURACIONES CREADAS:');
    
    const totalMultipliers = await prisma.timeMultiplier.count();
    const totalSchedules = await prisma.workSchedule.count();
    const totalHolidays = await prisma.holiday.count();
    
    console.log(`📋 Multiplicadores de tiempo: ${totalMultipliers}`);
    console.log(`📅 Horarios de trabajo: ${totalSchedules}`);
    console.log(`🎉 Feriados: ${totalHolidays}`);
    
    console.log('\n✅ DATOS DE PRUEBA CREADOS EXITOSAMENTE!');

  } catch (error) {
    console.error('❌ Error al crear datos de prueba:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestMultipliers();
