const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Iniciando seed de la base de datos...');

  // Crear usuarios iniciales
  const adminPassword = await bcrypt.hash('admin123', 10);
  const userPassword = await bcrypt.hash('user123', 10);

  // Usuario administrador
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Administrador',
      password: adminPassword,
      role: 'ADMIN',
    },
  });

  // Usuario empleado
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Usuario Empleado',
      password: userPassword,
      role: 'EMPLOYEE',
    },
  });

  // Usuario manager
  const manager = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Manager',
      password: userPassword,
      role: 'MANAGER',
    },
  });

  // Crear horarios de trabajo (Lunes a Viernes)
  const workDays = [1, 2, 3, 4, 5]; // Lunes a Viernes
  
  for (const dayOfWeek of workDays) {
    await prisma.workSchedule.upsert({
      where: { 
        id: `schedule-${dayOfWeek}` 
      },
      update: {},
      create: {
        id: `schedule-${dayOfWeek}`,
        dayOfWeek,
        startTime: '09:00',
        endTime: '18:00',
        lunchBreak: '13:00',
        lunchDuration: 60,
        allowedOutsideHours: 8,
        assumedWeeklyHours: 40,
      },
    });
  }

  // Crear multiplicadores de tiempo
  await prisma.timeMultiplier.upsert({
    where: { id: 'night-hours' },
    update: {},
    create: {
      id: 'night-hours',
      name: 'Horas Nocturnas',
      value: 1.5,
      startTime: '22:00',
      endTime: '06:00',
      description: 'Multiplicador para horas nocturnas',
    },
  });

  await prisma.timeMultiplier.upsert({
    where: { id: 'overtime' },
    update: {},
    create: {
      id: 'overtime',
      name: 'Horas Extra',
      value: 1.5,
      description: 'Multiplicador para horas extra',
    },
  });

  console.log('✅ Seed completado exitosamente');
  console.log('👤 Usuarios creados:');
  console.log('   - <EMAIL> / admin123 (ADMIN)');
  console.log('   - <EMAIL> / user123 (MANAGER)');
  console.log('   - <EMAIL> / user123 (EMPLOYEE)');
  console.log('⏰ Horarios de trabajo: Lunes a Viernes 09:00-18:00');
  console.log('🔢 Multiplicadores de tiempo configurados');
}

main()
  .catch((e) => {
    console.error('❌ Error durante el seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
