FROM node:18-alpine

WORKDIR /app

# Cambiar el propietario del directorio de trabajo
RUN chown -R node:node /app

# Cambiar al usuario node
USER node

COPY --chown=node:node package*.json ./

# Configurar npm para usar HTTPS y agregar reintentos
RUN npm config set registry https://registry.npmjs.org/
RUN npm config set fetch-retry-maxtimeout 600000
RUN npm config set fetch-retry-mintimeout 10000
RUN npm config set fetch-retries 5

# Instalar dependencias con reintentos
RUN npm install --verbose || npm install --verbose || npm install --verbose

# Instalar la versión específica de date-fns
RUN npm uninstall date-fns && npm install date-fns@2.30.0

# Copiar el resto de los archivos como usuario node
COPY --chown=node:node . .

EXPOSE 3000

CMD ["npm", "start"]
