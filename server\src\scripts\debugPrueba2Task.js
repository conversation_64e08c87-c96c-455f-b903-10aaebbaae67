const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function debugPrueba2Task() {
  try {
    console.log('🔍 DEBUGGEANDO TAREA "prueba 2"');
    console.log('='.repeat(60));

    // 1. Bus<PERSON> la tarea "prueba21"
    const timeEntries = await prisma.timeEntry.findMany({
      where: {
        title: {
          contains: 'prueba21'
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`\n📝 ENTRADAS ENCONTRADAS: ${timeEntries.length}`);
    
    if (timeEntries.length === 0) {
      console.log('❌ No se encontró ninguna tarea con "prueba21"');
      return;
    }

    const entry = timeEntries[0]; // La más reciente
    console.log(`\n🎯 ANALIZANDO ENTRADA MÁS RECIENTE:`);
    console.log(`   ID: ${entry.id}`);
    console.log(`   Título: ${entry.title}`);
    console.log(`   Usuario: ${entry.user?.name || 'Desconocido'}`);
    console.log(`   Estado: ${entry.status}`);
    console.log(`   Inicio: ${entry.startTime}`);
    console.log(`   Fin: ${entry.endTime}`);
    console.log(`   Horas trabajadas: ${entry.hoursWorked}`);

    // 2. Obtener configuración de WorkSchedule
    const workSchedules = await prisma.workSchedule.findMany();
    console.log(`\n📅 HORARIOS CONFIGURADOS: ${workSchedules.length}`);

    // 3. Simular exactamente la lógica del taskController
    const entryDate = new Date(entry.startTime);
    const dayOfWeek = entryDate.getDay();
    
    console.log(`\n🗓️ ANÁLISIS DE FECHA:`);
    console.log(`   Fecha de entrada: ${entryDate.toISOString()}`);
    console.log(`   Día de la semana: ${dayOfWeek} (0=Domingo, 1=Lunes, etc.)`);
    console.log(`   Fecha local: ${entryDate.toLocaleString()}`);

    const workSchedule = workSchedules.find(ws => ws.dayOfWeek === dayOfWeek);
    
    if (!workSchedule) {
      console.log(`❌ NO se encontró horario para el día ${dayOfWeek}`);
      console.log(`✅ Por tanto, isOutsideOfficeHours = true`);
      return;
    }

    console.log(`\n⚙️ HORARIO PARA EL DÍA ${dayOfWeek}:`);
    console.log(`   Inicio: ${workSchedule.startTime}`);
    console.log(`   Fin: ${workSchedule.endTime}`);

    // 4. Calcular exactamente como en el código
    const officeStart = workSchedule.startTime.split(':');
    const officeEnd = workSchedule.endTime.split(':');
    const officeStartMinutes = parseInt(officeStart[0]) * 60 + parseInt(officeStart[1]);
    const officeEndMinutes = parseInt(officeEnd[0]) * 60 + parseInt(officeEnd[1]);
    const entryStartMinutes = entryDate.getHours() * 60 + entryDate.getMinutes();
    const entryEndMinutes = new Date(entry.endTime).getHours() * 60 + new Date(entry.endTime).getMinutes();

    console.log(`\n🧮 CÁLCULOS EN MINUTOS:`);
    console.log(`   Oficina inicia: ${officeStartMinutes} minutos (${workSchedule.startTime})`);
    console.log(`   Oficina termina: ${officeEndMinutes} minutos (${workSchedule.endTime})`);
    console.log(`   Entrada inicia: ${entryStartMinutes} minutos (${entryDate.getHours()}:${entryDate.getMinutes().toString().padStart(2, '0')})`);
    console.log(`   Entrada termina: ${entryEndMinutes} minutos (${new Date(entry.endTime).getHours()}:${new Date(entry.endTime).getMinutes().toString().padStart(2, '0')})`);

    // 5. Aplicar la lógica exacta del código
    const isOutsideOfficeHours = entryStartMinutes < officeStartMinutes || entryEndMinutes > officeEndMinutes;

    console.log(`\n🎯 RESULTADO DEL CÁLCULO:`);
    console.log(`   ¿Inicia antes? ${entryStartMinutes} < ${officeStartMinutes} = ${entryStartMinutes < officeStartMinutes}`);
    console.log(`   ¿Termina después? ${entryEndMinutes} > ${officeEndMinutes} = ${entryEndMinutes > officeEndMinutes}`);
    console.log(`   isOutsideOfficeHours = ${isOutsideOfficeHours ? '🔴 TRUE (ROJO)' : '🟢 FALSE (VERDE)'}`);

    // 6. Verificar zona horaria y conversión a hora peruana
    console.log(`\n🌍 INFORMACIÓN DE ZONA HORARIA:`);
    console.log(`   Entrada startTime original: ${entry.startTime}`);
    console.log(`   Entrada endTime original: ${entry.endTime}`);
    console.log(`   Fecha parseada: ${entryDate.toString()}`);
    console.log(`   UTC: ${entryDate.toUTCString()}`);
    console.log(`   ISO: ${entryDate.toISOString()}`);

    // Convertir a hora peruana (UTC-5)
    const peruStartTime = new Date(entryDate.getTime() - 5 * 60 * 60 * 1000);
    const peruEndTime = new Date(new Date(entry.endTime).getTime() - 5 * 60 * 60 * 1000);

    console.log(`\n🇵🇪 CONVERSIÓN A HORA PERUANA (UTC-5):`);
    console.log(`   Inicio en Perú: ${peruStartTime.toISOString()} (${peruStartTime.getHours()}:${peruStartTime.getMinutes().toString().padStart(2, '0')})`);
    console.log(`   Fin en Perú: ${peruEndTime.toISOString()} (${peruEndTime.getHours()}:${peruEndTime.getMinutes().toString().padStart(2, '0')})`);

    // Recalcular con hora peruana
    const peruStartMinutes = peruStartTime.getHours() * 60 + peruStartTime.getMinutes();
    const peruEndMinutes = peruEndTime.getHours() * 60 + peruEndTime.getMinutes();

    console.log(`\n🧮 RECÁLCULO CON HORA PERUANA:`);
    console.log(`   Entrada inicia (Perú): ${peruStartMinutes} minutos (${peruStartTime.getHours()}:${peruStartTime.getMinutes().toString().padStart(2, '0')})`);
    console.log(`   Entrada termina (Perú): ${peruEndMinutes} minutos (${peruEndTime.getHours()}:${peruEndTime.getMinutes().toString().padStart(2, '0')})`);

    const isOutsideOfficeHoursPeru = peruStartMinutes < officeStartMinutes || peruEndMinutes > officeEndMinutes;

    console.log(`\n🎯 RESULTADO CON HORA PERUANA:`);
    console.log(`   ¿Inicia antes? ${peruStartMinutes} < ${officeStartMinutes} = ${peruStartMinutes < officeStartMinutes}`);
    console.log(`   ¿Termina después? ${peruEndMinutes} > ${officeEndMinutes} = ${peruEndMinutes > officeEndMinutes}`);
    console.log(`   isOutsideOfficeHours (Perú) = ${isOutsideOfficeHoursPeru ? '🔴 TRUE (ROJO)' : '🟢 FALSE (VERDE)'}`);

    console.log(`\n📊 COMPARACIÓN:`);
    console.log(`   Con UTC: ${isOutsideOfficeHours ? '🔴 ROJO' : '🟢 VERDE'}`);
    console.log(`   Con Perú: ${isOutsideOfficeHoursPeru ? '🔴 ROJO' : '🟢 VERDE'}`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugPrueba2Task();
