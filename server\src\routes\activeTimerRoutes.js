const express = require('express');
const router = express.Router();
const activeTimerController = require('../controllers/activeTimerController');
const { authenticateToken } = require('../middleware/auth');

// Aplicar middleware de autenticación a todas las rutas
router.use(authenticateToken);

// Rutas para el contador de tiempo activo
router.post('/start', activeTimerController.startTimer);
router.post('/:taskId/pause', activeTimerController.pauseTimer);
router.post('/:taskId/stop', activeTimerController.stopTimer);
router.get('/:taskId/status', activeTimerController.getTimerStatus);

module.exports = router;