#!/bin/bash

echo "🔧 INICIANDO INSTALACIÓN DE DEPENDENCIAS..."

# Configurar npm
npm config set registry https://registry.npmjs.org/
npm config set fetch-retry-maxtimeout 600000
npm config set fetch-retry-mintimeout 10000
npm config set fetch-retries 5

# Limpiar caché
echo "🧹 Limpiando caché de npm..."
npm cache clean --force

# Eliminar instalaciones previas
echo "🗑️ Eliminando instalaciones previas..."
rm -rf node_modules package-lock.json

# Instalar dependencias
echo "📦 Instalando dependencias..."
npm install --verbose --no-optional --production=false --audit=false

# Verificar dependencias críticas
echo "✅ Verificando dependencias críticas..."

CRITICAL_DEPS=("multer" "@prisma/client" "express" "cors" "jsonwebtoken" "bcryptjs" "prisma" "socket.io")

for dep in "${CRITICAL_DEPS[@]}"; do
    echo "Verificando $dep..."
    if npm list "$dep" > /dev/null 2>&1; then
        echo "✅ $dep instalado correctamente"
    else
        echo "❌ ERROR: $dep no está instalado"
        echo "Intentando instalar $dep individualmente..."
        npm install "$dep" --verbose
        
        if npm list "$dep" > /dev/null 2>&1; then
            echo "✅ $dep instalado exitosamente en segundo intento"
        else
            echo "❌ FALLO CRÍTICO: No se pudo instalar $dep"
            exit 1
        fi
    fi
done

echo "🎉 TODAS LAS DEPENDENCIAS INSTALADAS CORRECTAMENTE"
echo "📋 Lista de dependencias instaladas:"
npm list --depth=0

echo "✅ INSTALACIÓN COMPLETADA"
