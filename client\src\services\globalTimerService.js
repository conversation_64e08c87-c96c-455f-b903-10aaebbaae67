import axios from 'axios';
import { API_URL } from '../config';

class GlobalTimerService {
  // Iniciar temporizador global
  async startTimer(taskId) {
    try {
      const response = await axios.post(
        `${API_URL}/api/global-timer/${taskId}/start`,
        {},
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error starting global timer:', error);
      throw error;
    }
  }

  // Pausar temporizador global
  async pauseTimer(taskId) {
    try {
      const response = await axios.post(
        `${API_URL}/api/global-timer/${taskId}/pause`,
        {},
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error pausing global timer:', error);
      throw error;
    }
  }

  // Obtener estado del temporizador global
  async getTimerStatus(taskId) {
    try {
      const response = await axios.get(
        `${API_URL}/api/global-timer/${taskId}/status`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error getting global timer status:', error);
      throw error;
    }
  }

  // Formatear tiempo en formato legible
  formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  }

  // Calcular tiempo transcurrido desde el inicio
  calculateElapsedTime(startTime, accumulatedTime) {
    if (!startTime) return accumulatedTime || 0;
    
    const now = new Date();
    const start = new Date(startTime);
    const elapsedSeconds = Math.floor((now - start) / 1000);
    
    return (accumulatedTime || 0) + elapsedSeconds;
  }
}

export default new GlobalTimerService();
