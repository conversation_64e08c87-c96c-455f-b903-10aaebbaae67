const express = require('express');
const router = express.Router();
const ldapController = require('../controllers/ldapController');
const { authenticateToken, isAdmin } = require('../middleware/auth');

// Todas las rutas requieren autenticación y ser administrador
router.use(authenticateToken);
router.use(isAdmin);

// Rutas LDAP
router.post('/sync', ldapController.syncUsers);
router.get('/status', ldapController.getLDAPStatus);
router.get('/config', ldapController.getLDAPConfig);
router.post('/config', ldapController.updateLDAPConfig);

module.exports = router;
