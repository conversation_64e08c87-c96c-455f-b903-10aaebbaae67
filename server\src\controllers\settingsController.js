const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Horarios de trabajo
const createWorkSchedule = async (req, res) => {
  try {
    const { startTime, endTime, lunchBreak, lunchDuration, workDays } = req.body;
    // Temporalmente comentado hasta resolver migración
    // const { allowedOutsideHours, assumedWeeklyHours } = req.body;

    // First delete existing work schedules if updating
    await prisma.workSchedule.deleteMany({});

    // Create a new work schedule for each day of the week
    const createdSchedules = [];

    // If workDays is provided, create a schedule for each day
    if (Array.isArray(workDays) && workDays.length > 0) {
      for (const day of workDays) {
        // Convert day name to number (0 = Sunday, 1 = Monday, etc.)
        const dayMap = {
          'Sunday': 0,
          'Monday': 1,
          'Tuesday': 2,
          'Wednesday': 3,
          'Thursday': 4,
          'Friday': 5,
          'Saturday': 6
        };

        const dayOfWeek = dayMap[day];

        if (dayOfWeek !== undefined) {
          const schedule = await prisma.workSchedule.create({
            data: {
              dayOfWeek,
              startTime,
              endTime,
              lunchBreak,
              lunchDuration
              // Temporalmente comentado hasta resolver migración
              // allowedOutsideHours: allowedOutsideHours || 8,
              // assumedWeeklyHours: assumedWeeklyHours || 40
            }
          });

          createdSchedules.push(schedule);
        }
      }
    }

    res.status(201).json(createdSchedules);
  } catch (error) {
    console.error('Error al crear horario de trabajo:', error);
    res.status(500).json({ error: 'Error al crear horario de trabajo' });
  }
};

const getWorkSchedules = async (req, res) => {
  try {
    const workSchedules = await prisma.workSchedule.findMany();

    // Group schedules by common properties
    if (workSchedules.length > 0) {
      // Get the first schedule as a template
      const template = workSchedules[0];

      // Map day numbers back to day names
      const dayMap = {
        0: 'Sunday',
        1: 'Monday',
        2: 'Tuesday',
        3: 'Wednesday',
        4: 'Thursday',
        5: 'Friday',
        6: 'Saturday'
      };

      // Extract work days from all schedules
      const workDays = workSchedules.map(s => dayMap[s.dayOfWeek]).filter(Boolean);

      // Return a single schedule object with workDays array
      const formattedSchedule = {
        id: template.id,
        startTime: template.startTime,
        endTime: template.endTime,
        lunchBreak: template.lunchBreak,
        lunchDuration: template.lunchDuration,
        // Temporalmente usar valores por defecto hasta resolver migración
        allowedOutsideHours: 8, // template.allowedOutsideHours || 8,
        assumedWeeklyHours: 40, // template.assumedWeeklyHours || 40,
        workDays
      };

      res.json([formattedSchedule]);
    } else {
      res.json([]);
    }
  } catch (error) {
    console.error('Error al obtener horarios de trabajo:', error);
    res.status(500).json({ error: 'Error al obtener horarios de trabajo' });
  }
};

// Multiplicadores de tiempo
const createTimeMultiplier = async (req, res) => {
  try {
    const { name, value, startTime, endTime, description, conditions } = req.body;

    console.log('📝 CREANDO MULTIPLICADOR:', { name, value, startTime, endTime, description });

    const multiplier = await prisma.timeMultiplier.create({
      data: {
        name,
        value: parseFloat(value),
        startTime,
        endTime,
        description,
        conditions,
        isActive: true
      }
    });

    console.log('✅ MULTIPLICADOR CREADO:', multiplier);
    res.status(201).json(multiplier);
  } catch (error) {
    console.error('Error al crear multiplicador:', error);
    res.status(500).json({ error: 'Error al crear multiplicador' });
  }
};

const getTimeMultipliers = async (req, res) => {
  try {
    console.log('📋 OBTENIENDO MULTIPLICADORES...');
    const multipliers = await prisma.timeMultiplier.findMany({
      orderBy: { createdAt: 'asc' }
    });
    console.log(`✅ MULTIPLICADORES ENCONTRADOS: ${multipliers.length}`);
    multipliers.forEach(mult => {
      console.log(`  - ${mult.name}: ${mult.value}x (${mult.startTime || 'N/A'} - ${mult.endTime || 'N/A'})`);
    });
    res.json(multipliers);
  } catch (error) {
    console.error('Error al obtener multiplicadores:', error);
    res.status(500).json({ error: 'Error al obtener multiplicadores' });
  }
};

const updateTimeMultiplier = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, value, startTime, endTime, description, conditions } = req.body;

    console.log('📝 ACTUALIZANDO MULTIPLICADOR:', { id, name, value, startTime, endTime, description });

    const multiplier = await prisma.timeMultiplier.update({
      where: { id },
      data: {
        name,
        value: parseFloat(value),
        startTime,
        endTime,
        description,
        conditions
      }
    });

    console.log('✅ MULTIPLICADOR ACTUALIZADO:', multiplier);
    res.json(multiplier);
  } catch (error) {
    console.error('Error al actualizar multiplicador:', error);
    res.status(500).json({ error: 'Error al actualizar multiplicador' });
  }
};

const deleteTimeMultiplier = async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.timeMultiplier.delete({
      where: { id }
    });

    res.status(204).send();
  } catch (error) {
    console.error('Error al eliminar multiplicador:', error);
    res.status(500).json({ error: 'Error al eliminar multiplicador' });
  }
};

// Tipos de permisos
const createPermissionType = async (req, res) => {
  try {
    const { name, label, maxDaysPerYear, requiresApproval, active } = req.body;

    const permissionType = await prisma.permissionTypeConfig.create({
      data: {
        name,
        label,
        maxDaysPerYear: maxDaysPerYear || 0,
        requiresApproval: requiresApproval !== undefined ? requiresApproval : true,
        active: active !== undefined ? active : true
      }
    });

    res.status(201).json(permissionType);
  } catch (error) {
    console.error('Error al crear tipo de permiso:', error);
    if (error.code === 'P2002') {
      res.status(400).json({ error: 'Ya existe un tipo de permiso con ese nombre' });
    } else {
      res.status(500).json({ error: 'Error al crear tipo de permiso' });
    }
  }
};

const getPermissionTypes = async (req, res) => {
  try {
    const permissionTypes = await prisma.permissionTypeConfig.findMany({
      orderBy: { name: 'asc' }
    });

    res.json(permissionTypes);
  } catch (error) {
    console.error('Error al obtener tipos de permisos:', error);
    res.status(500).json({ error: 'Error al obtener tipos de permisos' });
  }
};

const updatePermissionType = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, label, maxDaysPerYear, requiresApproval, active } = req.body;

    const permissionType = await prisma.permissionTypeConfig.update({
      where: { id },
      data: {
        name,
        label,
        maxDaysPerYear,
        requiresApproval,
        active
      }
    });

    res.json(permissionType);
  } catch (error) {
    console.error('Error al actualizar tipo de permiso:', error);
    if (error.code === 'P2002') {
      res.status(400).json({ error: 'Ya existe un tipo de permiso con ese nombre' });
    } else if (error.code === 'P2025') {
      res.status(404).json({ error: 'Tipo de permiso no encontrado' });
    } else {
      res.status(500).json({ error: 'Error al actualizar tipo de permiso' });
    }
  }
};

const deletePermissionType = async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.permissionTypeConfig.delete({
      where: { id }
    });

    res.status(204).send();
  } catch (error) {
    console.error('Error al eliminar tipo de permiso:', error);
    if (error.code === 'P2025') {
      res.status(404).json({ error: 'Tipo de permiso no encontrado' });
    } else {
      res.status(500).json({ error: 'Error al eliminar tipo de permiso' });
    }
  }
};

module.exports = {
  createWorkSchedule,
  getWorkSchedules,
  createTimeMultiplier,
  getTimeMultipliers,
  updateTimeMultiplier,
  deleteTimeMultiplier,
  createPermissionType,
  getPermissionTypes,
  updatePermissionType,
  deletePermissionType
};
