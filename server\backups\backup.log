{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-03-01T03:00:00.763Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at exports.createBackup (/app/src/controllers/backupController.js:5:40)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at exports.isAdmin (/app/src/middleware/auth.js:37:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/app/node_modules/express/lib/router/index.js:328:13)\n    at /app/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)","timestamp":"2025-03-04T21:30:40.308Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-04-07T03:00:00.337Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-04-08T03:00:00.973Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-04-09T03:00:00.654Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-04-10T03:00:00.554Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-04-11T03:00:00.669Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-04-12T03:00:00.344Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-04-13T03:00:00.030Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-04-14T03:00:00.778Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-04-15T03:00:00.477Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-04-16T03:00:00.896Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-04-17T03:00:00.454Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-05-08T03:00:00.372Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at exports.createBackup (/app/src/controllers/backupController.js:5:40)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at exports.isAdmin (/app/src/middleware/auth.js:37:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/app/node_modules/express/lib/router/index.js:328:13)\n    at /app/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)","timestamp":"2025-05-30T05:02:43.782Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:147:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:134:27)\n    at BackupService.createBackup (/app/src/services/backupService.js:41:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-05-31T03:00:00.607Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:224:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:140:29)\n    at BackupService.createBackup (/app/src/services/backupService.js:40:18)\n    at Task._execution (/app/src/services/cronService.js:26:29)\n    at Task.execute (/app/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/app/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/app/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:517:28)\n    at Timeout.matchTime [as _onTimeout] (/app/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-01T03:00:00.168Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:224:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:140:29)\n    at BackupService.createBackup (/app/src/services/backupService.js:40:18)\n    at exports.createBackup (/app/src/controllers/backupController.js:6:40)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at exports.isAdmin (/app/src/middleware/auth.js:37:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/app/node_modules/express/lib/router/index.js:328:13)\n    at /app/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)","timestamp":"2025-06-01T21:56:22.439Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:224:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:140:29)\n    at BackupService.createBackup (/app/src/services/backupService.js:40:18)\n    at exports.createBackup (/app/src/controllers/backupController.js:8:40)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at exports.isAdmin (/app/src/middleware/auth.js:37:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/app/node_modules/express/lib/router/index.js:328:13)\n    at /app/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)","timestamp":"2025-06-01T22:03:28.614Z"}
{"level":"error","message":"Error al crear backup: URL de base de datos inválida","stack":"Error: URL de base de datos inválida\n    at BackupService.parseDatabaseUrl (/app/src/services/backupService.js:224:13)\n    at BackupService.executeBackup (/app/src/services/backupService.js:140:29)\n    at BackupService.createBackup (/app/src/services/backupService.js:40:18)\n    at exports.createBackup (/app/src/controllers/backupController.js:8:40)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at exports.isAdmin (/app/src/middleware/auth.js:37:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/app/node_modules/express/lib/router/index.js:328:13)\n    at /app/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)","timestamp":"2025-06-01T22:04:55.983Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-06-01T22-07-42-426Z.sql","timestamp":"2025-06-01T22:07:44.730Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-06-02T03-00-00-299Z.sql","timestamp":"2025-06-02T03:00:01.160Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-06-03T03-00-00-379Z.sql","timestamp":"2025-06-03T03:00:00.817Z"}
{"level":"info","message":"Backup restaurado exitosamente: backup-2025-06-03T03-00-00-379Z.sql","timestamp":"2025-06-03T05:08:38.954Z"}
{"level":"info","message":"Backup restaurado exitosamente: backup-2025-06-03T03-00-00-379Z.sql","timestamp":"2025-06-03T05:11:57.113Z"}
{"level":"info","message":"Backup restaurado exitosamente: backup-2025-06-03T03-00-00-379Z.sql","timestamp":"2025-06-03T05:22:11.525Z"}
{"level":"info","message":"Backup restaurado exitosamente: backup-2025-06-03T03-00-00-379Z.sql","timestamp":"2025-06-03T05:22:36.541Z"}
{"level":"info","message":"Backup restaurado exitosamente: backup-2025-06-03T03-00-00-379Z.sql","timestamp":"2025-06-03T05:22:54.362Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-06-04T03-00-00-334Z.sql","timestamp":"2025-06-04T03:00:00.814Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-06-05T03-00-00-532Z.sql","timestamp":"2025-06-05T03:00:01.128Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-06-06T03-00-00-084Z.sql","timestamp":"2025-06-06T03:00:00.486Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-06-01T22-07-42-426Z.sql","timestamp":"2025-06-06T03:00:00.503Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-06-07T03-00-00-938Z.sql","timestamp":"2025-06-07T03:00:01.360Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-06-02T03-00-00-299Z.sql","timestamp":"2025-06-07T03:00:01.377Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-06-08T03-00-00-031Z.sql","timestamp":"2025-06-08T03:00:00.515Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-06-03T03-00-00-379Z.sql","timestamp":"2025-06-08T03:00:00.531Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-06-08T16-02-01-960Z.sql","timestamp":"2025-06-08T16:02:04.168Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-06-04T03-00-00-334Z.sql","timestamp":"2025-06-08T16:02:04.184Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-06-30T03-25-21-433Z.sql","timestamp":"2025-06-30T03:25:23.445Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-06-05T03-00-00-532Z.sql","timestamp":"2025-06-30T03:25:23.465Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-07-02T03-43-43-522Z.sql","timestamp":"2025-07-02T03:43:44.088Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-06-06T03-00-00-084Z.sql","timestamp":"2025-07-02T03:43:44.126Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-07-05T15-08-28-022Z.sql","timestamp":"2025-07-05T15:08:29.528Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-06-07T03-00-00-938Z.sql","timestamp":"2025-07-05T15:08:29.553Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-07-05T19-04-08-504Z.sql","timestamp":"2025-07-05T19:04:10.040Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-06-08T03-00-00-031Z.sql","timestamp":"2025-07-05T19:04:10.076Z"}
{"level":"info","message":"Backup restaurado exitosamente: backup-2025-07-05T19-04-08-504Z.sql","timestamp":"2025-07-06T00:06:45.259Z"}
{"level":"info","message":"Backup restaurado exitosamente: backup-2025-07-05T19-04-08-504Z.sql","timestamp":"2025-07-06T00:29:46.935Z"}
{"level":"info","message":"Backup restaurado exitosamente: backup-2025-07-05T19-04-08-504Z.sql","timestamp":"2025-07-06T00:35:43.326Z"}
{"level":"info","message":"Backup restaurado exitosamente: backup-2025-07-05T19-04-08-504Z.sql","timestamp":"2025-07-06T00:36:00.763Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-07-06T00-41-42-500Z.sql","timestamp":"2025-07-06T00:41:43.609Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-06-08T16-02-01-960Z.sql","timestamp":"2025-07-06T00:41:43.635Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-07-06T20-04-01-825Z.sql","timestamp":"2025-07-06T20:04:02.402Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-06-30T03-25-21-433Z.sql","timestamp":"2025-07-06T20:04:02.421Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-07-08T03-38-50-149Z.sql","timestamp":"2025-07-08T03:38:50.931Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-07-02T03-43-43-522Z.sql","timestamp":"2025-07-08T03:38:50.946Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-07-09T03-26-52-851Z.sql","timestamp":"2025-07-09T03:26:55.702Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-07-05T15-08-28-022Z.sql","timestamp":"2025-07-09T03:26:55.782Z"}
{"level":"info","message":"Backup restaurado exitosamente: backup-2025-07-09T03-26-52-851Z.sql","timestamp":"2025-07-09T04:50:28.889Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-07-10T05-44-14-759Z.sql","timestamp":"2025-07-10T05:44:15.248Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-07-05T19-04-08-504Z.sql","timestamp":"2025-07-10T05:44:15.266Z"}
{"level":"info","message":"Backup creado exitosamente: backup-2025-07-10T23-50-40-190Z.sql","timestamp":"2025-07-10T23:50:40.857Z"}
{"level":"info","message":"Backup antiguo eliminado: backup-2025-07-06T00-41-42-500Z.sql","timestamp":"2025-07-10T23:50:40.878Z"}
