const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixSundayMultiplier() {
  try {
    console.log('🔧 CONFIGURANDO MULTIPLICADORES PARA DOMINGO...\n');

    // 1. Crear o actualizar multiplicador de feriados
    const holidayMultiplier = await prisma.timeMultiplier.upsert({
      where: { 
        name: '<PERSON>riad<PERSON>'
      },
      update: {
        value: 2.0,
        description: 'Multiplicador para días feriados y domingos',
        isActive: true
      },
      create: {
        name: '<PERSON>riad<PERSON>',
        value: 2.0,
        description: 'Multiplicador para días feriados y domingos',
        isActive: true
      }
    });

    console.log(`✅ Multiplicador de feriados: ${holidayMultiplier.name} - ${holidayMultiplier.value}x`);

    // 2. Crear o actualizar multiplicador de horas nocturnas
    const nightMultiplier = await prisma.timeMultiplier.upsert({
      where: { 
        name: '<PERSON><PERSON> Nocturnas'
      },
      update: {
        value: 1.5,
        startTime: '22:00',
        endTime: '06:00',
        description: 'Multiplicador para horas nocturnas',
        isActive: true
      },
      create: {
        name: 'Horas Nocturnas',
        value: 1.5,
        startTime: '22:00',
        endTime: '06:00',
        description: 'Multiplicador para horas nocturnas',
        isActive: true
      }
    });

    console.log(`✅ Multiplicador nocturno: ${nightMultiplier.name} - ${nightMultiplier.value}x (${nightMultiplier.startTime}-${nightMultiplier.endTime})`);

    // 3. Crear o actualizar multiplicador de horas extra
    const overtimeMultiplier = await prisma.timeMultiplier.upsert({
      where: { 
        name: 'Horas Extra'
      },
      update: {
        value: 1.5,
        description: 'Multiplicador para horas extra',
        isActive: true
      },
      create: {
        name: 'Horas Extra',
        value: 1.5,
        description: 'Multiplicador para horas extra',
        isActive: true
      }
    });

    console.log(`✅ Multiplicador horas extra: ${overtimeMultiplier.name} - ${overtimeMultiplier.value}x`);

    // 4. Configurar horarios de trabajo para todos los días
    const workSchedules = [
      { dayOfWeek: 1, startTime: '09:00', endTime: '18:00', lunchBreak: '13:00', lunchDuration: 60, multiplier: 1.0 }, // Lunes
      { dayOfWeek: 2, startTime: '09:00', endTime: '18:00', lunchBreak: '13:00', lunchDuration: 60, multiplier: 1.0 }, // Martes
      { dayOfWeek: 3, startTime: '09:00', endTime: '18:00', lunchBreak: '13:00', lunchDuration: 60, multiplier: 1.0 }, // Miércoles
      { dayOfWeek: 4, startTime: '09:00', endTime: '18:00', lunchBreak: '13:00', lunchDuration: 60, multiplier: 1.0 }, // Jueves
      { dayOfWeek: 5, startTime: '09:00', endTime: '18:00', lunchBreak: '13:00', lunchDuration: 60, multiplier: 1.0 }, // Viernes
      { dayOfWeek: 6, startTime: '09:00', endTime: '14:00', lunchBreak: '12:00', lunchDuration: 60, multiplier: 1.5 }, // Sábado
      { dayOfWeek: 0, startTime: '10:00', endTime: '15:00', lunchBreak: '12:30', lunchDuration: 60, multiplier: 2.0 }  // Domingo
    ];

    const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];

    for (const schedule of workSchedules) {
      const existingSchedule = await prisma.workSchedule.findFirst({
        where: { dayOfWeek: schedule.dayOfWeek }
      });

      if (existingSchedule) {
        await prisma.workSchedule.update({
          where: { id: existingSchedule.id },
          data: schedule
        });
        console.log(`🔄 Actualizado horario: ${dayNames[schedule.dayOfWeek]} - ${schedule.startTime} a ${schedule.endTime} (${schedule.multiplier}x)`);
      } else {
        await prisma.workSchedule.create({
          data: {
            ...schedule,
            allowedOutsideHours: 8,
            assumedWeeklyHours: 40
          }
        });
        console.log(`✅ Creado horario: ${dayNames[schedule.dayOfWeek]} - ${schedule.startTime} a ${schedule.endTime} (${schedule.multiplier}x)`);
      }
    }

    // 5. Verificar configuración final
    console.log('\n📊 VERIFICANDO CONFIGURACIÓN FINAL...');
    
    const allMultipliers = await prisma.timeMultiplier.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    });

    console.log(`\n📈 MULTIPLICADORES ACTIVOS: ${allMultipliers.length}`);
    allMultipliers.forEach(mult => {
      console.log(`  - ${mult.name}: ${mult.value}x ${mult.startTime ? `(${mult.startTime}-${mult.endTime})` : '(sin horario específico)'}`);
    });

    const allSchedules = await prisma.workSchedule.findMany({
      orderBy: { dayOfWeek: 'asc' }
    });

    console.log(`\n📅 HORARIOS CONFIGURADOS: ${allSchedules.length}`);
    allSchedules.forEach(schedule => {
      console.log(`  - ${dayNames[schedule.dayOfWeek]}: ${schedule.startTime}-${schedule.endTime} (${schedule.multiplier}x)`);
    });

    console.log('\n✅ CONFIGURACIÓN DE DOMINGO COMPLETADA');
    console.log('🎯 El domingo ahora debería multiplicar las horas por 2.0x en el dashboard');

  } catch (error) {
    console.error('❌ ERROR AL CONFIGURAR MULTIPLICADORES:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  fixSundayMultiplier();
}

module.exports = { fixSundayMultiplier };
