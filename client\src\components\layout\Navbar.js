import React from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  IconButton,
  Box,
  ListItem,
  ListItemIcon,
  ListItemText,
  Link,
  MenuItem,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  ViewKanban as KanbanIcon,
  AccessTime as TimeIcon,
  EventNote as PermissionsIcon,
  Assessment as ReportsIcon,
  Settings as SettingsIcon,
  CheckCircle as ApprovalIcon,
  Backup as BackupIcon,
  Group as LDAPIcon,
  Logout as LogoutIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import Notifications from '../Notifications';

const Navbar = () => {
  const navigate = useNavigate();
  const userRole = localStorage.getItem('userRole');
  const isAdmin = userRole === 'ADMIN' || userRole === 'MANAGER';

  const handleLogout = () => {
    localStorage.clear();
    navigate('/login');
  };

  const handleMenuItemClick = (path) => {
    navigate(path);
  };

  return (
    <AppBar position="static">
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 0, mr: 4 }}>
          Area Ingenieria
        </Typography>
        <Box sx={{ flexGrow: 1, display: 'flex', gap: 2 }}>
          <Button
            color="inherit"
            component={RouterLink}
            to="/"
            startIcon={<DashboardIcon />}
          >
            Dashboard
          </Button>
          <Button
            color="inherit"
            component={RouterLink}
            to="/kanban"
            startIcon={<KanbanIcon />}
          >
            Kanban
          </Button>
          <Button
            color="inherit"
            component={RouterLink}
            to="/time-tracking"
            startIcon={<TimeIcon />}
          >
            Registro de Tiempo
          </Button>
          {isAdmin && (
            <Button
              color="inherit"
              component={RouterLink}
              to="/time-approval"
              startIcon={<ApprovalIcon />}
            >
              Aprobación
            </Button>
          )}
          {isAdmin && (
            <Button
              color="inherit"
              component={RouterLink}
              to="/permission-approval"
              startIcon={<PermissionsIcon />}
            >
              Aprobar Permisos
            </Button>
          )}
          <Button
            color="inherit"
            component={RouterLink}
            to="/permissions"
            startIcon={<PermissionsIcon />}
          >
            Permisos
          </Button>
          {isAdmin && (
            <Button
              color="inherit"
              component={RouterLink}
              to="/reports"
              startIcon={<ReportsIcon />}
            >
              Reportes
            </Button>
          )}
          {isAdmin && (
            <Button
              color="inherit"
              component={RouterLink}
              to="/backups"
              startIcon={<BackupIcon />}
            >
              Backups
            </Button>
          )}
          {isAdmin && (
            <Button
              color="inherit"
              component={RouterLink}
              to="/ldap-settings"
              startIcon={<LDAPIcon />}
            >
              LDAP
            </Button>
          )}
          <Button
            color="inherit"
            component={RouterLink}
            to="/completed"
            startIcon={<CheckCircleIcon />}
          >
            Tareas Completadas
          </Button>
        </Box>
        <Box sx={{ flexGrow: 0, display: 'flex', alignItems: 'center', gap: 2 }}>
          <Notifications />
          <Button
            color="inherit"
            onClick={handleLogout}
            startIcon={<LogoutIcon />}
          >
            Cerrar Sesión
          </Button>
          {isAdmin && (
            <IconButton
              color="inherit"
              component={RouterLink}
              to="/settings"
              sx={{ ml: 2 }}
            >
              <SettingsIcon />
            </IconButton>
          )}
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
