import React, { useState, useEffect } from 'react';
import { Paper, Typography, Alert, Button, Box } from '@mui/material';
import axios from 'axios';
import { API_URL } from '../config';

const TestOvertimeTable = () => {
  const [testResult, setTestResult] = useState(null);
  const [loading, setLoading] = useState(false);

  console.log('🧪 TestOvertimeTable renderizado correctamente');

  const testEndpoint = async () => {
    try {
      setLoading(true);
      console.log('🧪 Probando endpoint de horas extra...');

      const response = await axios.get(`${API_URL}/api/dashboard/test-overtime`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });

      console.log('✅ Respuesta del endpoint:', response.data);
      setTestResult(response.data);
    } catch (error) {
      console.error('❌ Error al probar endpoint:', error);
      setTestResult({ error: error.response?.data?.error || error.message });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testEndpoint();
  }, []);

  return (
    <Paper sx={{ p: 3, mb: 3, bgcolor: 'success.light' }}>
      <Typography variant="h5" gutterBottom>
        🧪 COMPONENTE DE PRUEBA - TABLA DE HORAS EXTRA
      </Typography>

      <Alert severity="success" sx={{ mb: 2 }}>
        ✅ Si puedes ver este mensaje, el componente se está renderizando correctamente.
      </Alert>

      <Box sx={{ mb: 2 }}>
        <Button
          variant="contained"
          onClick={testEndpoint}
          disabled={loading}
          sx={{ mr: 2 }}
        >
          {loading ? 'Probando...' : 'Probar Endpoint'}
        </Button>
      </Box>

      {testResult && (
        <Alert severity={testResult.error ? 'error' : 'info'} sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Resultado del test:</strong>
            <br />
            {testResult.error ? (
              `❌ Error: ${testResult.error}`
            ) : (
              <>
                ✅ {testResult.message}
                <br />
                👤 Usuario: {testResult.user?.email} ({testResult.user?.role})
                <br />
                🕐 Timestamp: {testResult.timestamp}
              </>
            )}
          </Typography>
        </Alert>
      )}

      <Typography variant="body1">
        Este es un componente de prueba para verificar que la tabla de horas extra se puede mostrar.
      </Typography>

      <Typography variant="body2" sx={{ mt: 2 }}>
        Próximos pasos:
        <br />• ✅ Verificar que el endpoint del backend funcione
        <br />• Verificar que los datos se obtengan correctamente
        <br />• Implementar la lógica completa de cálculo
      </Typography>
    </Paper>
  );
};

export default TestOvertimeTable;
