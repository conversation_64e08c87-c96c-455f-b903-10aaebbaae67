const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Feriados oficiales de Perú 2024-2025
const peruvianHolidays = [
  // 2024
  { name: 'Año Nuevo', date: '2024-01-01', multiplier: 2.0 },
  { name: '<PERSON><PERSON>', date: '2024-03-28', multiplier: 2.0 },
  { name: 'Viernes Santo', date: '2024-03-29', multiplier: 2.0 },
  { name: '<PERSON><PERSON> del Trabajo', date: '2024-05-01', multiplier: 2.0 },
  { name: 'Día de San Pedro y San Pablo', date: '2024-06-29', multiplier: 2.0 },
  { name: '<PERSON><PERSON>s Patrias - Día de la Independencia', date: '2024-07-28', multiplier: 2.0 },
  { name: '<PERSON><PERSON>s Patrias - Día de las Fuerzas Armadas', date: '2024-07-29', multiplier: 2.0 },
  { name: '<PERSON> Lima', date: '2024-08-30', multiplier: 2.0 },
  { name: '<PERSON><PERSON> de Angamos', date: '2024-10-08', multiplier: 2.0 },
  { name: '<PERSON><PERSON> de Todos los Santos', date: '2024-11-01', multiplier: 2.0 },
  { name: 'Inmaculada Concepción', date: '2024-12-08', multiplier: 2.0 },
  { name: 'Navidad', date: '2024-12-25', multiplier: 2.0 },

  // 2025
  { name: 'Año Nuevo', date: '2025-01-01', multiplier: 2.0 },
  { name: 'Jueves Santo', date: '2025-04-17', multiplier: 2.0 },
  { name: 'Viernes Santo', date: '2025-04-18', multiplier: 2.0 },
  { name: 'Día del Trabajo', date: '2025-05-01', multiplier: 2.0 },
  { name: 'Día de San Pedro y San Pablo', date: '2025-06-29', multiplier: 2.0 },
  { name: 'Fiestas Patrias - Día de la Independencia', date: '2025-07-28', multiplier: 2.0 },
  { name: 'Fiestas Patrias - Día de las Fuerzas Armadas', date: '2025-07-29', multiplier: 2.0 },
  { name: 'Santa Rosa de Lima', date: '2025-08-30', multiplier: 2.0 },
  { name: 'Combate de Angamos', date: '2025-10-08', multiplier: 2.0 },
  { name: 'Día de Todos los Santos', date: '2025-11-01', multiplier: 2.0 },
  { name: 'Inmaculada Concepción', date: '2025-12-08', multiplier: 2.0 },
  { name: 'Navidad', date: '2025-12-25', multiplier: 2.0 },

  // 2026 (algunos fijos)
  { name: 'Año Nuevo', date: '2026-01-01', multiplier: 2.0 },
  { name: 'Día del Trabajo', date: '2026-05-01', multiplier: 2.0 },
  { name: 'Día de San Pedro y San Pablo', date: '2026-06-29', multiplier: 2.0 },
  { name: 'Fiestas Patrias - Día de la Independencia', date: '2026-07-28', multiplier: 2.0 },
  { name: 'Fiestas Patrias - Día de las Fuerzas Armadas', date: '2026-07-29', multiplier: 2.0 },
  { name: 'Santa Rosa de Lima', date: '2026-08-30', multiplier: 2.0 },
  { name: 'Combate de Angamos', date: '2026-10-08', multiplier: 2.0 },
  { name: 'Día de Todos los Santos', date: '2026-11-01', multiplier: 2.0 },
  { name: 'Inmaculada Concepción', date: '2026-12-08', multiplier: 2.0 },
  { name: 'Navidad', date: '2026-12-25', multiplier: 2.0 }
];

async function updatePeruvianHolidays() {
  try {
    console.log('🇵🇪 ACTUALIZANDO FERIADOS OFICIALES DE PERÚ...');

    // Verificar la estructura de la tabla Holiday
    console.log('🔍 Verificando estructura de tabla Holiday...');

    const tableInfo = await prisma.$queryRaw`
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = 'Holiday' AND table_schema = 'public'
    `;

    console.log('📋 Columnas existentes:', tableInfo.map(col => col.column_name));

    // Verificar si existe la columna isActive
    const hasIsActive = tableInfo.some(col => col.column_name === 'isActive');

    if (!hasIsActive) {
      console.log('⚠️ Columna isActive no existe. Agregándola...');
      try {
        await prisma.$executeRaw`
          ALTER TABLE "Holiday" ADD COLUMN "isActive" BOOLEAN DEFAULT true;
        `;
        console.log('✅ Columna isActive agregada');
      } catch (error) {
        console.log('ℹ️ Continuando sin columna isActive...');
      }
    }

    // Limpiar feriados existentes (opcional)
    console.log('🧹 Limpiando feriados existentes...');
    await prisma.$executeRaw`DELETE FROM "Holiday"`;

    // Insertar feriados peruanos
    console.log('📅 Insertando feriados oficiales de Perú...');

    // Verificar nuevamente si isActive existe después de intentar agregarla
    const updatedTableInfo = await prisma.$queryRaw`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'Holiday' AND table_schema = 'public'
    `;
    const hasIsActiveNow = updatedTableInfo.some(col => col.column_name === 'isActive');

    for (const holiday of peruvianHolidays) {
      try {
        if (hasIsActiveNow) {
          // Insertar con todas las columnas necesarias
          await prisma.$executeRaw`
            INSERT INTO "Holiday" (name, date, multiplier, "isRecurring", "isActive", "updatedAt")
            VALUES (${holiday.name}, ${holiday.date}::date, ${holiday.multiplier}, false, true, CURRENT_TIMESTAMP)
          `;
        } else {
          // Insertar sin columna isActive
          await prisma.$executeRaw`
            INSERT INTO "Holiday" (name, date, multiplier, "isRecurring", "updatedAt")
            VALUES (${holiday.name}, ${holiday.date}::date, ${holiday.multiplier}, false, CURRENT_TIMESTAMP)
          `;
        }

        console.log(`✅ ${holiday.date}: ${holiday.name}`);
      } catch (error) {
        console.log(`❌ Error insertando ${holiday.name}: ${error.message}`);
      }
    }

    console.log(`🎉 ${peruvianHolidays.length} feriados oficiales de Perú insertados correctamente`);
    
    // Mostrar resumen
    const holidayCount = await prisma.$queryRaw`
      SELECT COUNT(*) as count FROM "Holiday"
    `;

    console.log(`📊 Total feriados insertados: ${holidayCount[0].count}`);

  } catch (error) {
    console.error('❌ Error al actualizar feriados:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  updatePeruvianHolidays();
}

module.exports = { updatePeruvianHolidays, peruvianHolidays };
