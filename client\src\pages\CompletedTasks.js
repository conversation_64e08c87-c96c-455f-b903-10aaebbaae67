import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Collapse
} from '@mui/material';
import { format, parseISO } from 'date-fns';
import { 
  Restore as RestoreIcon, 
  Visibility as VisibilityIcon,
  FilterList as FilterIcon,
  FileDownload as FileDownloadIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../config';

const CompletedTasks = () => {
  const [completedTasks, setCompletedTasks] = useState([]);
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentUser, setCurrentUser] = useState(null);
  const [downloadLoading, setDownloadLoading] = useState(false);
  
  // Estados para filtros (solo para administradores)
  const [assigneeFilter, setAssigneeFilter] = useState('');
  const [startDateFilter, setStartDateFilter] = useState('');
  const [endDateFilter, setEndDateFilter] = useState('');
  const [titleFilter, setTitleFilter] = useState(''); // Nuevo estado para filtro por título
  const [users, setUsers] = useState([]);
  const [showFilters, setShowFilters] = useState(false);
  const [sourceFilter, setSourceFilter] = useState(''); // Nuevo estado para filtro por tipo de tarea (manual o kanban)

  useEffect(() => {
    fetchCurrentUser();
  }, []);

  // Add effect to fetch tasks when user is loaded
  useEffect(() => {
    if (currentUser) {
      fetchCompletedTasks();
    }
  }, [currentUser]);

  // Modificar este efecto para cargar usuarios después de que las tareas estén disponibles
  useEffect(() => {
    if (completedTasks.length > 0 && currentUser?.role === 'ADMIN') {
      // Solo cargar usuarios si es administrador y hay tareas completadas
      fetchUsers();
      applyFilters();
    } else if (completedTasks.length > 0) {
      applyFilters();
    }
  }, [completedTasks, currentUser]);

  // Efecto para aplicar filtros cuando cambian
  useEffect(() => {
    if (completedTasks.length > 0) {
      applyFilters();
    }
  }, [assigneeFilter, startDateFilter, endDateFilter, titleFilter, sourceFilter]);

  const fetchCurrentUser = async () => {
    try {
      // Intentar obtener el usuario desde el perfil en lugar de /me
      const response = await axios.get(`${API_URL}/api/auth/profile`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      setCurrentUser(response.data);
    } catch (error) {
      console.error('Error al obtener información del usuario:', error);
      
      // Plan B: Intentar decodificar el token JWT para obtener el rol
      try {
        const token = localStorage.getItem('token');
        if (token) {
          // Decodificar el token JWT (que normalmente contiene información del usuario)
          const base64Url = token.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
          
          const decodedToken = JSON.parse(jsonPayload);
          setCurrentUser(decodedToken); // El token debería contener el rol del usuario
        }
      } catch (tokenError) {
        console.error('Error al decodificar el token:', tokenError);
      }
    }
  };

  // Función para obtener la lista de usuarios (solo para administradores)
  const fetchUsers = async () => {
    console.log('Intentando obtener usuarios...');
    
    // Primero intentar extraer usuarios de las tareas completadas
    if (completedTasks.length > 0) {
      console.log('Extrayendo usuarios de las tareas completadas primero');
      const uniqueUsers = {};
      
      // Extraer usuarios únicos de las tareas completadas
      completedTasks.forEach(task => {
        if (task.assignee) {
          const userId = task.assigneeId || `user-${task.assignee.replace(/\s+/g, '-').toLowerCase()}`;
          uniqueUsers[userId] = {
            id: userId,
            name: task.assignee
          };
        }
      });
      
      const extractedUsers = Object.values(uniqueUsers);
      console.log('Usuarios extraídos de tareas:', extractedUsers);
      
      if (extractedUsers.length > 0) {
        setUsers(extractedUsers);
        return; // Si encontramos usuarios en las tareas, no necesitamos consultar la API
      }
    }
    
    // Si no hay usuarios en las tareas, intentar con la API
    try {
      const response = await axios.get(`${API_URL}/api/users`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      console.log('Respuesta de API de usuarios:', response.data);
      
      if (Array.isArray(response.data) && response.data.length > 0) {
        setUsers(response.data);
      } else if (response.data.users && Array.isArray(response.data.users) && response.data.users.length > 0) {
        setUsers(response.data.users);
      } else {
        console.warn('La API no devolvió usuarios en un formato reconocible');
        setUsers([]); // Asegurarse de que users sea un array vacío si no hay datos
      }
    } catch (error) {
      console.error('Error al obtener usuarios de la API:', error);
      setUsers([]); // Asegurarse de que users sea un array vacío en caso de error
    }
  };
  
  const fetchCompletedTasks = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_URL}/api/kanban/tasks/archived`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      // Filter tasks based on user role
      let fetchedTasks = response.data;
      if (currentUser && currentUser.role !== 'ADMIN') {
        console.log('Current user:', currentUser);
        fetchedTasks = response.data.filter(task => {
          console.log('Task being checked:', task);
          return task.assigneeId === currentUser.id || 
                 task.assignedTo === currentUser.id;
        });
      }
      
      console.log('Filtered tasks:', fetchedTasks);
      console.log('Primera tarea con source:', fetchedTasks[0]);
      
      setCompletedTasks(fetchedTasks);
      setFilteredTasks(fetchedTasks);
      setError(null);
    } catch (error) {
      console.error('Error al obtener tareas completadas:', error);
      setError('Error al cargar las tareas completadas');
      setCompletedTasks([]);
      setFilteredTasks([]);
    } finally {
      setLoading(false);
    }
  };

  // Función para aplicar filtros (solo para administradores)
  const applyFilters = () => {
    // Si no es administrador, no aplicar filtros
    if (currentUser?.role !== 'ADMIN') {
      setFilteredTasks(completedTasks);
      return;
    }

    let result = [...completedTasks];
    
    // Filtrar por asignado
    if (assigneeFilter) {
      result = result.filter(task => task.assigneeId === assigneeFilter);
    }
    
    // Filtrar por título
    if (titleFilter) {
      const searchTerm = titleFilter.toLowerCase();
      result = result.filter(task => 
        task.title.toLowerCase().includes(searchTerm)
      );
    }
    
    // Filtrar por fuente (manual o kanban)
    if (sourceFilter) {
      result = result.filter(task => task.source === sourceFilter);
    }
    
    // Filtrar por rango de fechas
    if (startDateFilter) {
      const startDate = new Date(startDateFilter);
      startDate.setHours(0, 0, 0, 0);
      result = result.filter(task => {
        const taskDate = new Date(task.updatedAt);
        return taskDate >= startDate;
      });
    }
    
    if (endDateFilter) {
      const endDate = new Date(endDateFilter);
      endDate.setHours(23, 59, 59, 999);
      result = result.filter(task => {
        const taskDate = new Date(task.updatedAt);
        return taskDate <= endDate;
      });
    }
    
    setFilteredTasks(result);
    setPage(0); // Resetear a la primera página cuando se aplican filtros
  };

  // Función para limpiar filtros
  const clearFilters = () => {
    setAssigneeFilter('');
    setStartDateFilter('');
    setEndDateFilter('');
    setTitleFilter('');
    setSourceFilter('');
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleRestoreTask = async (taskId) => {
    try {
      // Encontrar la tarea para determinar su tipo
      const task = completedTasks.find(t => t.id === taskId);

      // Determinar el estado de restauración según el tipo de tarea
      let restoreStatus;
      if (task && task.source === 'MANUAL_ENTRY') {
        restoreStatus = 'PENDING'; // Tareas manuales van a PENDING para re-aprobación
      } else {
        restoreStatus = 'TODO'; // Tareas Kanban van a TODO
      }

      await axios.patch(
        `${API_URL}/api/kanban/tasks/${taskId}/status`,
        { status: restoreStatus },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      // Actualizar la lista de tareas completadas
      fetchCompletedTasks();
      
      // Mostrar mensaje de éxito
      setError(null);
    } catch (error) {
      console.error('Error al restaurar la tarea:', error);
      setError('Error al restaurar la tarea');
    }
  };

  // Función para generar y descargar Excel
  const generateExcel = async () => {
    try {
      setDownloadLoading(true);
      
      // Cargar dinámicamente la librería xlsx
      const XLSX = await import('xlsx');
      
      // Preparar los datos para Excel
      const tasksToExport = filteredTasks.map(task => ({
        'ID': task.id,
        'Título': task.title,
        'Descripción': task.description || '',
        'Asignado a': task.assignee || 'Sin asignar',
        'Horas Estimadas': task.estimatedHours || 0,
        'Horas Reales': task.actualHours !== null && task.actualHours !== undefined
          ? Number(task.actualHours).toFixed(2)
          : 'N/A',
        'Inicio Contador': task.timerStartTime
          ? format(new Date(task.timerStartTime), 'dd/MM/yyyy HH:mm')
          : 'N/A',
        'Fin Contador': task.timerEndTime
          ? format(new Date(task.timerEndTime), 'dd/MM/yyyy HH:mm')
          : 'N/A',
        'Fecha Completado': task.updatedAt
          ? format(new Date(task.updatedAt), 'dd/MM/yyyy HH:mm')
          : 'N/A',
        'Tipo': task.source === 'MANUAL_ENTRY' ? 'Manual' : 'Kanban'
      }));
      
      // Crear libro y hoja
      const worksheet = XLSX.utils.json_to_sheet(tasksToExport);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Tareas Completadas');
      
      // Ajustar ancho de columnas
      const maxWidth = tasksToExport.reduce((acc, task) => {
        return {
          'ID': Math.max(acc['ID'], String(task['ID']).length),
          'Título': Math.max(acc['Título'], String(task['Título']).length),
          'Descripción': Math.max(acc['Descripción'], String(task['Descripción']).length),
          'Asignado a': Math.max(acc['Asignado a'], String(task['Asignado a']).length),
          'Horas Estimadas': Math.max(acc['Horas Estimadas'], String(task['Horas Estimadas']).length),
          'Horas Reales': Math.max(acc['Horas Reales'], String(task['Horas Reales']).length),
          'Fecha Completado': Math.max(acc['Fecha Completado'], String(task['Fecha Completado']).length),
          'Tipo': Math.max(acc['Tipo'], String(task['Tipo']).length)
        };
      }, {
        'ID': 2,
        'Título': 6,
        'Descripción': 11,
        'Asignado a': 10,
        'Horas Estimadas': 15,
        'Horas Reales': 11,
        'Fecha Completado': 16,
        'Tipo': 4
      });
      
      // Generar archivo y descargar
      XLSX.writeFile(workbook, `tareas-completadas-${new Date().toISOString().split('T')[0]}.xlsx`);
      
      setError(null);
    } catch (error) {
      console.error('Error al generar Excel:', error);
      setError('Error al generar el archivo Excel. Verifica que la librería xlsx esté instalada.');
    } finally {
      setDownloadLoading(false);
    }
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4">
            Tareas Completadas
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2 }}>
            {/* Botón para descargar Excel */}
            <Button 
              variant="contained" 
              color="success"
              startIcon={<FileDownloadIcon />}
              onClick={generateExcel}
              disabled={downloadLoading || filteredTasks.length === 0}
            >
              {downloadLoading ? 'Generando...' : 'Descargar Excel'}
            </Button>
            
            {/* Botón de filtros solo para administradores */}
            {currentUser?.role === 'ADMIN' && (
              <Button 
                variant="outlined" 
                startIcon={<FilterIcon />}
                onClick={() => setShowFilters(!showFilters)}
              >
                {showFilters ? 'Ocultar filtros' : 'Mostrar filtros'}
              </Button>
            )}
          </Box>
        </Box>
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {/* Panel de filtros solo para administradores */}
        {currentUser?.role === 'ADMIN' && (
          <Collapse in={showFilters}>
            <Paper sx={{ p: 2, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Filtros
              </Typography>
              
              <Grid container spacing={2} alignItems="center">
                {/* Filtro por título */}
                <Grid item xs={12} sm={2}>
                  <TextField
                    label="Título"
                    fullWidth
                    size="small"
                    value={titleFilter}
                    onChange={(e) => setTitleFilter(e.target.value)}
                  />
                </Grid>
                
                {/* Filtro por tipo de tarea (manual o kanban) */}
                <Grid item xs={12} sm={2}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Tipo</InputLabel>
                    <Select
                      value={sourceFilter}
                      label="Tipo"
                      onChange={(e) => setSourceFilter(e.target.value)}
                    >
                      <MenuItem value="">Todos</MenuItem>
                      <MenuItem value="MANUAL_ENTRY">Manual</MenuItem>
                      <MenuItem value="KANBAN">Kanban</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                {/* Filtro por asignado */}
                <Grid item xs={12} sm={2}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Asignado a</InputLabel>
                    <Select
                      value={assigneeFilter}
                      label="Asignado a"
                      onChange={(e) => setAssigneeFilter(e.target.value)}
                    >
                      <MenuItem value="">Todos</MenuItem>
                      {users && users.length > 0 ? (
                        users.map(user => (
                          <MenuItem key={user.id} value={user.id}>
                            {user.name || user.email || 'Usuario sin nombre'}
                          </MenuItem>
                        ))
                      ) : (
                        <MenuItem disabled>No hay usuarios disponibles</MenuItem>
                      )}
                    </Select>
                  </FormControl>
                </Grid>
                
                {/* Filtro por fecha de inicio */}
                <Grid item xs={12} sm={2}>
                  <TextField
                    label="Fecha desde"
                    type="date"
                    fullWidth
                    size="small"
                    value={startDateFilter}
                    onChange={(e) => setStartDateFilter(e.target.value)}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                
                {/* Filtro por fecha de fin */}
                <Grid item xs={12} sm={2}>
                  <TextField
                    label="Fecha hasta"
                    type="date"
                    fullWidth
                    size="small"
                    value={endDateFilter}
                    onChange={(e) => setEndDateFilter(e.target.value)}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                
                {/* Botón para limpiar filtros */}
                <Grid item xs={12} sm={2}>
                  <Button 
                    variant="outlined" 
                    fullWidth
                    onClick={clearFilters}
                  >
                    Limpiar
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          </Collapse>
        )}
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer component={Paper} sx={{ mt: 3 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Título</TableCell>
                    <TableCell>Descripción</TableCell>
                    <TableCell>Asignado a</TableCell>
                    <TableCell>Horas Estimadas</TableCell>
                    <TableCell>Horas Reales</TableCell>
                    <TableCell>Inicio Contador</TableCell>
                    <TableCell>Fin Contador</TableCell>
                    <TableCell>Fecha Completado</TableCell>
                    <TableCell>Acciones</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredTasks
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((task) => (
                      <TableRow key={task.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {task.title}
                            {task.source === 'MANUAL_ENTRY' && (
                              <Chip
                                label="Manual"
                                size="small"
                                color="info"
                              />
                            )}
                            {task.source === 'KANBAN' && (
                              <Chip
                                label="Kanban"
                                size="small"
                                color="success"
                              />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          {task.description && task.description.length > 50
                            ? `${task.description.substring(0, 50)}...`
                            : task.description}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={task.assignee || 'Sin asignar'}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>{task.estimatedHours}h</TableCell>
                        <TableCell>
                          {task.actualHours !== null && task.actualHours !== undefined
                            ? `${task.actualHours.toFixed(5)}h`
                            : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {task.timerStartTime ? format(new Date(task.timerStartTime), 'dd/MM/yyyy HH:mm') : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {task.timerEndTime ? format(new Date(task.timerEndTime), 'dd/MM/yyyy HH:mm') : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {task.updatedAt ? format(new Date(task.updatedAt), 'dd/MM/yyyy HH:mm') : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {currentUser?.role === 'ADMIN' ? (
                            <Tooltip title="Restaurar tarea">
                              <IconButton 
                                size="small" 
                                color="primary"
                                onClick={() => handleRestoreTask(task.id)}
                              >
                                <RestoreIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          ) : null}
                          <Tooltip title="Ver detalles">
                            <IconButton 
                              size="small" 
                              color="info"
                              onClick={() => window.location.href = `/kanban?task=${task.id}`}
                            >
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  
                  {filteredTasks.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={9} align="center">
                        No hay tareas completadas
                        {currentUser?.role === 'ADMIN' && showFilters && ' que coincidan con los filtros'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={filteredTasks.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage="Filas por página"
              labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}
            />
          </>
        )}
      </Box>
    </Container>
  );
};

export default CompletedTasks;





