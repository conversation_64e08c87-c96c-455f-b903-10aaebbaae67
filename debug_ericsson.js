const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function debugE<PERSON>sson() {
  try {
    console.log('🔍 INVESTIGANDO ERICSSON HUAMÁN - SEMANA 19-25 MAYO 2025');
    
    // 1. Buscar usuario <PERSON>
    const user = await prisma.user.findFirst({
      where: {
        name: {
          contains: 'er<PERSON><PERSON>',
          mode: 'insensitive'
        }
      }
    });
    
    if (!user) {
      console.log('❌ <PERSON><PERSON><PERSON> no encontrado');
      return;
    }
    
    console.log(`✅ Usuario encontrado: ${user.name} (ID: ${user.id})`);
    
    // 2. Buscar registros de tiempo en esa semana
    const weekStart = new Date('2025-05-19T00:00:00.000Z');
    const weekEnd = new Date('2025-05-25T23:59:59.999Z');
    
    console.log(`📅 Buscando registros entre ${weekStart.toISOString()} y ${weekEnd.toISOString()}`);
    
    const timeEntries = await prisma.timeEntry.findMany({
      where: {
        userId: user.id,
        startTime: {
          gte: weekStart,
          lte: weekEnd
        },
        status: 'APPROVED'
      },
      orderBy: {
        startTime: 'asc'
      }
    });
    
    console.log(`📝 Registros encontrados: ${timeEntries.length}`);
    
    if (timeEntries.length === 0) {
      console.log('❌ No hay registros aprobados para Ericsson en esa semana');
      return;
    }
    
    // 3. Obtener configuración de horarios
    const workSchedules = await prisma.workSchedule.findMany({
      orderBy: { dayOfWeek: 'asc' }
    });
    
    const defaultSchedule = workSchedules.length > 0 ? workSchedules[0] : {
      startTime: '09:00',
      endTime: '18:00',
      lunchBreak: '13:00',
      lunchDuration: 60,
      workDays: [1, 2, 3, 4, 5] // L-V
    };
    
    console.log('⚙️ CONFIGURACIÓN DE HORARIOS:');
    console.log(`  Horario: ${defaultSchedule.startTime} - ${defaultSchedule.endTime}`);
    console.log(`  Almuerzo: ${defaultSchedule.lunchBreak} (${defaultSchedule.lunchDuration}min)`);
    console.log(`  Días laborables: ${defaultSchedule.workDays}`);
    
    // 4. Analizar cada registro
    let totalOutsideHours = 0;
    
    console.log('\n📊 ANÁLISIS DETALLADO DE CADA REGISTRO:');
    
    timeEntries.forEach((entry, index) => {
      const entryStart = new Date(entry.startTime);
      const entryEnd = new Date(entry.endTime);
      const entryDayOfWeek = entryStart.getDay();
      
      console.log(`\n--- REGISTRO ${index + 1} ---`);
      console.log(`📝 Título: ${entry.title}`);
      console.log(`🕐 Horario: ${entryStart.toISOString()} a ${entryEnd.toISOString()}`);
      console.log(`⏰ Horas trabajadas: ${entry.hoursWorked}h`);
      console.log(`📅 Día de la semana: ${entryDayOfWeek} (0=Dom, 1=Lun, etc.)`);
      
      let entryOutsideHours = 0;
      
      // Verificar si es día laborable
      if (!defaultSchedule.workDays.includes(entryDayOfWeek)) {
        // Día no laborable - todas las horas son fuera de oficina
        entryOutsideHours = entry.hoursWorked;
        console.log(`🚫 DÍA NO LABORABLE: ${entryOutsideHours}h completamente fuera de horario`);
      } else {
        // Día laborable - calcular horas fuera del horario estándar
        console.log(`✅ DÍA LABORABLE - Calculando horas fuera de horario:`);
        
        const [workStartHour, workStartMin] = defaultSchedule.startTime.split(':').map(Number);
        const [workEndHour, workEndMin] = defaultSchedule.endTime.split(':').map(Number);
        
        const workStartMinutes = workStartHour * 60 + workStartMin;
        const workEndMinutes = workEndHour * 60 + workEndMin;
        
        const entryStartMinutes = entryStart.getHours() * 60 + entryStart.getMinutes();
        const entryEndMinutes = entryEnd.getHours() * 60 + entryEnd.getMinutes();
        
        console.log(`  Horario trabajo: ${workStartMinutes}min (${defaultSchedule.startTime}) - ${workEndMinutes}min (${defaultSchedule.endTime})`);
        console.log(`  Entrada: ${entryStartMinutes}min - ${entryEndMinutes}min`);
        
        // 1. Horas antes del trabajo
        if (entryStartMinutes < workStartMinutes) {
          const endBeforeWork = Math.min(entryEndMinutes, workStartMinutes);
          const hoursBeforeWork = Math.max(0, (endBeforeWork - entryStartMinutes) / 60);
          entryOutsideHours += hoursBeforeWork;
          console.log(`  🌅 Antes del trabajo: ${hoursBeforeWork.toFixed(2)}h`);
        }
        
        // 2. Horas después del trabajo
        if (entryEndMinutes > workEndMinutes) {
          const startAfterWork = Math.max(entryStartMinutes, workEndMinutes);
          const hoursAfterWork = Math.max(0, (entryEndMinutes - startAfterWork) / 60);
          entryOutsideHours += hoursAfterWork;
          console.log(`  🌙 Después del trabajo: ${hoursAfterWork.toFixed(2)}h`);
        }
        
        // 3. Almuerzo ya no se cuenta como fuera de oficina
        console.log(`  🍽️ Almuerzo: NO se cuenta como fuera de oficina`);
      }
      
      console.log(`📊 Total fuera de oficina para este registro: ${entryOutsideHours.toFixed(2)}h`);
      totalOutsideHours += entryOutsideHours;
    });
    
    console.log(`\n🎯 RESULTADO FINAL:`);
    console.log(`📊 Total horas fuera de oficina para ${user.name}: ${totalOutsideHours.toFixed(2)}h`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugEricsson();
