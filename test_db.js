const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testConnection() {
  try {
    const userCount = await prisma.user.count();
    console.log('✅ Conexión exitosa. Usuarios en BD:', userCount);
    
    if (userCount > 0) {
      const users = await prisma.user.findMany({
        select: { email: true, name: true, role: true },
        take: 3
      });
      console.log('👥 Usuarios encontrados:', users);
    }
  } catch (error) {
    console.error('❌ Error de conexión:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();
