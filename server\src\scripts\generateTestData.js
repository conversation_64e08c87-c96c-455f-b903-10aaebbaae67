const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function generateTestData() {
  try {
    // Create test users with different roles
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Admin User',
        password: await bcrypt.hash('admin123', 10),
        role: 'ADMIN'
      }
    });

    const managerUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Manager User',
        password: await bcrypt.hash('manager123', 10),
        role: 'MANAGER'
      }
    });

    const employee1 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Employee One',
        password: await bcrypt.hash('emp123', 10),
        role: 'EMPLOYEE'
      }
    });

    // Create test tasks
    const task1 = await prisma.task.create({
      data: {
        title: 'Implement Login Feature',
        description: 'Create login page with authentication',
        status: 'IN_PROGRESS',
        estimatedHours: 8,
        actualHours: 6,
        priority: 'HIGH',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        tags: ['frontend', 'auth'],
        assigneeId: employee1.id,
        creatorId: managerUser.id
      }
    });

    const task2 = await prisma.task.create({
      data: {
        title: 'Database Optimization',
        description: 'Optimize database queries for better performance',
        status: 'TODO',
        estimatedHours: 16,
        priority: 'MEDIUM',
        dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
        tags: ['backend', 'database'],
        assigneeId: employee1.id,
        creatorId: managerUser.id
      }
    });

    // Create time entries
    const timeEntry1 = await prisma.timeEntry.create({
      data: {
        title: 'Login Feature Development',
        description: 'Working on authentication implementation',
        startTime: new Date(Date.now() - 4 * 60 * 60 * 1000),
        endTime: new Date(),
        hoursWorked: 4,
        status: 'PENDING',
        type: 'REGULAR',
        userId: employee1.id,
        taskId: task1.id
      }
    });

    const timeEntry2 = await prisma.timeEntry.create({
      data: {
        title: 'Overtime Work on Login',
        description: 'Extended work on login feature',
        startTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
        endTime: new Date(Date.now() - 20 * 60 * 60 * 1000),
        hoursWorked: 4,
        status: 'APPROVED',
        type: 'OVERTIME',
        userId: employee1.id,
        taskId: task1.id,
        approverId: managerUser.id,
        approvedAt: new Date()
      }
    });

    // Create permissions
    await prisma.permission.create({
      data: {
        type: 'VACATION',
        startTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        endTime: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000),
        reason: 'Annual vacation',
        status: 'PENDING',
        userId: employee1.id
      }
    });

    console.log('Test data generated successfully!');
  } catch (error) {
    console.error('Error generating test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

generateTestData();