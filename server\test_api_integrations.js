const { PrismaClient } = require('@prisma/client');
const apiExecutionService = require('./src/services/apiExecutionService');

const prisma = new PrismaClient();

async function testApiIntegrations() {
  console.log('🧪 Iniciando pruebas de integraciones API...\n');

  try {
    // 1. Verificar que las tablas existen
    console.log('1️⃣ Verificando estructura de base de datos...');
    
    const integrations = await prisma.apiIntegration.findMany();
    console.log(`   ✅ Tabla ApiIntegration: ${integrations.length} registros`);
    
    const userApiKeys = await prisma.userApiKey.findMany();
    console.log(`   ✅ Tabla UserApiKey: ${userApiKeys.length} registros`);
    
    const importLogs = await prisma.apiImportLog.findMany();
    console.log(`   ✅ Tabla ApiImportLog: ${importLogs.length} registros\n`);

    // 2. Probar validación de integraciones
    console.log('2️⃣ Probando validación de integraciones...');
    
    const validIntegration = {
      name: 'Test Integration',
      baseUrl: 'https://api.example.com',
      authType: 'api_key',
      endpoints: [{
        name: 'Test Endpoint',
        method: 'GET',
        path: '/test',
        targetMenu: 'kanban'
      }]
    };
    
    const validation = apiExecutionService.validateIntegration(validIntegration);
    console.log(`   ✅ Validación exitosa: ${validation.isValid}`);
    
    if (!validation.isValid) {
      console.log(`   ❌ Errores: ${validation.errors.join(', ')}`);
    }

    // 3. Probar procesamiento de parámetros
    console.log('\n3️⃣ Probando procesamiento de parámetros...');
    
    const testUser = {
      id: 'test-user-123',
      email: '<EMAIL>',
      name: 'Test User'
    };
    
    const testParams = {
      search_criteria: [{
        field: 'technician.email_id',
        condition: 'is',
        value: '{{USER_EMAIL}}'
      }],
      user_info: {
        id: '{{USER_ID}}',
        name: '{{USER_NAME}}'
      }
    };
    
    const processedParams = apiExecutionService.processParameters(testParams, testUser);
    console.log('   ✅ Parámetros procesados:');
    console.log('   ', JSON.stringify(processedParams, null, 2));

    // 4. Crear integración de prueba
    console.log('\n4️⃣ Creando integración de prueba...');
    
    const testIntegration = await prisma.apiIntegration.create({
      data: {
        name: 'Test ManageEngine',
        description: 'Integración de prueba',
        baseUrl: 'https://demo.manageengine.com/api/v3',
        authType: 'api_key',
        authConfig: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        endpoints: [{
          name: 'Test Tickets',
          method: 'GET',
          path: '/requests',
          targetMenu: 'kanban',
          queryParams: {
            input_data: {
              list_info: {
                row_count: 10,
                start_index: 1
              }
            }
          },
          fieldMapping: {
            title: 'subject',
            description: 'description',
            status: 'status.name',
            priority: 'priority.name',
            externalId: 'id'
          }
        }],
        createdBy: 'test-admin'
      }
    });
    
    console.log(`   ✅ Integración creada: ${testIntegration.id}`);

    // 5. Crear API key de prueba
    console.log('\n5️⃣ Creando API key de prueba...');
    
    // Primero necesitamos un usuario de prueba
    let testUserRecord;
    try {
      testUserRecord = await prisma.user.findFirst({
        where: { email: '<EMAIL>' }
      });
      
      if (!testUserRecord) {
        testUserRecord = await prisma.user.create({
          data: {
            email: '<EMAIL>',
            name: 'Test User',
            password: 'hashed-password',
            role: 'EMPLOYEE'
          }
        });
      }
    } catch (error) {
      console.log('   ⚠️ No se pudo crear usuario de prueba, usando usuario existente');
      testUserRecord = await prisma.user.findFirst();
    }

    if (testUserRecord) {
      const testApiKey = await prisma.userApiKey.create({
        data: {
          userId: testUserRecord.id,
          integrationId: testIntegration.id,
          apiKey: 'encrypted-test-api-key-123',
          isActive: true
        }
      });
      
      console.log(`   ✅ API key creada: ${testApiKey.id}`);
    }

    // 6. Crear log de prueba
    console.log('\n6️⃣ Creando log de importación de prueba...');
    
    if (testUserRecord) {
      const testLog = await prisma.apiImportLog.create({
        data: {
          userId: testUserRecord.id,
          integrationId: testIntegration.id,
          endpointName: 'Test Tickets',
          status: 'success',
          recordsFound: 5,
          tasksCreated: 3,
          tasksUpdated: 2,
          executionTime: 1500
        }
      });
      
      console.log(`   ✅ Log creado: ${testLog.id}`);
    }

    // 7. Verificar relaciones
    console.log('\n7️⃣ Verificando relaciones...');
    
    const integrationWithRelations = await prisma.apiIntegration.findUnique({
      where: { id: testIntegration.id },
      include: {
        userApiKeys: true,
        importLogs: true,
        _count: {
          select: {
            userApiKeys: true,
            importLogs: true,
            tasks: true
          }
        }
      }
    });
    
    console.log(`   ✅ Integración con relaciones:`);
    console.log(`      - API Keys: ${integrationWithRelations._count.userApiKeys}`);
    console.log(`      - Logs: ${integrationWithRelations._count.importLogs}`);
    console.log(`      - Tareas: ${integrationWithRelations._count.tasks}`);

    console.log('\n🎉 ¡Todas las pruebas completadas exitosamente!');
    
    // Limpiar datos de prueba
    console.log('\n🧹 Limpiando datos de prueba...');
    await prisma.apiImportLog.deleteMany({
      where: { integrationId: testIntegration.id }
    });
    await prisma.userApiKey.deleteMany({
      where: { integrationId: testIntegration.id }
    });
    await prisma.apiIntegration.delete({
      where: { id: testIntegration.id }
    });
    console.log('   ✅ Datos de prueba eliminados');

  } catch (error) {
    console.error('❌ Error en las pruebas:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar pruebas si el archivo se ejecuta directamente
if (require.main === module) {
  testApiIntegrations()
    .then(() => {
      console.log('\n✅ Pruebas completadas exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Error en las pruebas:', error);
      process.exit(1);
    });
}

module.exports = { testApiIntegrations };
