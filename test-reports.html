<!DOCTYPE html>
<html>
<head>
    <title>Test Reports</title>
</head>
<body>
    <h1>Test Reports</h1>
    <div id="results"></div>
    
    <script>
        async function testReports() {
            const results = document.getElementById('results');
            const token = localStorage.getItem('token') || 'test-token';
            
            const endpoints = [
                '/api/reports/task',
                '/api/reports/time',
                '/api/reports/user',
                '/api/reports/status'
            ];
            
            const startDate = '2024-01-01';
            const endDate = '2024-12-31';
            
            for (const endpoint of endpoints) {
                try {
                    const url = `http://localhost:5000${endpoint}?startDate=${startDate}&endDate=${endDate}`;
                    console.log('Testing:', url);
                    
                    const response = await fetch(url, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const data = await response.json();
                    
                    results.innerHTML += `
                        <h3>${endpoint}</h3>
                        <p>Status: ${response.status}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                        <hr>
                    `;
                } catch (error) {
                    results.innerHTML += `
                        <h3>${endpoint}</h3>
                        <p>Error: ${error.message}</p>
                        <hr>
                    `;
                }
            }
        }
        
        // Auto-run test
        testReports();
    </script>
</body>
</html>
