import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  Box,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  CircularProgress,
  Alert,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  format,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  addWeeks,
  subWeeks,
  parseISO
} from 'date-fns';
import { es } from 'date-fns/locale';
import axios from 'axios';
import { API_URL } from '../config';
import {
  NavigateBefore as PrevIcon,
  NavigateNext as NextIcon,
  Assignment as AssignmentIcon,
  Event as EventIcon,
  BeachAccess as PermissionIcon,
  Task as KanbanIcon,
  Star as WeightedIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

const WeeklyCalendar = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [weekDays, setWeekDays] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [kanbanTasks, setKanbanTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showOutsideOfficeHoursOnly, setShowOutsideOfficeHoursOnly] = useState(false);
  const [showTimeEntries, setShowTimeEntries] = useState(true);
  const [showPermissions, setShowPermissions] = useState(true);
  const [showKanbanTasks, setShowKanbanTasks] = useState(true);

  // Referencias removidas - no necesarias en el nuevo layout
  const [selectedUser, setSelectedUser] = useState('');
  const [users, setUsers] = useState([]);
  const isAdmin = localStorage.getItem('userRole') === 'ADMIN' || localStorage.getItem('userRole') === 'MANAGER';

  useEffect(() => {
    // Calcular los días de la semana actual
    const start = startOfWeek(currentDate, { weekStartsOn: 1 }); // Lunes como inicio de semana
    const end = endOfWeek(currentDate, { weekStartsOn: 1 }); // Domingo como fin de semana
    const days = eachDayOfInterval({ start, end });
    setWeekDays(days);

    // Cargar datos
    fetchData();
  }, [currentDate, selectedUser, showOutsideOfficeHoursOnly]);



  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Calcular fechas para la consulta
      const start = startOfWeek(currentDate, { weekStartsOn: 1 });
      const end = endOfWeek(currentDate, { weekStartsOn: 1 });

      // Formatear fechas para la API
      const startStr = format(start, 'yyyy-MM-dd');
      const endStr = format(end, 'yyyy-MM-dd');

      // Parámetros de consulta
      const params = { startDate: startStr, endDate: endStr };

      // Solo admins pueden filtrar por usuario
      if (isAdmin && selectedUser && selectedUser !== 'ALL') {
        params.userId = selectedUser;
      }
      // Para usuarios normales, el backend automáticamente filtra por su ID

      if (showOutsideOfficeHoursOnly) {
        params.outsideOfficeHoursOnly = 'true';
      }

      console.log('Solicitando datos con parámetros:', params);

      // Obtener tareas completadas
      const tasksResponse = await axios.get(`${API_URL}/api/tasks/completed-by-date`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        params
      });

      // Obtener permisos aprobados
      const permissionsResponse = await axios.get(`${API_URL}/api/permissions/approved-by-date`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        params
      });

      // Si es admin, obtener lista de usuarios
      if (isAdmin && users.length === 0) {
        const usersResponse = await axios.get(`${API_URL}/api/users`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          }
        });
        setUsers(usersResponse.data);
      }

      // Obtener tareas kanban archivadas
      const kanbanResponse = await axios.get(`${API_URL}/api/tasks/archived-kanban-tasks`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        params
      });

      console.log('Tareas recibidas:', tasksResponse.data);
      console.log('Permisos recibidos:', permissionsResponse.data);
      console.log('Tareas kanban recibidas:', kanbanResponse.data);

      // Log detallado de las primeras 3 tareas
      if (tasksResponse.data.length > 0) {
        console.log('🔍 ANÁLISIS DE TAREAS RECIBIDAS:');
        tasksResponse.data.slice(0, 3).forEach((task, index) => {
          console.log(`Tarea ${index + 1}:`, {
            id: task.id,
            title: task.title,
            status: task.status,
            isManualEntry: task.isManualEntry,
            timeEntries: task.timeEntries?.length || 0,
            assignee: task.assignee?.name
          });
        });
      }

      setTasks(tasksResponse.data);
      setPermissions(permissionsResponse.data);
      setKanbanTasks(kanbanResponse.data);
    } catch (err) {
      console.error('Error al cargar datos del calendario:', err);
      setError('Error al cargar datos del calendario');
    } finally {
      setLoading(false);
    }
  };

  const handlePrevWeek = () => {
    setCurrentDate(subWeeks(currentDate, 1));
  };

  const handleNextWeek = () => {
    setCurrentDate(addWeeks(currentDate, 1));
  };

  const handleUserChange = (event) => {
    setSelectedUser(event.target.value);
  };

  // Función de scroll removida - no necesaria en el nuevo layout

  // Función para obtener eventos de un día específico
  const getDayEvents = (day) => {
    // Para registros manuales aprobados, NO filtrar por updatedAt
    // El backend ya envió solo los del rango correcto
    const dayTasks = tasks.filter(task => {
      if (task.isManualEntry) {
        // Para registros manuales, verificar si tienen timeEntries para este día
        return task.timeEntries && task.timeEntries.some(entry => {
          const entryDate = parseISO(entry.startTime);
          return format(entryDate, 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd');
        });
      } else {
        // Para tareas normales del kanban, usar updatedAt
        const completedDate = parseISO(task.updatedAt);
        return format(completedDate, 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd');
      }
    }).sort((a, b) => {
      // Ordenar tareas por hora de completado o inicio
      if (a.isManualEntry && a.timeEntries?.[0]) {
        const aTime = parseISO(a.timeEntries[0].startTime);
        const bTime = b.isManualEntry && b.timeEntries?.[0]
          ? parseISO(b.timeEntries[0].startTime)
          : parseISO(b.updatedAt);
        return aTime - bTime;
      }
      return parseISO(a.updatedAt) - parseISO(b.updatedAt);
    });

    const dayPermissions = permissions.filter(permission => {
      const startDate = parseISO(permission.startTime);
      const endDate = parseISO(permission.endTime);
      const dayStart = new Date(day);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(day);
      dayEnd.setHours(23, 59, 59, 999);

      // Verificar si el permiso se superpone con este día
      const overlaps = startDate <= dayEnd && endDate >= dayStart;

      if (overlaps) {
        console.log(`🏖️ PERMISO ENCONTRADO para ${format(day, 'yyyy-MM-dd')}:`);
        console.log(`  📝 Razón: ${permission.reason || 'Sin razón'}`);
        console.log(`  👤 Usuario: ${permission.user?.name || 'Desconocido'}`);
        console.log(`  🕐 Inicio: ${permission.startTime} → ${format(startDate, 'HH:mm')}`);
        console.log(`  🕐 Fin: ${permission.endTime} → ${format(endDate, 'HH:mm')}`);
      }

      return overlaps;
    }).sort((a, b) => {
      // Ordenar permisos por hora de inicio
      return parseISO(a.startTime) - parseISO(b.startTime);
    });

    // Filtrar tareas kanban para este día
    const dayKanbanTasks = kanbanTasks.filter(kanbanTask => {
      const startDate = parseISO(kanbanTask.startTime);
      const dayStr = format(day, 'yyyy-MM-dd');
      const kanbanDateStr = format(startDate, 'yyyy-MM-dd');

      const matches = kanbanDateStr === dayStr;

      if (matches) {
        console.log(`🎯 TAREA KANBAN ENCONTRADA para ${dayStr}:`);
        console.log(`  📝 Título: ${kanbanTask.title}`);
        console.log(`  👤 Usuario: ${kanbanTask.user?.name || 'Desconocido'}`);
        console.log(`  👤 Asignado a: ${kanbanTask.assignee?.name || 'Sin asignar'}`);
        console.log(`  🕐 Inicio: ${kanbanTask.startTime} → ${format(startDate, 'HH:mm')}`);
        console.log(`  🕐 Fin: ${kanbanTask.endTime} → ${format(parseISO(kanbanTask.endTime), 'HH:mm')}`);
        console.log(`  🎯 Horas reales trabajadas: ${kanbanTask.actualHours}h`);
        console.log(`  📊 Duración calculada: ${format(startDate, 'HH:mm')} - ${format(parseISO(kanbanTask.endTime), 'HH:mm')}`);
      }

      return matches;
    }).sort((a, b) => {
      return parseISO(a.startTime) - parseISO(b.startTime);
    });

    return { tasks: dayTasks, permissions: dayPermissions, kanbanTasks: dayKanbanTasks };
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  return (
    <Paper sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          {isAdmin ? 'Calendario Semanal' : 'Mi Calendario'}: {format(weekDays[0], 'dd/MM/yyyy', { locale: es })} - {format(weekDays[6], 'dd/MM/yyyy', { locale: es })}
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, flexWrap: 'wrap' }}>
          {isAdmin && (
            <FormControl
              size="small"
              sx={{
                minWidth: 140,
                '& .MuiInputLabel-root': {
                  fontSize: '0.75rem'
                },
                '& .MuiSelect-select': {
                  fontSize: '0.75rem',
                  py: 0.5
                }
              }}
            >
              <InputLabel>Usuario</InputLabel>
              <Select
                value={selectedUser}
                label="Usuario"
                onChange={handleUserChange}
              >
                <MenuItem value="" sx={{ fontSize: '0.75rem' }}>Yo</MenuItem>
                <MenuItem value="ALL" sx={{ fontSize: '0.75rem' }}>Todos los usuarios</MenuItem>
                {users.map(user => (
                  <MenuItem key={user.id} value={user.id} sx={{ fontSize: '0.75rem' }}>
                    {user.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}

          <FormControlLabel
            control={
              <Switch
                checked={showTimeEntries}
                onChange={(e) => setShowTimeEntries(e.target.checked)}
                size="small"
              />
            }
            label="Registros de tiempo"
            sx={{
              fontSize: '0.75rem',
              '& .MuiFormControlLabel-label': {
                fontSize: '0.75rem'
              }
            }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={showPermissions}
                onChange={(e) => setShowPermissions(e.target.checked)}
                size="small"
              />
            }
            label="Permisos"
            sx={{
              fontSize: '0.75rem',
              '& .MuiFormControlLabel-label': {
                fontSize: '0.75rem'
              }
            }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={showKanbanTasks}
                onChange={(e) => setShowKanbanTasks(e.target.checked)}
                size="small"
              />
            }
            label="Tareas Kanban"
            sx={{
              fontSize: '0.75rem',
              '& .MuiFormControlLabel-label': {
                fontSize: '0.75rem'
              }
            }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={showOutsideOfficeHoursOnly}
                onChange={(e) => setShowOutsideOfficeHoursOnly(e.target.checked)}
                size="small"
              />
            }
            label="Solo fuera de horario"
            sx={{
              fontSize: '0.75rem',
              '& .MuiFormControlLabel-label': {
                fontSize: '0.75rem'
              }
            }}
          />

          {!isAdmin && (
            <Typography variant="caption" sx={{
              color: 'text.secondary',
              fontStyle: 'italic',
              maxWidth: '180px',
              fontSize: '0.7rem',
              lineHeight: 1.2
            }}>
              Mostrando solo tus actividades aprobadas
            </Typography>
          )}



          <Button
            variant="outlined"
            startIcon={<PrevIcon />}
            onClick={handlePrevWeek}
            size="small"
            sx={{
              minWidth: '100px',
              fontSize: '0.75rem',
              px: 1.5,
              py: 0.5
            }}
          >
            Anterior
          </Button>
          <Button
            variant="outlined"
            endIcon={<NextIcon />}
            onClick={handleNextWeek}
            size="small"
            sx={{
              minWidth: '100px',
              fontSize: '0.75rem',
              px: 1.5,
              py: 0.5
            }}
          >
            Siguiente
          </Button>

          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={fetchData}
            size="small"
            color="primary"
            disabled={loading}
            sx={{
              minWidth: '110px',
              fontSize: '0.75rem',
              px: 1.5,
              py: 0.5
            }}
          >
            {loading ? 'Actualizando...' : 'Actualizar'}
          </Button>
        </Box>
      </Box>

      {/* Vista de Timeline Vertical - LAYOUT SIMPLIFICADO */}
      <Box sx={{
        border: '1px solid #e0e0e0',
        borderRadius: 1,
        overflow: 'auto',
        maxHeight: '80vh',
        position: 'relative',
        width: '100%'
      }}>
        {/* Contenedor con scroll común para horas y días */}
        <Box sx={{
          display: 'flex',
          minHeight: 'calc(24 * 60px + 60px)', // 24 horas + header
          position: 'relative'
        }}>
          {/* Columna de horas (izquierda) - FIJA horizontalmente */}
          <Box
            sx={{
              width: '80px',
              backgroundColor: '#f5f5f5',
              borderRight: '1px solid #e0e0e0',
              position: 'sticky',
              left: 0,
              zIndex: 10,
              flexShrink: 0
            }}>
            {/* Header de horas */}
            <Box sx={{
              height: '60px',
              borderBottom: '1px solid #e0e0e0',
              backgroundColor: '#f5f5f5',
              position: 'sticky',
              top: 0,
              zIndex: 11
            }} />

            {/* Escala de 24 horas */}
            {Array.from({ length: 24 }, (_, hour) => (
              <Box
                key={hour}
                sx={{
                  height: '60px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderBottom: '1px solid #e0e0e0',
                  backgroundColor: hour % 2 === 0 ? '#fafafa' : '#f5f5f5'
                }}
              >
                <Typography variant="caption" sx={{ fontSize: '0.75rem', fontWeight: 'medium' }}>
                  {hour.toString().padStart(2, '0')}:00
                </Typography>
              </Box>
            ))}
          </Box>

          {/* Contenedor de días */}
          <Box sx={{
            display: 'flex',
            flexGrow: 1,
            width: 'calc(100% - 80px)' // Ancho total menos la columna de horas
          }}>
          {weekDays.map(day => {
            const { tasks: dayTasks, permissions: dayPermissions, kanbanTasks: dayKanbanTasks } = getDayEvents(day);
            const isToday = format(day, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');

            // Función para calcular posición vertical basada en la hora
            const getVerticalPosition = (time) => {
              const hour = time.getHours();
              const minutes = time.getMinutes();
              return (hour * 60 + minutes) / (24 * 60) * (24 * 60) + 60; // +60 por el header
            };

            // Función para calcular altura basada en duración
            const getHeight = (startTime, endTime) => {
              const durationMinutes = (endTime - startTime) / (1000 * 60);
              return Math.max(20, (durationMinutes / (24 * 60)) * (24 * 60)); // Mínimo 20px
            };

            // Preparar actividades con posiciones usando tiempos reales
            const dayStr = format(day, 'yyyy-MM-dd');
            console.log(`🎛️ FILTROS APLICADOS para ${dayStr}:`);
            console.log(`  📝 Registros de tiempo: ${showTimeEntries ? 'HABILITADO' : 'DESHABILITADO'}`);
            console.log(`  🏖️ Permisos: ${showPermissions ? 'HABILITADO' : 'DESHABILITADO'}`);
            console.log(`  🎯 Tareas Kanban: ${showKanbanTasks ? 'HABILITADO' : 'DESHABILITADO'}`);
            console.log(`  🕐 Solo fuera de horario: ${showOutsideOfficeHoursOnly ? 'HABILITADO' : 'DESHABILITADO'}`);

            const positionedActivities = [
              // Procesar registros de tiempo manuales aprobados (si están habilitados)
              ...(showTimeEntries ? dayTasks.flatMap(task => {
                console.log(`🔍 Evaluando tarea: ${task.title}`);
                console.log(`  - isManualEntry: ${task.isManualEntry}`);
                console.log(`  - timeEntries: ${task.timeEntries?.length || 0}`);
                console.log(`  - status: ${task.status}`);

                // Solo procesar tareas que son registros manuales aprobados
                if (task.isManualEntry && task.timeEntries && task.timeEntries.length > 0) {
                  console.log(`✅ Procesando tarea manual: ${task.title}`);

                  // ELIMINAR FILTRO REDUNDANTE - El backend ya filtró por rango de fechas
                  // Procesar TODAS las timeEntries que vienen del backend
                  const dayEntries = task.timeEntries.filter(entry => {
                    const entryDate = parseISO(entry.startTime);
                    const dayStr = format(day, 'yyyy-MM-dd');
                    const entryDateStr = format(entryDate, 'yyyy-MM-dd');

                    // Solo mostrar en el día correcto del calendario
                    const matches = entryDateStr === dayStr;

                    if (matches) {
                      console.log(`📅 MOSTRANDO en ${dayStr}: ${task.title}`);
                      console.log(`  🕐 Hora original: ${entry.startTime}`);
                      console.log(`  🇵🇪 Hora parseada: ${format(entryDate, 'HH:mm')} (${entryDate.toLocaleString()})`);
                    }

                    return matches;
                  });

                  if (dayEntries.length > 0) {
                    return dayEntries.flatMap(entry => {
                      const startTime = parseISO(entry.startTime);
                      const endTime = parseISO(entry.endTime);

                      // Log para registros manuales aprobados
                      const dayStr = format(day, 'yyyy-MM-dd');
                      console.log(`✅ REGISTRO MANUAL APROBADO - ${dayStr}:`);
                      console.log(`  Usuario: ${task.registeredBy?.name || task.assignee?.name || 'Desconocido'}`);
                      console.log(`  Actividad: ${entry.title}`);
                      console.log(`  Tiempo: ${format(startTime, 'HH:mm')} - ${format(endTime, 'HH:mm')}`);
                      console.log(`  Duración: ${entry.hoursWorked}h`);
                      console.log(`  Estado: APPROVED`);



                      const activities = [];

                      // Actividad principal (tiempo normal)
                      const mainActivity = {
                        ...task,
                        type: 'manual_entry',
                        entryId: entry.id,
                        entryTitle: entry.title,
                        startTime,
                        endTime,
                        top: getVerticalPosition(startTime),
                        height: getHeight(startTime, endTime),
                        displayStart: format(startTime, 'HH:mm'),
                        displayEnd: format(endTime, 'HH:mm'),
                        hoursWorked: entry.hoursWorked,
                        isManualEntry: true,
                        isOutsideOfficeHours: task.isOutsideOfficeHours, // Usar valor de la tarea principal (corregido)
                        weightedHours: task.weightedHours, // Usar valor de la tarea principal (corregido)
                        hourMultiplier: task.hourMultiplier, // Usar valor de la tarea principal (corregido)
                        multiplierReasons: entry.multiplierReasons // Mantener del entry (tiene más detalle)
                      };

                      activities.push(mainActivity);

                      // Si tiene horas ponderadas (fuera de horario), agregar bloque adicional
                      if (entry.isOutsideOfficeHours && entry.weightedHours > entry.hoursWorked) {
                        console.log(`💰 AGREGANDO HORA PONDERADA VISUAL:`);
                        console.log(`  🎯 Horas ponderadas: ${entry.weightedHours}h`);
                        console.log(`  💰 Multiplicador: ${entry.hourMultiplier}x`);
                        console.log(`  📋 Razones: ${entry.multiplierReasons?.join(', ') || 'N/A'}`);

                        // Calcular duración de la hora ponderada adicional
                        const weightedDurationMs = (entry.weightedHours - entry.hoursWorked) * 60 * 60 * 1000;
                        const weightedEndTime = new Date(endTime.getTime() + weightedDurationMs);

                        const weightedActivity = {
                          ...task,
                          type: 'weighted_hours',
                          entryId: `${entry.id}-weighted`,
                          entryTitle: `${entry.title} (Hora Ponderada +${entry.hourMultiplier}x)`,
                          startTime: endTime, // Empieza donde termina la actividad normal
                          endTime: weightedEndTime,
                          top: getVerticalPosition(endTime),
                          height: getHeight(endTime, weightedEndTime),
                          displayStart: format(endTime, 'HH:mm'),
                          displayEnd: format(weightedEndTime, 'HH:mm'),
                          hoursWorked: entry.weightedHours - entry.hoursWorked,
                          isManualEntry: true,
                          isWeightedHours: true,
                          originalHours: entry.hoursWorked,
                          weightedHours: entry.weightedHours,
                          hourMultiplier: entry.hourMultiplier,
                          multiplierReasons: entry.multiplierReasons
                        };

                        activities.push(weightedActivity);
                      }

                      return activities;
                    });
                  }
                } else {
                  console.log(`❌ Tarea NO procesada: ${task.title} (isManualEntry: ${task.isManualEntry}, timeEntries: ${task.timeEntries?.length || 0})`);
                }

                return [];
              }) : []),
              // Procesar permisos (si están habilitados)
              ...(showPermissions ? dayPermissions.map(permission => {
                console.log(`🏖️ Procesando permiso: ${permission.reason || 'Sin razón'} - Usuario: ${permission.user?.name || 'Desconocido'}`);
                const startTime = parseISO(permission.startTime);
                const endTime = parseISO(permission.endTime);

                return {
                  ...permission,
                  type: 'permission',
                  startTime,
                  endTime,
                  top: getVerticalPosition(startTime),
                  height: getHeight(startTime, endTime),
                  displayStart: format(startTime, 'HH:mm'),
                  displayEnd: format(endTime, 'HH:mm')
                };
              }) : []),
              // Procesar tareas kanban archivadas (si están habilitadas)
              ...(showKanbanTasks ? dayKanbanTasks.map(kanbanTask => {
                console.log(`🎯 Procesando tarea kanban: ${kanbanTask.title} - Usuario: ${kanbanTask.user?.name || 'Desconocido'}`);
                const startTime = parseISO(kanbanTask.startTime);
                const endTime = parseISO(kanbanTask.endTime);

                return {
                  ...kanbanTask,
                  type: 'kanban_task',
                  startTime,
                  endTime,
                  top: getVerticalPosition(startTime),
                  height: getHeight(startTime, endTime),
                  displayStart: format(startTime, 'HH:mm'),
                  displayEnd: format(endTime, 'HH:mm')
                };
              }) : [])
            ];

            return (
              <Box
                key={day.toString()}
                sx={{
                  flex: '1 1 0',
                  minWidth: '120px',
                  borderRight: '1px solid #e0e0e0',
                  position: 'relative',
                  backgroundColor: isToday ? '#e3f2fd' : 'white'
                }}
              >
                {/* Header del día */}
                <Box
                  sx={{
                    height: '60px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderBottom: '1px solid #e0e0e0',
                    backgroundColor: isToday ? '#1976d2' : '#f5f5f5',
                    color: isToday ? 'white' : 'text.primary',
                    position: 'sticky',
                    top: 0,
                    zIndex: 5
                  }}
                >
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', fontSize: '0.8rem' }}>
                    {format(day, 'EEEE', { locale: es })}
                  </Typography>
                  <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                    {format(day, 'dd/MM', { locale: es })}
                  </Typography>
                </Box>

                {/* Líneas de fondo para las horas */}
                {Array.from({ length: 24 }, (_, hour) => (
                  <Box
                    key={hour}
                    sx={{
                      position: 'absolute',
                      top: 60 + (hour * 60),
                      left: 0,
                      right: 0,
                      height: '60px',
                      borderBottom: '1px solid #f0f0f0',
                      backgroundColor: hour % 2 === 0 ? 'transparent' : '#fafafa'
                    }}
                  />
                ))}

                {/* Actividades posicionadas */}
                {(() => {
                  // Función para detectar superposiciones temporales
                  const getOverlapColumns = (activities) => {
                    const sortedActivities = [...activities].sort((a, b) => a.top - b.top);
                    const columns = [];

                    // Log para miércoles 28/05
                    const dayStr = format(day, 'yyyy-MM-dd');
                    if (dayStr === '2025-05-28') {
                      console.log(`🔍 ALGORITMO DE SUPERPOSICIÓN - ${dayStr}:`);
                      console.log(`Total actividades: ${activities.length}`);
                    }

                    sortedActivities.forEach((activity, index) => {
                      // Buscar la primera columna disponible
                      let columnIndex = 0;
                      while (columnIndex < columns.length) {
                        const column = columns[columnIndex];
                        const hasOverlap = column.some(existingActivity => {
                          const activityEnd = activity.top + activity.height;
                          const existingEnd = existingActivity.top + existingActivity.height;

                          // Verificar si hay superposición temporal
                          const overlap = !(activity.top >= existingEnd || activityEnd <= existingActivity.top);

                          // Log específico para miércoles 28/05
                          if (dayStr === '2025-05-28' && overlap) {
                            console.log(`  ⚠️ SUPERPOSICIÓN DETECTADA:`);
                            console.log(`    Actividad A: ${activity.entryTitle || activity.title} (${activity.displayStart}-${activity.displayEnd})`);
                            console.log(`    Actividad B: ${existingActivity.entryTitle || existingActivity.title} (${existingActivity.displayStart}-${existingActivity.displayEnd})`);
                            console.log(`    Posiciones: A(${activity.top}-${activityEnd}) vs B(${existingActivity.top}-${existingEnd})`);
                          }

                          return overlap;
                        });

                        if (!hasOverlap) {
                          break;
                        }
                        columnIndex++;
                      }

                      // Si no encontramos una columna disponible, crear una nueva
                      if (columnIndex >= columns.length) {
                        columns.push([]);
                      }

                      if (dayStr === '2025-05-28') {
                        console.log(`  Actividad ${index + 1}: "${activity.entryTitle || activity.title}" → Columna ${columnIndex}`);
                      }

                      columns[columnIndex].push({ ...activity, column: columnIndex });
                    });

                    if (dayStr === '2025-05-28') {
                      console.log(`Total columnas creadas: ${columns.length}`);
                    }

                    return columns.flat();
                  };

                  const activitiesWithColumns = getOverlapColumns(positionedActivities);
                  const maxColumns = Math.max(1, ...activitiesWithColumns.map(a => a.column + 1));

                  return activitiesWithColumns.map((activity) => {
                    const isManualEntry = activity.type === 'manual_entry';
                    const isPermission = activity.type === 'permission';
                    const isKanbanTask = activity.type === 'kanban_task';
                    const isWeightedHours = activity.type === 'weighted_hours';
                    const columnWidth = maxColumns > 1 ? `${100 / maxColumns}%` : 'calc(100% - 8px)';
                    const leftPosition = maxColumns > 1 ? `${(activity.column / maxColumns) * 100}%` : '4px';

                    return (
                      <Tooltip
                        key={`${activity.type}-${activity.id}-${activity.entryId || 'main'}`}
                        title={
                          activity.type === 'manual_entry'
                            ? `${activity.registeredBy?.name || activity.assignee?.name || 'Usuario'}: ${activity.entryTitle} - ${activity.hoursWorked}h (${activity.displayStart} - ${activity.displayEnd}) [Registro Manual Aprobado]${activity.isOutsideOfficeHours ? ` - ${activity.multiplierReasons?.join(', ') || 'FUERA DE HORARIO'}` : ''}`
                            : activity.type === 'weighted_hours'
                              ? `${activity.registeredBy?.name || activity.assignee?.name || 'Usuario'}: ${activity.entryTitle} - ${activity.hoursWorked}h (${activity.displayStart} - ${activity.displayEnd}) [Hora Ponderada ${activity.hourMultiplier}x] - ${activity.multiplierReasons?.join(', ') || 'Multiplicador aplicado'}`
                              : activity.type === 'permission'
                                ? `${activity.user?.name || 'Usuario'}: ${activity.reason || 'Permiso'} (${activity.displayStart} - ${activity.displayEnd}) [Permiso Aprobado]`
                                : activity.type === 'kanban_task'
                                  ? `${activity.user?.name || activity.assignee?.name || 'Usuario'}: ${activity.title} - ${activity.actualHours}h (${activity.displayStart} - ${activity.displayEnd}) [Tarea Kanban Archivada]`
                                  : `${activity.title} - ${activity.actualHours}h (${activity.displayStart} - ${activity.displayEnd})`
                        }
                      >
                        <Box
                          sx={{
                            position: 'absolute',
                            top: activity.top,
                            left: leftPosition,
                            width: columnWidth,
                            height: activity.height,
                            backgroundColor: isManualEntry ? (activity.isOutsideOfficeHours ? 'error.main' : 'success.main') : isPermission ? 'info.main' : isKanbanTask ? 'warning.main' : isWeightedHours ? 'error.dark' : 'primary.main',
                            borderRadius: '4px',
                            display: 'flex',
                            alignItems: 'center',
                            px: 1,
                            cursor: 'pointer',
                            opacity: isWeightedHours ? 0.8 : 0.9,
                            border: '1px solid',
                            borderColor: isManualEntry ? (activity.isOutsideOfficeHours ? 'error.dark' : 'success.dark') : isPermission ? 'info.dark' : isKanbanTask ? 'warning.dark' : isWeightedHours ? 'error.darker' : 'primary.dark',
                            '&:hover': {
                              opacity: 1,
                              transform: 'scale(1.02)',
                              zIndex: 10
                            },
                            transition: 'all 0.2s ease',
                            zIndex: 2
                          }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 0, width: '100%' }}>
                            {isManualEntry ? (
                              <AssignmentIcon sx={{ fontSize: '14px', color: 'white', mr: 0.5, flexShrink: 0 }} />
                            ) : isPermission ? (
                              <PermissionIcon sx={{ fontSize: '14px', color: 'white', mr: 0.5, flexShrink: 0 }} />
                            ) : isKanbanTask ? (
                              <KanbanIcon sx={{ fontSize: '14px', color: 'white', mr: 0.5, flexShrink: 0 }} />
                            ) : isWeightedHours ? (
                              <WeightedIcon sx={{ fontSize: '14px', color: 'white', mr: 0.5, flexShrink: 0 }} />
                            ) : (
                              <EventIcon sx={{ fontSize: '14px', color: 'white', mr: 0.5, flexShrink: 0 }} />
                            )}
                            <Box sx={{ minWidth: 0, flexGrow: 1 }}>
                              <Typography
                                variant="caption"
                                sx={{
                                  color: 'white',
                                  fontSize: '0.7rem',
                                  fontWeight: 'medium',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap',
                                  display: 'block'
                                }}
                              >
                                {isManualEntry
                                  ? `${activity.registeredBy?.name || activity.assignee?.name || 'Usuario'}: ${activity.entryTitle || activity.title}`
                                  : isPermission
                                    ? `${activity.user?.name || 'Usuario'}: ${activity.reason || 'Permiso'}`
                                    : isKanbanTask
                                      ? `${activity.user?.name || activity.assignee?.name || 'Usuario'}: ${activity.title}`
                                      : isWeightedHours
                                        ? `💰 ${activity.hourMultiplier}x - ${activity.multiplierReasons?.[0]?.split(':')[0] || 'Ponderada'}`
                                        : activity.type
                                }
                              </Typography>
                              <Typography
                                variant="caption"
                                sx={{
                                  color: 'rgba(255,255,255,0.8)',
                                  fontSize: '0.6rem',
                                  display: 'block'
                                }}
                              >
                                {activity.displayStart} - {activity.displayEnd}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      </Tooltip>
                    );
                  });
                })()}
              </Box>
            );
          })}
          </Box>
        </Box>
      </Box>
    </Paper>
  );
};

export default WeeklyCalendar;
