import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Typography, IconButton, Card, CardContent, Chip } from '@mui/material';
import { Timer as TimerIcon, Stop as StopIcon, Person as PersonIcon } from '@mui/icons-material';
import globalTimerService from '../services/globalTimerService';
import { socket } from '../services/socket';

const GlobalTimeTracker = ({ taskId, taskOwnerId, userRole }) => {
  const [timerData, setTimerData] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const timerRef = useRef(null);
  const currentUserId = localStorage.getItem('userId');
  
  // Determinar si el usuario puede controlar el temporizador
  const canControlTimer = taskOwnerId === currentUserId || 
                         userRole === 'ADMIN' || 
                         userRole === 'MANAGER';

  // Obtener estado inicial del temporizador
  useEffect(() => {
    const getInitialTimerStatus = async () => {
      try {
        const response = await globalTimerService.getTimerStatus(taskId);
        setTimerData(response.globalTimer);
        
        if (response.globalTimer && response.globalTimer.isRunning) {
          setElapsedTime(response.globalTimer.accumulatedTime);
          startLocalTimer(response.globalTimer.accumulatedTime);
        } else {
          setElapsedTime(response.globalTimer?.accumulatedTime || 0);
        }
      } catch (error) {
        console.error('Error al obtener estado inicial del temporizador global:', error);
      }
    };
    
    getInitialTimerStatus();
  }, [taskId]);

  // Escuchar actualizaciones del temporizador global
  useEffect(() => {
    if (socket) {
      socket.emit('joinTask', taskId);
      
      // Escuchar actualizaciones del temporizador global
      socket.on('globalTimerUpdate', (data) => {
        if (data.task.id === taskId) {
          console.log('Actualización de temporizador global recibida:', data);
          setTimerData(data.task.globalTimer);
          
          if (data.task.globalTimer.isRunning) {
            setElapsedTime(data.task.globalTimer.accumulatedTime);
            startLocalTimer(data.task.globalTimer.accumulatedTime);
          } else {
            setElapsedTime(data.task.globalTimer.accumulatedTime);
            stopLocalTimer();
          }
        }
      });
    }

    return () => {
      if (socket) {
        socket.off('globalTimerUpdate');
        socket.emit('leaveTask', taskId);
      }
      stopLocalTimer();
    };
  }, [taskId]);

  const startLocalTimer = (initialTime) => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setElapsedTime(initialTime || elapsedTime);
    timerRef.current = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);
  };

  const stopLocalTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}m ${secs}s`;
  };

  const startTimer = async () => {
    try {
      await globalTimerService.startTimer(taskId);
      console.log('Temporizador global iniciado para tarea:', taskId);
    } catch (error) {
      console.error('Error al iniciar el temporizador global:', error);
    }
  };

  const pauseTimer = async () => {
    try {
      await globalTimerService.pauseTimer(taskId);
      console.log('Temporizador global pausado para tarea:', taskId);
    } catch (error) {
      console.error('Error al pausar el temporizador global:', error);
    }
  };

  const handleTimerClick = async () => {
    if (timerData && timerData.isRunning) {
      await pauseTimer();
    } else {
      await startTimer();
    }
  };

  // Si no hay datos del temporizador, no mostrar nada
  if (!timerData && elapsedTime === 0) {
    return null;
  }

  return (
    <Card variant="outlined" sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* Mostrar botón de control solo si el usuario puede controlar el temporizador */}
          {canControlTimer && (
            <IconButton
              color={timerData?.isRunning ? "error" : "primary"}
              onClick={handleTimerClick}
              size="small"
              title={timerData?.isRunning ? "Pausar temporizador" : "Iniciar temporizador"}
            >
              <TimerIcon fontSize="small" />
            </IconButton>
          )}
          
          {/* Mostrar icono de persona si no puede controlar */}
          {!canControlTimer && (
            <PersonIcon 
              sx={{ 
                color: timerData?.isRunning ? 'success.main' : 'text.secondary',
                fontSize: 'small'
              }} 
            />
          )}
          
          <Typography 
            variant="body2" 
            component="div"
            sx={{ fontSize: '0.875rem' }}
          >
            {formatTime(elapsedTime)}
          </Typography>
          
          {/* Mostrar estado del temporizador */}
          <Chip
            label={timerData?.isRunning ? 'Corriendo' : 'Pausado'}
            color={timerData?.isRunning ? 'success' : 'default'}
            size="small"
          />
          
          {/* Mostrar quién está trabajando actualmente */}
          {timerData?.currentUser && (
            <Typography 
              variant="caption" 
              color="text.secondary"
              sx={{ ml: 'auto' }}
            >
              {timerData.currentUser}
            </Typography>
          )}
        </Box>
        
        {/* Mostrar información adicional para admins/managers */}
        {(userRole === 'ADMIN' || userRole === 'MANAGER') && timerData && (
          <Box sx={{ mt: 1 }}>
            <Typography variant="caption" color="text.secondary">
              Temporizador Global • 
              {timerData.currentUser ? ` Trabajando: ${timerData.currentUser}` : ' Sin usuario activo'}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default GlobalTimeTracker;
