import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  MenuItem,
  Snackbar,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { es } from 'date-fns/locale';
import { format } from 'date-fns';
import axios from 'axios';
import { API_URL } from '../config';
import DynamicFieldRenderer from '../components/DynamicFieldRenderer';
import { useDynamicFields } from '../hooks/useDynamicFields';

const formatHoursAndMinutes = (hours) => {
  if (hours === null || hours === undefined || isNaN(hours)) {
    return '0h 0m';
  }
  
  const wholeHours = Math.floor(hours);
  const minutes = Math.round((hours - wholeHours) * 60);
  
  return `${wholeHours}h ${minutes}m`;
};

const TimeTracking = () => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    taskId: '',
    startTime: new Date(),
    endTime: new Date(),
    hoursWorked: '',
  });

  const [tasks, setTasks] = useState([]);
  const [timeEntries, setTimeEntries] = useState([]);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // Hook para campos dinámicos
  const {
    fields: dynamicFields,
    values: dynamicValues,
    errors: dynamicErrors,
    updateValue: updateDynamicValue,
    validateFields: validateDynamicFields,
    resetValues: resetDynamicValues
  } = useDynamicFields('timeEntry');

  useEffect(() => {
    fetchTasks();
    fetchTimeEntries();
  }, []);

  const fetchTasks = async () => {
    try {
      const response = await axios.get(`${API_URL}/api/tasks/my-tasks`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      setTasks(response.data);
    } catch (error) {
      console.error('Error al obtener tareas:', error);
    }
  };

  const fetchTimeEntries = async () => {
    try {
      const response = await axios.get(`${API_URL}/api/time-entries/my-entries`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      setTimeEntries(response.data);
    } catch (error) {
      console.error('Error al obtener registros de tiempo:', error);
    }
  };

  const handleChange = (event) => {
    const { name, value } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    
    // Implementar autocompletado: cuando se cambia el título, buscar una tarea con ese título
    if (name === 'title' && value.trim() !== '') {
      const matchingTask = tasks.find(task => 
        task.title.toLowerCase() === value.toLowerCase());
      
      if (matchingTask) {
        setFormData((prev) => ({
          ...prev,
          taskId: matchingTask.id
        }));
      }
    }
  };

  const handleDateChange = (name) => (date) => {
    setFormData((prev) => ({
      ...prev,
      [name]: date,
    }));
    // Calcular horas trabajadas automáticamente cuando se actualiza cualquier fecha
    if (formData.startTime && date && name === 'endTime') {
      const diff = date - formData.startTime;
      const hours = diff / (1000 * 60 * 60);
      if (hours >= 0) {
        setFormData((prev) => ({
          ...prev,
          hoursWorked: hours.toFixed(2),
        }));
      }
    } else if (name === 'startTime' && formData.endTime && date) {
      const diff = formData.endTime - date;
      const hours = diff / (1000 * 60 * 60);
      if (hours >= 0) {
        setFormData((prev) => ({
          ...prev,
          hoursWorked: hours.toFixed(2),
        }));
      }
    }
  };

  const calculateHours = () => {
    const diff = formData.endTime - formData.startTime;
    const hours = diff / (1000 * 60 * 60);
    if (hours >= 0) {
      setFormData((prev) => ({
        ...prev,
        hoursWorked: hours.toFixed(2),
      }));
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    // Validar campos dinámicos
    const dynamicValidationErrors = validateDynamicFields();
    if (Object.keys(dynamicValidationErrors).length > 0) {
      setSnackbar({
        open: true,
        message: 'Por favor complete todos los campos requeridos',
        severity: 'error',
      });
      return;
    }

    try {
      // Si no hay taskId seleccionado, crear una nueva tarea con el mismo título
      const submitData = {
        ...formData,
        startTime: formData.startTime.toISOString(),
        endTime: formData.endTime.toISOString(),
        hoursWorked: parseFloat(formData.hoursWorked),
        dynamicFields: dynamicValues, // Agregar valores de campos dinámicos
      };

      await axios.post(
        `${API_URL}/api/time-entries`,
        submitData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );

      setSnackbar({
        open: true,
        message: 'Registro de tiempo enviado correctamente',
        severity: 'success',
      });

      // Limpiar formulario y actualizar lista
      setFormData({
        title: '',
        description: '',
        taskId: '',
        startTime: new Date(),
        endTime: new Date(),
        hoursWorked: '',
      });
      resetDynamicValues(); // Limpiar campos dinámicos
      fetchTimeEntries();
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Error al enviar el registro de tiempo',
        severity: 'error',
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={es}>
      <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Registro de Tiempo
        </Typography>

        <Paper sx={{ p: 3, mb: 4 }}>
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              {/* Renderizar campos dinámicos ordenados */}
              {dynamicFields
                .filter(field => field.isActive)
                .sort((a, b) => a.order - b.order)
                .map((field) => {
                  // Manejar campos especiales que necesitan lógica personalizada
                  if (field.name === 'startTime') {
                    return (
                      <Grid item xs={12} sm={6} key={field.name}>
                        <DateTimePicker
                          label={field.label}
                          value={formData.startTime}
                          onChange={handleDateChange('startTime')}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              fullWidth
                              required={field.required}
                              error={!!dynamicErrors[field.name]}
                              helperText={dynamicErrors[field.name]}
                            />
                          )}
                        />
                      </Grid>
                    );
                  }

                  if (field.name === 'endTime') {
                    return (
                      <Grid item xs={12} sm={6} key={field.name}>
                        <DateTimePicker
                          label={field.label}
                          value={formData.endTime}
                          onChange={handleDateChange('endTime')}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              fullWidth
                              required={field.required}
                              error={!!dynamicErrors[field.name]}
                              helperText={dynamicErrors[field.name]}
                            />
                          )}
                        />
                      </Grid>
                    );
                  }

                  if (field.name === 'hoursWorked') {
                    return (
                      <Grid item xs={12} key={field.name}>
                        <TextField
                          required={field.required}
                          fullWidth
                          label={field.label}
                          name="hoursWorked"
                          type="number"
                          value={formData.hoursWorked}
                          onChange={handleChange}
                          placeholder={field.placeholder}
                          error={!!dynamicErrors[field.name]}
                          helperText={dynamicErrors[field.name]}
                          InputProps={{
                            readOnly: true,
                          }}
                        />
                      </Grid>
                    );
                  }

                  // Campos que usan formData en lugar de dynamicValues
                  if (['title', 'description', 'task'].includes(field.name)) {
                    const fieldName = field.name === 'task' ? 'taskId' : field.name;

                    if (field.name === 'task') {
                      return (
                        <Grid item xs={12} key={field.name}>
                          <TextField
                            select
                            fullWidth
                            label={field.label}
                            name="taskId"
                            value={formData.taskId}
                            onChange={handleChange}
                            required={field.required}
                            placeholder={field.placeholder}
                            error={!!dynamicErrors[field.name]}
                            helperText={dynamicErrors[field.name]}
                          >
                            <MenuItem value="">
                              <em>Ninguna</em>
                            </MenuItem>
                            {tasks.map((task) => (
                              <MenuItem key={task.id} value={task.id}>
                                {task.title}
                              </MenuItem>
                            ))}
                          </TextField>
                        </Grid>
                      );
                    }

                    return (
                      <Grid item xs={12} key={field.name}>
                        <TextField
                          required={field.required}
                          fullWidth
                          label={field.label}
                          name={fieldName}
                          multiline={field.type === 'textarea'}
                          rows={field.type === 'textarea' ? 3 : 1}
                          value={formData[fieldName]}
                          onChange={handleChange}
                          placeholder={field.placeholder}
                          error={!!dynamicErrors[field.name]}
                          helperText={dynamicErrors[field.name]}
                        />
                      </Grid>
                    );
                  }

                  // Campos dinámicos personalizados
                  return (
                    <Grid item xs={12} key={field.name}>
                      <DynamicFieldRenderer
                        field={field}
                        value={dynamicValues[field.name]}
                        onChange={updateDynamicValue}
                        error={dynamicErrors[field.name]}
                        tasks={tasks}
                      />
                    </Grid>
                  );
                })}

              <Grid item xs={12}>
                <Button type="submit" variant="contained" color="primary" fullWidth>
                  Registrar Tiempo
                </Button>
              </Grid>
            </Grid>
          </form>
        </Paper>

        <Typography variant="h5" component="h2" gutterBottom>
          Mis Registros de Tiempo
        </Typography>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Título</TableCell>
                <TableCell>Tarea</TableCell>
                <TableCell>Inicio</TableCell>
                <TableCell>Fin</TableCell>
                <TableCell>Horas</TableCell>
                <TableCell>Estado</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {timeEntries.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell>{entry.title}</TableCell>
                  <TableCell>{entry.task?.title || 'N/A'}</TableCell>
                  <TableCell>{format(new Date(entry.startTime), 'dd/MM/yyyy HH:mm')}</TableCell>
                  <TableCell>{format(new Date(entry.endTime), 'dd/MM/yyyy HH:mm')}</TableCell>
                  <TableCell>{formatHoursAndMinutes(entry.hoursWorked)}</TableCell>
                  <TableCell>{entry.status}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Container>
    </LocalizationProvider>
  );
};

export default TimeTracking;
