-- Migración para agregar funcionalidad de integraciones API
-- Ejecutar este script en la base de datos después de aplicar los cambios al schema.prisma

-- Nota: Los campos userApiKeys y apiImportLogs en User son relaciones, no columnas físicas
-- Se manejan a través de las tablas UserApiKey y ApiImportLog

-- Agregar campos a la tabla Task para integraciones externas
ALTER TABLE "Task" ADD COLUMN IF NOT EXISTS "externalId" TEXT;
ALTER TABLE "Task" ADD COLUMN IF NOT EXISTS "externalUrl" TEXT;
ALTER TABLE "Task" ADD COLUMN IF NOT EXISTS "integrationId" TEXT;

-- Crear tabla ApiIntegration
CREATE TABLE IF NOT EXISTS "ApiIntegration" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "baseUrl" TEXT NOT NULL,
    "authType" TEXT NOT NULL,
    "authConfig" JSONB NOT NULL,
    "endpoints" JSONB NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT NOT NULL,

    CONSTRAINT "ApiIntegration_pkey" PRIMARY KEY ("id")
);

-- Crear tabla UserApiKey
CREATE TABLE IF NOT EXISTS "UserApiKey" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "integrationId" TEXT NOT NULL,
    "apiKey" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastTested" TIMESTAMP(3),
    "testStatus" TEXT,
    "testMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserApiKey_pkey" PRIMARY KEY ("id")
);

-- Crear tabla ApiImportLog
CREATE TABLE IF NOT EXISTS "ApiImportLog" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "integrationId" TEXT NOT NULL,
    "endpointName" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "recordsFound" INTEGER,
    "tasksCreated" INTEGER,
    "tasksUpdated" INTEGER,
    "errorMessage" TEXT,
    "executionTime" INTEGER,
    "requestData" JSONB,
    "responseData" JSONB,
    "executedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ApiImportLog_pkey" PRIMARY KEY ("id")
);

-- Crear índices únicos y de rendimiento
CREATE UNIQUE INDEX IF NOT EXISTS "UserApiKey_userId_integrationId_key" ON "UserApiKey"("userId", "integrationId");
CREATE INDEX IF NOT EXISTS "UserApiKey_userId_idx" ON "UserApiKey"("userId");
CREATE INDEX IF NOT EXISTS "UserApiKey_integrationId_idx" ON "UserApiKey"("integrationId");
CREATE INDEX IF NOT EXISTS "ApiImportLog_userId_idx" ON "ApiImportLog"("userId");
CREATE INDEX IF NOT EXISTS "ApiImportLog_integrationId_idx" ON "ApiImportLog"("integrationId");
CREATE INDEX IF NOT EXISTS "ApiImportLog_executedAt_idx" ON "ApiImportLog"("executedAt");

-- Agregar claves foráneas
ALTER TABLE "Task" ADD CONSTRAINT "Task_integrationId_fkey" 
    FOREIGN KEY ("integrationId") REFERENCES "ApiIntegration"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "UserApiKey" ADD CONSTRAINT "UserApiKey_userId_fkey" 
    FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "UserApiKey" ADD CONSTRAINT "UserApiKey_integrationId_fkey" 
    FOREIGN KEY ("integrationId") REFERENCES "ApiIntegration"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "ApiImportLog" ADD CONSTRAINT "ApiImportLog_userId_fkey" 
    FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "ApiImportLog" ADD CONSTRAINT "ApiImportLog_integrationId_fkey" 
    FOREIGN KEY ("integrationId") REFERENCES "ApiIntegration"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Insertar datos de ejemplo para ManageEngine (opcional)
INSERT INTO "ApiIntegration" ("id", "name", "description", "baseUrl", "authType", "authConfig", "endpoints", "createdBy", "updatedAt")
VALUES (
    'manage-engine-example',
    'ManageEngine ServiceDesk Plus',
    'Integración de ejemplo con ManageEngine para importar tickets',
    'https://company.manageengine.com/api/v3',
    'api_key',
    '{"Content-Type": "application/json", "Accept": "application/json"}',
    '[{
        "name": "Obtener Tickets Asignados",
        "method": "GET",
        "path": "/requests",
        "targetMenu": "kanban",
        "queryParams": {
            "input_data": {
                "list_info": {
                    "row_count": 50,
                    "start_index": 1,
                    "search_criteria": [{
                        "field": "technician.email_id",
                        "condition": "is",
                        "value": "{{USER_EMAIL}}"
                    }]
                }
            }
        },
        "fieldMapping": {
            "title": "subject",
            "description": "description",
            "priority": "priority.name",
            "status": "status.name",
            "externalId": "id",
            "externalUrl": "display_id"
        },
        "statusMapping": {
            "Open": "TODO",
            "In Progress": "IN_PROGRESS",
            "Pending": "TODO",
            "Resolved": "REVIEW",
            "Closed": "DONE"
        },
        "priorityMapping": {
            "Low": "LOW",
            "Medium": "MEDIUM",
            "High": "HIGH",
            "Urgent": "URGENT"
        }
    }]',
    'admin-user-id',
    CURRENT_TIMESTAMP
) ON CONFLICT ("id") DO NOTHING;

-- Verificar que las tablas se crearon correctamente
SELECT 'ApiIntegration' as tabla, count(*) as registros FROM "ApiIntegration"
UNION ALL
SELECT 'UserApiKey' as tabla, count(*) as registros FROM "UserApiKey"
UNION ALL
SELECT 'ApiImportLog' as tabla, count(*) as registros FROM "ApiImportLog";

-- Mostrar estructura de las nuevas tablas
\d "ApiIntegration"
\d "UserApiKey"
\d "ApiImportLog"
