#!/usr/bin/env node

/**
 * Script para probar las APIs del calendario directamente
 */

const axios = require('axios');

const API_URL = 'http://localhost:5000';

async function testCalendarAPIs() {
  console.log('🔍 Probando APIs del Calendario...\n');

  // Simular token de usuario - necesitarás obtener uno real del localStorage del navegador
  const token = 'TOKEN_REAL_AQUI';
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  const params = {
    startDate: '2025-01-06',
    endDate: '2025-01-12'
  };

  console.log('📅 Parámetros de prueba:', params);
  console.log('🔑 Headers:', { Authorization: 'Bearer [TOKEN]' });
  console.log('');

  // Test 1: Tareas completadas
  try {
    console.log('1️⃣ Probando /api/tasks/completed-by-date...');
    const tasksResponse = await axios.get(`${API_URL}/api/tasks/completed-by-date`, {
      headers,
      params
    });
    console.log('✅ Tareas completadas - Status:', tasksResponse.status);
    console.log('📊 Cantidad de tareas:', tasksResponse.data.length);
    if (tasksResponse.data.length > 0) {
      console.log('📝 Primera tarea:', {
        id: tasksResponse.data[0].id,
        title: tasksResponse.data[0].title,
        status: tasksResponse.data[0].status
      });
    }
  } catch (error) {
    console.log('❌ Error en tareas completadas:', error.response?.status, error.response?.data?.error || error.message);
  }

  console.log('');

  // Test 2: Permisos aprobados
  try {
    console.log('2️⃣ Probando /api/permissions/approved-by-date...');
    const permissionsResponse = await axios.get(`${API_URL}/api/permissions/approved-by-date`, {
      headers,
      params
    });
    console.log('✅ Permisos aprobados - Status:', permissionsResponse.status);
    console.log('📊 Cantidad de permisos:', permissionsResponse.data.length);
    if (permissionsResponse.data.length > 0) {
      console.log('📝 Primer permiso:', {
        id: permissionsResponse.data[0].id,
        type: permissionsResponse.data[0].type,
        status: permissionsResponse.data[0].status
      });
    }
  } catch (error) {
    console.log('❌ Error en permisos aprobados:', error.response?.status, error.response?.data?.error || error.message);
  }

  console.log('');

  // Test 3: Tareas kanban archivadas
  try {
    console.log('3️⃣ Probando /api/tasks/archived-kanban-tasks...');
    const kanbanResponse = await axios.get(`${API_URL}/api/tasks/archived-kanban-tasks`, {
      headers,
      params
    });
    console.log('✅ Tareas kanban archivadas - Status:', kanbanResponse.status);
    console.log('📊 Cantidad de tareas kanban:', kanbanResponse.data.length);
    if (kanbanResponse.data.length > 0) {
      console.log('📝 Primera tarea kanban:', {
        id: kanbanResponse.data[0].id,
        title: kanbanResponse.data[0].title,
        status: kanbanResponse.data[0].status
      });
    }
  } catch (error) {
    console.log('❌ Error en tareas kanban archivadas:', error.response?.status, error.response?.data?.error || error.message);
  }

  console.log('');

  // Test 4: Verificar si el backend está respondiendo
  try {
    console.log('4️⃣ Probando conectividad básica del backend...');
    const healthResponse = await axios.get(`${API_URL}/`);
    console.log('✅ Backend responde - Status:', healthResponse.status);
    console.log('📝 Respuesta:', healthResponse.data);
  } catch (error) {
    console.log('❌ Error de conectividad del backend:', error.message);
  }

  console.log('\n🎯 Diagnóstico completado');
}

// Ejecutar las pruebas
testCalendarAPIs().catch(console.error);
