
const express = require('express');
const router = express.Router();
const globalTimerController = require('../controllers/globalTimerController');
const { authenticateToken } = require('../middleware/auth');

// Todas las rutas requieren autenticación
router.use(authenticateToken);

// POST /api/global-timer/:taskId/start - Iniciar temporizador global
router.post('/:taskId/start', globalTimerController.startGlobalTimer);

// POST /api/global-timer/:taskId/pause - Pausar temporizador global
router.post('/:taskId/pause', globalTimerController.pauseGlobalTimer);

// GET /api/global-timer/:taskId/status - Obtener estado del temporizador
router.get('/:taskId/status', globalTimerController.getGlobalTimerStatus);

module.exports = router;
