import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  CircularProgress,
  Alert,
  Chip,
  Button,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  AccessTime as TimeIcon,
  Assignment as TaskIcon,
  CheckCircle as ApprovalIcon,
  Warning as PendingIcon,
  Notifications as NotificationIcon,
  DateRange as DateIcon,
  TrendingUp as TrendingIcon,
  Person as PersonIcon,
  Flag as FlagIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { API_URL } from '../config';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import axios from 'axios';

// Importar los componentes necesarios
import UsersWeeklyHours<PERSON>hart from '../components/UsersWeeklyHoursChart';
import WeeklyCalendar from '../components/WeeklyCalendar';
import OvertimeCompensationTable from '../components/OvertimeCompensationTable';

// Modify the StatCard component to use 'label' instead of 'title'
const StatCard = ({ label, value, icon: Icon, color }) => (
  <Paper
    sx={{
      p: 3,
      display: 'flex',
      alignItems: 'center',
      gap: 2,
      height: '100%',
    }}
  >
    <Box
      sx={{
        backgroundColor: `${color}.light`,
        borderRadius: '50%',
        p: 1,
        display: 'flex',
      }}
    >
      <Icon sx={{ color: `${color}.main` }} />
    </Box>
    <Box>
      <Typography variant="h6" component="div">
        {value}
      </Typography>
      <Typography color="textSecondary" variant="body2">
        {label}
      </Typography>
    </Box>
  </Paper>
);

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [summaryData, setSummaryData] = useState(null);
  const [statsData, setStatsData] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const [summaryResponse, statsResponse] = await Promise.all([
        axios.get(`${API_URL}/api/dashboard/summary`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        }),
        axios.get(`${API_URL}/api/dashboard/stats`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        }),
      ]);

      setSummaryData(summaryResponse.data);
      setStatsData(statsResponse.data);
      setError(null);
    } catch (err) {
      console.error('Error al obtener datos del dashboard:', err);
      setError('Error al cargar el dashboard');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  const taskStatusData = summaryData?.taskStatusDistribution.map(status => ({
    name: status.status,
    value: status._count,
  }));

  const timeTypeData = summaryData?.timeByType.map(type => ({
    name: type.type,
    horas: type._sum.hoursWorked,
    horasPonderadas: type._sum.multipliedHours || type._sum.hoursWorked,
  }));

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<DateIcon />}
          onClick={fetchDashboardData}
        >
          Actualizar
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* Estadísticas principales */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            label="Horas esta semana"
            value={`${summaryData?.weeklyHours.toFixed(1)}h`}
            icon={TimeIcon}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Tooltip title={`Registros: ${summaryData?.weeklyHoursFromTimeEntries.toFixed(1)}h | Tareas archivadas: ${summaryData?.weeklyHoursFromArchivedTasks.toFixed(1)}h`}>
            <StatCard
              label="Desglose de horas"
              value={`${summaryData?.weeklyHours.toFixed(1)}h`}
              icon={TimeIcon}
              color="info"
            />
          </Tooltip>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            label="Tareas pendientes"
            value={summaryData?.pendingTasks}
            icon={TaskIcon}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            label="Por aprobar"
            value={summaryData?.pendingApprovals}
            icon={ApprovalIcon}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            label="Notificaciones"
            value={summaryData?.unreadNotifications}
            icon={NotificationIcon}
            color="error"
          />
        </Grid>

        {/* Gráficos */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Distribución de Horas por Tipo
            </Typography>
            <Box sx={{ height: 300 }}>
              <BarChart
                width={700}
                height={300}
                data={timeTypeData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                <Bar dataKey="horas" name="Horas Normales" fill="#8884d8" />
                <Bar dataKey="horasPonderadas" name="Horas Ponderadas" fill="#82ca9d" />
              </BarChart>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Estado de Tareas
            </Typography>
            <Box sx={{ height: 300, display: 'flex', justifyContent: 'center' }}>
              <PieChart width={300} height={300}>
                <Pie
                  data={taskStatusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {taskStatusData?.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <RechartsTooltip />
              </PieChart>
            </Box>
          </Paper>
        </Grid>

        {/* Gráfico de horas semanales por usuario (solo para administradores) */}
        {(localStorage.getItem('userRole') === 'ADMIN' || localStorage.getItem('userRole') === 'MANAGER') && (
          <Grid item xs={12}>
            <UsersWeeklyHoursChart />
          </Grid>
        )}

        {/* Tabla de Horas Extra y Compensaciones (todos los usuarios) */}
        <Grid item xs={12}>
          <OvertimeCompensationTable />
        </Grid>

        {/* Calendario Semanal */}
        <Grid item xs={12}>
          <WeeklyCalendar />
        </Grid>

        {/* Próximas tareas */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Próximas Tareas
            </Typography>
            <List>
              {summaryData?.upcomingTasks.map((task) => (
                <React.Fragment key={task.id}>
                  <ListItem>
                    <ListItemIcon>
                      <FlagIcon color={task.priority === 'HIGH' ? 'error' : 'action'} />
                    </ListItemIcon>
                    <ListItemText
                      primary={task.title}
                      secondary={
                        <React.Fragment>
                          <Typography variant="body2" component="span">
                            Vence: {format(new Date(task.dueDate), 'dd/MM/yyyy', { locale: es })}
                          </Typography>
                          <br />
                          <Chip
                            size="small"
                            label={`${task.estimatedHours}h est. / ${task.actualHours}h real`}
                            color={task.actualHours > task.estimatedHours ? 'warning' : 'default'}
                            sx={{ mt: 1 }}
                          />
                        </React.Fragment>
                      }
                    />
                  </ListItem>
                  <Divider />
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Permisos recientes */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Permisos Recientes
            </Typography>
            <List>
              {summaryData?.recentPermissions.map((permission) => (
                <React.Fragment key={permission.id}>
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={permission.type}
                      secondary={
                        <React.Fragment>
                          <Typography variant="body2" component="span">
                            {format(new Date(permission.startTime), 'dd/MM/yyyy HH:mm', { locale: es })}
                            {' - '}
                            {format(new Date(permission.endTime), 'dd/MM/yyyy HH:mm', { locale: es })}
                          </Typography>
                          <br />
                          <Chip
                            size="small"
                            label={permission.status}
                            color={
                              permission.status === 'APPROVED'
                                ? 'success'
                                : permission.status === 'REJECTED'
                                ? 'error'
                                : 'warning'
                            }
                            sx={{ mt: 1 }}
                          />
                        </React.Fragment>
                      }
                    />
                  </ListItem>
                  <Divider />
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Eficiencia de tareas */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Eficiencia de Tareas
            </Typography>
            <Box sx={{ height: 300 }}>
              <BarChart
                width={1000}
                height={300}
                data={statsData?.taskEfficiency}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="title" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                <Bar dataKey="estimatedHours" name="Horas Estimadas" fill="#8884d8" />
                <Bar dataKey="actualHours" name="Horas Reales" fill="#82ca9d" />
              </BarChart>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Dashboard;


