import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, IconButton, Card, CardContent, List, ListItem, ListItemText, Divider } from '@mui/material';
import { Timer as TimerIcon, Stop as StopIcon, Person as PersonIcon } from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../config';
import { socket } from '../services/socket';

const TimeTracker = ({ taskId, onTimeEntrySaved, taskOwnerId }) => {
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const timerRef = useRef(null);
  const lastUpdateTimestamp = useRef(0);
  const unmountingRef = useRef(false);
  const [otherTimers, setOtherTimers] = useState({});
  const currentUserId = localStorage.getItem('userId');
  
  // Determinar si la tarea pertenece al usuario actual
  const isTaskOwner = taskOwnerId === currentUserId;

  // Add this useEffect to fetch timer status when component mounts
  useEffect(() => {
    const getInitialTimerStatus = async () => {
      try {
        const response = await axios.get(`${API_URL}/api/active-timer/${taskId}/status`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        });
        
        if (response.data && response.data.isRunning) {
          setElapsedTime(response.data.accumulatedTime);
          setIsRunning(true);
          startLocalTimer(response.data.accumulatedTime);
        }
      } catch (error) {
        console.error('Error al obtener estado inicial del timer:', error);
      }
    };
    
    getInitialTimerStatus();
  }, [taskId]);

  // Socket connection and room joining
  useEffect(() => {
    if (socket) {
      socket.emit('joinTask', taskId);
      
      // Listen for timer updates
      socket.on(`taskTimer_${taskId}`, (data) => {
        console.log('Actualización de temporizador recibida:', data);
        
        // Verificar si la actualización es para el usuario actual
        const isCurrentUser = data.userId === currentUserId;
        
        if (isCurrentUser && data.timestamp > lastUpdateTimestamp.current) {
          lastUpdateTimestamp.current = data.timestamp;
          setElapsedTime(data.accumulatedTime);
          setIsRunning(data.isRunning);
          
          // Update local timer if running
          if (data.isRunning) {
            startLocalTimer(data.accumulatedTime);
          } else {
            stopLocalTimer();
          }
        } else if (!isCurrentUser) {
          // Actualizar temporizadores de otros usuarios
          setOtherTimers(prev => ({
            ...prev,
            [data.userId]: {
              userId: data.userId,
              userName: data.userName || 'Usuario',
              accumulatedTime: data.accumulatedTime,
              isRunning: data.isRunning,
              timestamp: data.timestamp
            }
          }));
        }
      });
  
      // Solicitar una sincronización inmediata al unirse a la tarea
      socket.emit('syncTimer', taskId);
    }
  
    return () => {
      unmountingRef.current = true;
      if (socket) {
        socket.off(`taskTimer_${taskId}`);
        socket.emit('leaveTask', taskId);
      }
      stopLocalTimer();
    };
  }, [taskId, currentUserId]);

  // Local storage persistence
  useEffect(() => {
    const storedTimer = localStorage.getItem(`timer_${taskId}`);
    if (storedTimer) {
      const { time, running, lastUpdate } = JSON.parse(storedTimer);
      if (running) {
        const now = Date.now();
        const elapsed = Math.floor((now - lastUpdate) / 1000);
        setElapsedTime(time + elapsed);
      } else {
        setElapsedTime(time);
      }
      setIsRunning(running);
    }
  }, [taskId]);

  // Save timer state to local storage
  useEffect(() => {
    localStorage.setItem(`timer_${taskId}`, JSON.stringify({
      time: elapsedTime,
      running: isRunning,
      lastUpdate: Date.now()
    }));
  }, [elapsedTime, isRunning, taskId]);

  const startLocalTimer = (initialTime) => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setElapsedTime(initialTime || elapsedTime);
    timerRef.current = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);
  };

  const stopLocalTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  // Add the fetchTimerStatus function
  const fetchTimerStatus = async () => {
    try {
      const response = await axios.get(`${API_URL}/api/active-timer/${taskId}/status`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      if (response.data) {
        setElapsedTime(response.data.accumulatedTime || 0);
        setIsRunning(response.data.isRunning || false);
        
        // If timer should be running, ensure it's running locally
        if (response.data.isRunning) {
          startLocalTimer(response.data.accumulatedTime);
        }
      }
    } catch (error) {
      console.error('Error al obtener estado del timer:', error);
    }
  };

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}m ${secs}s`;
  };

  const startTimer = async () => {
    try {
      // Iniciar timer en el servidor
      await axios.post(`${API_URL}/api/active-timer/start`, 
        { taskId },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );
      
      setIsRunning(true);
      
      // Iniciar intervalo local para UI
      timerRef.current = setInterval(() => {
        setElapsedTime(prev => prev + 1);
      }, 1000);
    } catch (error) {
      console.error('Error al iniciar el timer:', error);
    }
  };

  const pauseTimer = async () => {
    try {
      // Pausar timer en el servidor
      await axios.post(`${API_URL}/api/active-timer/${taskId}/pause`, {}, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      setIsRunning(false);
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    } catch (error) {
      console.error('Error al pausar el timer:', error);
    }
  };

  const stopTimer = async () => {
    try {
      // Detener timer en el servidor
      const response = await axios.post(`${API_URL}/api/active-timer/${taskId}/stop`, {}, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      setIsRunning(false);
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      
      // Notificar al componente padre
      if (onTimeEntrySaved && !unmountingRef.current && response.data.timeEntry) {
        onTimeEntrySaved(response.data.timeEntry);
      }
    } catch (error) {
      console.error('Error al detener el timer:', error);
    }
  };

  const handleTimerClick = async () => {
    if (isRunning) {
      await pauseTimer();
    } else {
      await startTimer();
    }
  };

  const handleStopClick = async () => {
    await stopTimer();
    await fetchTimerStatus();
  };

  return (
    <Card variant="outlined" sx={{ mb: 2 }}>
      <CardContent>
        {isTaskOwner ? (
          // Mostrar el temporizador propio solo si la tarea pertenece al usuario
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton
                color={isRunning ? "error" : "primary"}
                onClick={handleTimerClick}
                size="small"
                title={isRunning ? "Pausar timer" : "Iniciar timer"}
              >
                <TimerIcon fontSize="small" />
              </IconButton>
              <Typography 
                variant="body2" 
                component="div"
                sx={{ fontSize: '0.875rem' }}
              >
                {formatTime(elapsedTime)}
              </Typography>
              <Box sx={{ ml: 'auto' }}>
                <IconButton
                  color="error"
                  onClick={handleStopClick}
                  size="small"
                  title="Detener y guardar tiempo"
                >
                  <StopIcon fontSize="small" />
                </IconButton>
              </Box>
            </Box>
          </>
        ) : (
          // Si no es propietario, no mostrar título
          null
        )}
        
        {/* Mostrar temporizadores de otros usuarios */}
        {Object.keys(otherTimers).length > 0 && (
          <>
            {isTaskOwner && <Divider sx={{ my: 2 }} />}
            {isTaskOwner && (
              <Typography variant="h6" gutterBottom>
                Otros Temporizadores
              </Typography>
            )}
            <List>
              {Object.values(otherTimers).map((timer) => (
                <ListItem key={timer.userId}>
                  <PersonIcon sx={{ mr: 1, color: timer.isRunning ? 'green' : 'grey' }} />
                  <ListItemText 
                    primary={timer.userName}
                    secondary={
                      <Typography variant="body2" component="span" color={timer.isRunning ? "success.main" : "text.secondary"}>
                        {formatTime(timer.accumulatedTime)} {timer.isRunning ? '(Activo)' : '(Pausado)'}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default TimeTracker;
