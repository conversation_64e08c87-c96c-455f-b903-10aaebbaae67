const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const prisma = new PrismaClient();

// Obtener todos los usuarios (solo para administradores)
exports.getAllUsers = async (req, res) => {
  try {
    // Verificar que el usuario sea administrador o manager
    if (req.user.role !== 'ADMIN' && req.user.role !== 'MANAGER') {
      return res.status(403).json({ error: 'No autorizado para ver esta información' });
    }

    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isLDAPUser: true,
        // isActive: true, // Comentado temporalmente
        lastLogin: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    res.json(users);
  } catch (error) {
    console.error('Error al obtener usuarios:', error);
    res.status(500).json({ error: 'Error al obtener usuarios' });
  }
};

// Obtener un usuario específico
exports.getUser = async (req, res) => {
  try {
    const { id } = req.params;

    // Los usuarios solo pueden ver su propio perfil, a menos que sean admin/manager
    if (req.user.id !== id && req.user.role !== 'ADMIN' && req.user.role !== 'MANAGER') {
      return res.status(403).json({ error: 'No autorizado' });
    }

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isLDAPUser: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'Usuario no encontrado' });
    }

    res.json(user);
  } catch (error) {
    console.error('Error al obtener usuario:', error);
    res.status(500).json({ error: 'Error al obtener usuario' });
  }
};

// Crear un nuevo usuario (solo para administradores)
exports.createUser = async (req, res) => {
  try {
    // Verificar que el usuario sea administrador
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'No autorizado para crear usuarios' });
    }

    const { name, email, password, role = 'EMPLOYEE' } = req.body;

    // Validar datos requeridos
    if (!name || !email || !password) {
      return res.status(400).json({ error: 'Nombre, email y contraseña son obligatorios' });
    }

    // Verificar si el usuario ya existe
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return res.status(400).json({ error: 'El usuario ya existe' });
    }

    // Encriptar contraseña
    const hashedPassword = await bcrypt.hash(password, 10);

    // Crear usuario
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isLDAPUser: true,
        createdAt: true,
        updatedAt: true
      }
    });

    res.status(201).json(user);
  } catch (error) {
    console.error('Error al crear usuario:', error);
    res.status(500).json({ error: 'Error al crear usuario' });
  }
};

// Actualizar un usuario (solo para administradores)
exports.updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, email, role } = req.body;

    // Verificar que el usuario sea administrador
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'No autorizado para actualizar usuarios' });
    }

    // Verificar que el usuario existe
    const existingUser = await prisma.user.findUnique({
      where: { id }
    });

    if (!existingUser) {
      return res.status(404).json({ error: 'Usuario no encontrado' });
    }

    // Actualizar usuario
    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(email && { email }),
        ...(role && { role })
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isLDAPUser: true,
        createdAt: true,
        updatedAt: true
      }
    });

    res.json(updatedUser);
  } catch (error) {
    console.error('Error al actualizar usuario:', error);
    res.status(500).json({ error: 'Error al actualizar usuario' });
  }
};

// Desactivar un usuario (solo para administradores)
// Nota: Como no existe el campo isActive en el modelo User, esta función está deshabilitada
exports.deactivateUser = async (req, res) => {
  try {
    // Verificar que el usuario sea administrador
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'No autorizado para desactivar usuarios' });
    }

    res.status(501).json({ error: 'Funcionalidad no implementada: el modelo User no tiene campo isActive' });
  } catch (error) {
    console.error('Error al desactivar usuario:', error);
    res.status(500).json({ error: 'Error al desactivar usuario' });
  }
};

// Restablecer contraseña
exports.resetPassword = async (req, res) => {
  try {
    // Verificar que el usuario sea admin
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'Solo los administradores pueden restablecer contraseñas' });
    }

    const { id } = req.params;

    // Verificar que el usuario existe
    const user = await prisma.user.findUnique({
      where: { id }
    });

    if (!user) {
      return res.status(404).json({ error: 'Usuario no encontrado' });
    }

    // Generar nueva contraseña temporal
    const newPassword = crypto.randomBytes(8).toString('hex');
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Actualizar contraseña
    await prisma.user.update({
      where: { id },
      data: {
        password: hashedPassword
      }
    });

    res.json({
      message: 'Contraseña restablecida correctamente',
      newPassword: newPassword
    });
  } catch (error) {
    console.error('Error al restablecer contraseña:', error);
    res.status(500).json({ error: 'Error al restablecer contraseña' });
  }
};

// Cambiar estado activo/inactivo del usuario
exports.toggleUserActive = async (req, res) => {
  try {
    // Verificar que el usuario sea admin
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'Solo los administradores pueden cambiar el estado de usuarios' });
    }

    const { id } = req.params;
    const { isActive } = req.body;

    // Verificar que el usuario existe
    const user = await prisma.user.findUnique({
      where: { id }
    });

    if (!user) {
      return res.status(404).json({ error: 'Usuario no encontrado' });
    }

    // No permitir desactivar el propio usuario
    if (user.id === req.user.id && !isActive) {
      return res.status(400).json({ error: 'No puedes desactivar tu propio usuario' });
    }

    // Actualizar estado (si el campo existe)
    try {
      const updatedUser = await prisma.user.update({
        where: { id },
        data: { isActive },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true
        }
      });

      res.json(updatedUser);
    } catch (error) {
      // Si el campo isActive no existe en el modelo
      if (error.code === 'P2002' || error.message.includes('isActive')) {
        return res.status(501).json({
          error: 'El campo isActive no está disponible en el modelo User. Necesita migración de base de datos.'
        });
      }
      throw error;
    }
  } catch (error) {
    console.error('Error al cambiar estado del usuario:', error);
    res.status(500).json({ error: 'Error al cambiar estado del usuario' });
  }
};

// Eliminar un usuario (solo para administradores)
exports.deleteUser = async (req, res) => {
  try {
    // Verificar que el usuario sea admin
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'Solo los administradores pueden eliminar usuarios' });
    }

    const { id } = req.params;

    // Verificar que el usuario no se elimine a sí mismo
    if (req.user.id === id) {
      return res.status(400).json({
        error: 'No puedes eliminar tu propia cuenta'
      });
    }

    // Verificar que el usuario existe
    const userExists = await prisma.user.findUnique({
      where: { id }
    });

    if (!userExists) {
      return res.status(404).json({ error: 'Usuario no encontrado' });
    }

    // Buscar un admin para reasignar tareas (que no sea el usuario a eliminar)
    const adminUser = await prisma.user.findFirst({
      where: {
        role: 'ADMIN',
        id: { not: id }
      }
    });

    if (!adminUser) {
      return res.status(400).json({
        error: 'No se puede eliminar el usuario: no hay otros administradores disponibles para reasignar las tareas'
      });
    }

    // Usar transacción para eliminar de forma segura
    await prisma.$transaction(async (tx) => {
      // 1. Eliminar timers activos
      await tx.activeTimer.deleteMany({
        where: { userId: id }
      });

      // 2. Eliminar registros de tiempo
      await tx.timeEntry.deleteMany({
        where: { userId: id }
      });

      // 3. Eliminar permisos del usuario
      await tx.permission.deleteMany({
        where: { userId: id }
      });

      // 4. Reasignar tareas asignadas al admin
      await tx.task.updateMany({
        where: { assigneeId: id },
        data: { assigneeId: adminUser.id }
      });

      // 5. Reasignar tareas creadas al admin
      await tx.task.updateMany({
        where: { creatorId: id },
        data: { creatorId: adminUser.id }
      });

      // 6. Limpiar referencias de timer actual
      await tx.task.updateMany({
        where: { timerCurrentUserId: id },
        data: { timerCurrentUserId: null }
      });

      // 7. Finalmente eliminar el usuario
      await tx.user.delete({
        where: { id }
      });
    });

    res.json({
      message: 'Usuario eliminado correctamente',
      reassignedTo: adminUser.name
    });
  } catch (error) {
    console.error('Error al eliminar usuario:', error);
    if (error.code === 'P2003') {
      res.status(400).json({
        error: 'No se puede eliminar el usuario porque tiene datos asociados. Considera desactivarlo en su lugar.'
      });
    } else {
      res.status(500).json({ error: 'Error interno del servidor' });
    }
  }
};
