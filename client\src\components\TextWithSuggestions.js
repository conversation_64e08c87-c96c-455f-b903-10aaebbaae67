import React, { useState, useEffect, useRef } from 'react';
import {
  TextField,
  Paper,
  List,
  ListItem,
  ListItemText,
  Popper,
  ClickAwayListener,
  Box
} from '@mui/material';
import axios from 'axios';
import { API_URL } from '../config';

const TextWithSuggestions = ({ 
  field, 
  value = '', 
  onChange, 
  error,
  ...props 
}) => {
  const [suggestions, setSuggestions] = useState([]);
  const [filteredSuggestions, setFilteredSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeSuggestion, setActiveSuggestion] = useState(-1);
  const [loading, setLoading] = useState(false);
  const inputRef = useRef(null);
  const anchorRef = useRef(null);

  // Cargar sugerencias existentes al montar el componente
  useEffect(() => {
    fetchSuggestions();
  }, [field.name]);

  // Filtrar sugerencias cuando cambia el valor
  useEffect(() => {
    if (value && suggestions.length > 0) {
      const filtered = suggestions.filter(suggestion =>
        suggestion.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredSuggestions(filtered);
    } else {
      setFilteredSuggestions(suggestions);
    }
  }, [value, suggestions]);

  const fetchSuggestions = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/dynamic-fields/suggestions/${field.name}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setSuggestions(response.data || []);
    } catch (error) {
      console.error('Error al cargar sugerencias:', error);
      setSuggestions([]);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (event) => {
    const newValue = event.target.value;
    onChange(field.name, newValue);
    setShowSuggestions(true);
    setActiveSuggestion(-1);
  };

  const handleInputFocus = () => {
    setShowSuggestions(true);
  };

  const handleSuggestionClick = (suggestion) => {
    onChange(field.name, suggestion);
    setShowSuggestions(false);
    setActiveSuggestion(-1);
  };

  const handleKeyDown = (event) => {
    if (!showSuggestions || filteredSuggestions.length === 0) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setActiveSuggestion(prev => 
          prev < filteredSuggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        event.preventDefault();
        setActiveSuggestion(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Tab':
      case 'Enter':
        if (activeSuggestion >= 0) {
          event.preventDefault();
          handleSuggestionClick(filteredSuggestions[activeSuggestion]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setActiveSuggestion(-1);
        break;
      default:
        break;
    }
  };

  const handleClickAway = () => {
    setShowSuggestions(false);
    setActiveSuggestion(-1);
  };

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <Box sx={{ position: 'relative' }}>
        <TextField
          ref={anchorRef}
          fullWidth
          label={field.label}
          value={value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          placeholder={field.placeholder}
          required={field.required}
          error={!!error}
          helperText={error || 'Escribe para ver sugerencias o crear un nuevo valor'}
          {...props}
        />
        
        <Popper
          open={showSuggestions && filteredSuggestions.length > 0}
          anchorEl={anchorRef.current}
          placement="bottom-start"
          style={{ zIndex: 1300, width: anchorRef.current?.offsetWidth }}
        >
          <Paper elevation={3} sx={{ maxHeight: 200, overflow: 'auto' }}>
            <List dense>
              {filteredSuggestions.map((suggestion, index) => (
                <ListItem
                  key={index}
                  button
                  selected={index === activeSuggestion}
                  onClick={() => handleSuggestionClick(suggestion)}
                  sx={{
                    backgroundColor: index === activeSuggestion ? 'action.hover' : 'transparent',
                    '&:hover': {
                      backgroundColor: 'action.hover'
                    }
                  }}
                >
                  <ListItemText primary={suggestion} />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Popper>
      </Box>
    </ClickAwayListener>
  );
};

export default TextWithSuggestions;
