const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Importar la función directamente del controlador
const taskController = require('../controllers/taskController');

async function testAPIDirectly() {
  try {
    console.log('🔍 PROBANDO API DIRECTAMENTE');
    console.log('='.repeat(60));

    // Simular req y res objects
    const req = {
      query: {
        startDate: '2025-06-23',
        endDate: '2025-06-29'
      },
      user: {
        id: 'cm2adcjdg000013dtfapcjdce', // ID de usuario de ejemplo
        role: 'ADMIN'
      }
    };

    const res = {
      json: (data) => {
        console.log(`\n📊 RESPUESTA DE LA API (${data.length} tareas):`);
        
        // Buscar específicamente la tarea de reunión ISO
        const reunionISO = data.find(task => task.title && task.title.includes('Reunión Revision de ISO'));
        
        if (reunionISO) {
          console.log(`\n⭐ TAREA REUNIÓN ISO ENCONTRADA:`);
          console.log(`   ID: ${reunionISO.id}`);
          console.log(`   Título: ${reunionISO.title}`);
          console.log(`   Inicio: ${reunionISO.startTime}`);
          console.log(`   Fin: ${reunionISO.endTime}`);
          console.log(`   🎯 TAREA isOutsideOfficeHours: ${reunionISO.isOutsideOfficeHours}`);
          console.log(`   🎯 TAREA hourMultiplier: ${reunionISO.hourMultiplier}`);
          console.log(`   🎯 TAREA weightedHours: ${reunionISO.weightedHours}`);
          console.log(`   Color esperado: ${reunionISO.isOutsideOfficeHours ? '🔴 ROJO' : '🟢 VERDE'}`);

          if (reunionISO.timeEntries && reunionISO.timeEntries.length > 0) {
            const timeEntry = reunionISO.timeEntries[0];
            console.log(`   📝 TimeEntry isOutsideOfficeHours: ${timeEntry.isOutsideOfficeHours}`);
            console.log(`   📝 TimeEntry hourMultiplier: ${timeEntry.hourMultiplier}`);
            console.log(`   📝 TimeEntry weightedHours: ${timeEntry.weightedHours}`);
          }

          // Mostrar todos los campos de la tarea para debug
          console.log(`\n🔍 TODOS LOS CAMPOS DE LA TAREA:`);
          console.log(JSON.stringify(reunionISO, null, 2));
        } else {
          console.log(`\n❌ TAREA PRUEBA21 NO ENCONTRADA EN LA RESPUESTA`);
          
          // Mostrar todas las tareas para debug
          console.log(`\n📝 TODAS LAS TAREAS EN LA RESPUESTA:`);
          data.forEach((task, index) => {
            console.log(`   ${index + 1}. ${task.title} (${task.id})`);
          });
        }
        
        return { json: () => {} };
      },
      status: (code) => ({
        json: (error) => {
          console.error(`❌ Error ${code}:`, error);
          return { json: () => {} };
        }
      })
    };

    console.log(`📅 Llamando getCompletedTasksByDate con:`);
    console.log(`   startDate: ${req.query.startDate}`);
    console.log(`   endDate: ${req.query.endDate}`);
    console.log(`   usuario: ${req.user.role}`);

    // Llamar la función directamente
    await taskController.getCompletedTasksByDate(req, res);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAPIDirectly();
