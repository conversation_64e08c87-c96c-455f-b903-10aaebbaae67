const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Función para crear campos por defecto
const createDefaultFields = async () => {
  const defaultFields = [
    // Campos para TimeTracking
    { name: 'title', label: 'Título', type: 'text', required: true, order: 1, appliesTo: 'timeEntry' },
    { name: 'description', label: 'Descripción', type: 'textarea', required: false, order: 2, appliesTo: 'timeEntry' },
    { name: 'startTime', label: 'Fecha/Hora Inicio', type: 'datetime', required: true, order: 3, appliesTo: 'timeEntry' },
    { name: 'endTime', label: 'Fecha/Hora Fin', type: 'datetime', required: true, order: 4, appliesTo: 'timeEntry' },
    { name: 'hoursWorked', label: 'Horas Trabajadas', type: 'number', required: true, order: 5, appliesTo: 'timeEntry' },
    { name: 'task', label: 'Tarea Asociada', type: 'select', required: false, order: 6, appliesTo: 'timeEntry' },

    // Campos para Kanban
    { name: 'title', label: 'Título', type: 'text', required: true, order: 1, appliesTo: 'kanban' },
    { name: 'description', label: 'Descripción', type: 'textarea', required: false, order: 2, appliesTo: 'kanban' },
    { name: 'estimatedHours', label: 'Horas Estimadas', type: 'number', required: false, order: 3, appliesTo: 'kanban' },
    { name: 'dueDate', label: 'Fecha Límite', type: 'date', required: false, order: 4, appliesTo: 'kanban' },
    { name: 'priority', label: 'Prioridad', type: 'select', required: false, order: 5, appliesTo: 'kanban', options: 'LOW,MEDIUM,HIGH,URGENT' },

    // Campo personalizado que creaste
    { name: 'actividad', label: 'Actividad', type: 'select', required: false, order: 7, appliesTo: 'both', options: 'Desarrollo,Testing,Documentación,Reunión,Soporte' }
  ];

  for (const field of defaultFields) {
    try {
      await prisma.dynamicField.upsert({
        where: { name: field.name + '_' + field.appliesTo },
        update: {},
        create: {
          name: field.name,
          label: field.label,
          type: field.type,
          required: field.required,
          order: field.order,
          appliesTo: field.appliesTo,
          options: field.options || null,
          isActive: true
        }
      });
    } catch (error) {
      console.log(`Campo ${field.name} ya existe o error:`, error.message);
    }
  }
};

// Obtener todos los campos dinámicos
exports.getDynamicFields = async (req, res) => {
  try {
    const { appliesTo } = req.query;

    const whereClause = {
      isActive: true
    };

    if (appliesTo && appliesTo !== 'both') {
      whereClause.OR = [
        { appliesTo: appliesTo },
        { appliesTo: 'both' }
      ];
    }

    let fields = await prisma.dynamicField.findMany({
      where: whereClause,
      orderBy: { order: 'asc' }
    });

    // Si no hay campos, crear los campos básicos
    if (fields.length === 0) {
      await createDefaultFields();
      fields = await prisma.dynamicField.findMany({
        where: whereClause,
        orderBy: { order: 'asc' }
      });
    }

    res.json(fields);
  } catch (error) {
    console.error('Error al obtener campos dinámicos:', error);
    res.status(500).json({ error: 'Error al obtener campos dinámicos' });
  }
};

// Obtener todos los campos para administración
exports.getAllDynamicFields = async (req, res) => {
  try {
    const fields = await prisma.dynamicField.findMany({
      orderBy: { order: 'asc' }
    });

    res.json(fields);
  } catch (error) {
    console.error('Error al obtener todos los campos dinámicos:', error);
    res.status(500).json({ error: 'Error al obtener campos dinámicos' });
  }
};

// Crear nuevo campo dinámico
exports.createDynamicField = async (req, res) => {
  try {
    const {
      name,
      label,
      type,
      required,
      placeholder,
      options,
      defaultValue,
      order,
      appliesTo
    } = req.body;

    // Verificar que el usuario sea admin
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'Solo los administradores pueden crear campos dinámicos' });
    }

    // Verificar que el nombre no exista
    const existingField = await prisma.dynamicField.findUnique({
      where: { name }
    });

    if (existingField) {
      return res.status(400).json({ error: 'Ya existe un campo con ese nombre' });
    }

    const field = await prisma.dynamicField.create({
      data: {
        name,
        label,
        type,
        required: required || false,
        placeholder,
        options,
        defaultValue,
        order: order || 0,
        appliesTo: appliesTo || 'both'
      }
    });

    res.status(201).json(field);
  } catch (error) {
    console.error('Error al crear campo dinámico:', error);
    res.status(500).json({ error: 'Error al crear campo dinámico' });
  }
};

// Actualizar campo dinámico
exports.updateDynamicField = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      label,
      type,
      required,
      placeholder,
      options,
      defaultValue,
      order,
      isActive,
      appliesTo
    } = req.body;

    // Verificar que el usuario sea admin
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'Solo los administradores pueden actualizar campos dinámicos' });
    }

    const field = await prisma.dynamicField.update({
      where: { id },
      data: {
        label,
        type,
        required,
        placeholder,
        options,
        defaultValue,
        order,
        isActive,
        appliesTo
      }
    });

    res.json(field);
  } catch (error) {
    console.error('Error al actualizar campo dinámico:', error);
    res.status(500).json({ error: 'Error al actualizar campo dinámico' });
  }
};

// Eliminar campo dinámico
exports.deleteDynamicField = async (req, res) => {
  try {
    const { id } = req.params;

    // Verificar que el usuario sea admin
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'Solo los administradores pueden eliminar campos dinámicos' });
    }

    // Verificar que no sea un campo por defecto
    const field = await prisma.dynamicField.findUnique({
      where: { id }
    });

    if (!field) {
      return res.status(404).json({ error: 'Campo no encontrado' });
    }

    if (field.id.startsWith('default-')) {
      return res.status(400).json({ error: 'No se pueden eliminar campos por defecto' });
    }

    await prisma.dynamicField.delete({
      where: { id }
    });

    res.json({ message: 'Campo eliminado correctamente' });
  } catch (error) {
    console.error('Error al eliminar campo dinámico:', error);
    res.status(500).json({ error: 'Error al eliminar campo dinámico' });
  }
};

// Obtener valores de campos dinámicos para una entidad
exports.getDynamicFieldValues = async (req, res) => {
  try {
    const { entityType, entityId } = req.params;

    const values = await prisma.dynamicFieldValue.findMany({
      where: {
        entityType,
        entityId
      },
      include: {
        field: true
      }
    });

    res.json(values);
  } catch (error) {
    console.error('Error al obtener valores de campos dinámicos:', error);
    res.status(500).json({ error: 'Error al obtener valores' });
  }
};

// Guardar valores de campos dinámicos
exports.saveDynamicFieldValues = async (req, res) => {
  try {
    const { entityType, entityId, values } = req.body;

    // Eliminar valores existentes
    await prisma.dynamicFieldValue.deleteMany({
      where: {
        entityType,
        entityId
      }
    });

    // Crear nuevos valores
    const createPromises = Object.entries(values).map(([fieldId, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        return prisma.dynamicFieldValue.create({
          data: {
            fieldId,
            entityType,
            entityId,
            value: String(value)
          }
        });
      }
      return null;
    }).filter(Boolean);

    await Promise.all(createPromises);

    res.json({ message: 'Valores guardados correctamente' });
  } catch (error) {
    console.error('Error al guardar valores de campos dinámicos:', error);
    res.status(500).json({ error: 'Error al guardar valores' });
  }
};

// Obtener sugerencias para un campo específico
exports.getFieldSuggestions = async (req, res) => {
  try {
    const { fieldName } = req.params;

    // Buscar valores únicos ya usados para este campo
    const suggestions = await prisma.dynamicFieldValue.findMany({
      where: {
        field: {
          name: fieldName
        },
        value: {
          not: null
        }
      },
      select: {
        value: true
      },
      distinct: ['value']
    });

    // Extraer solo los valores y filtrar vacíos
    const uniqueValues = suggestions
      .map(s => s.value)
      .filter(value => value && value.trim() !== '')
      .sort();

    res.json(uniqueValues);
  } catch (error) {
    console.error('Error al obtener sugerencias:', error);
    res.status(500).json({ error: 'Error al obtener sugerencias' });
  }
};
