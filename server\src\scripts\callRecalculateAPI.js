const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function recalculateOfficeHoursDirectly() {
  try {
    console.log('🔄 RECALCULANDO HORARIOS DE OFICINA PARA TODAS LAS TAREAS APROBADAS');
    console.log('='.repeat(70));

    // 1. Obtener configuración actual de horarios de trabajo
    const workSchedules = await prisma.workSchedule.findMany();
    console.log(`📅 Horarios de trabajo configurados: ${workSchedules.length}`);

    if (workSchedules.length === 0) {
      console.log('⚠️ No hay horarios configurados. Usando valores por defecto.');
    } else {
      workSchedules.forEach(schedule => {
        const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
        console.log(`  - ${dayNames[schedule.dayOfWeek]}: ${schedule.startTime} - ${schedule.endTime}`);
      });
    }

    // 2. Obtener configuración de multiplicadores
    const multipliers = await prisma.timeMultiplier.findMany({
      where: {
        isActive: true
      }
    });

    console.log(`💰 Multiplicadores configurados: ${multipliers.length}`);

    // 3. Función para calcular multiplicador
    const calculateMultiplier = (entryStartTime, entryEndTime) => {
      const entryDate = new Date(entryStartTime);
      const dayOfWeek = entryDate.getDay(); // 0 = Domingo, 1 = Lunes, etc.

      const startHour = entryDate.getHours();
      const startMinute = entryDate.getMinutes();

      let applicableMultiplier = 1.0;
      let multiplierReasons = [];

      // Verificar cada multiplicador configurado
      multipliers.forEach(mult => {
        // Verificar multiplicador por rango de horas
        if (mult.startTime && mult.endTime) {
          const multStartMinutes = parseInt(mult.startTime.split(':')[0]) * 60 + parseInt(mult.startTime.split(':')[1]);
          const multEndMinutes = parseInt(mult.endTime.split(':')[0]) * 60 + parseInt(mult.endTime.split(':')[1]);
          const entryStartMinutes = startHour * 60 + startMinute;

          // Manejar horarios que cruzan medianoche
          let isInTimeRange = false;
          if (multStartMinutes > multEndMinutes) {
            // Cruza medianoche
            isInTimeRange = (entryStartMinutes >= multStartMinutes) || (entryStartMinutes <= multEndMinutes);
          } else {
            // No cruza medianoche
            isInTimeRange = (entryStartMinutes >= multStartMinutes) && (entryStartMinutes <= multEndMinutes);
          }

          if (isInTimeRange) {
            applicableMultiplier = Math.max(applicableMultiplier, mult.value);
            multiplierReasons.push(`${mult.name} (${mult.startTime}-${mult.endTime}): ${mult.value}x`);
          }
        }
      });

      // Verificar multiplicador por día de la semana (domingo = 2x)
      if (dayOfWeek === 0) { // Domingo
        applicableMultiplier = Math.max(applicableMultiplier, 2.0);
        multiplierReasons.push(`Domingo: 2.0x`);
      }

      return { multiplier: applicableMultiplier, reasons: multiplierReasons };
    };

    // 4. Función para verificar si está fuera del horario de oficina
    const isOutsideOfficeHours = (entryStartTime, entryEndTime) => {
      const entryDate = new Date(entryStartTime);
      const dayOfWeek = entryDate.getDay();

      // Buscar horario para el día de la semana
      const daySchedule = workSchedules.find(schedule => schedule.dayOfWeek === dayOfWeek);

      if (!daySchedule) {
        // Si no hay horario para este día, está fuera del horario de oficina
        return true;
      }

      // Convertir horarios a minutos
      const [startHour, startMin] = daySchedule.startTime.split(':').map(Number);
      const [endHour, endMin] = daySchedule.endTime.split(':').map(Number);
      const officeStartMinutes = startHour * 60 + startMin;
      const officeEndMinutes = endHour * 60 + endMin;

      const entryStartMinutes = entryDate.getHours() * 60 + entryDate.getMinutes();
      const entryEndMinutes = new Date(entryEndTime).getHours() * 60 + new Date(entryEndTime).getMinutes();

      // Verificar si está fuera del horario de oficina
      return entryStartMinutes < officeStartMinutes ||
             entryStartMinutes >= officeEndMinutes ||
             entryEndMinutes <= officeStartMinutes ||
             entryEndMinutes > officeEndMinutes;
    };

    // 5. Obtener todas las tareas aprobadas
    const approvedEntries = await prisma.timeEntry.findMany({
      where: {
        status: 'APPROVED'
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    console.log(`\n📊 Tareas aprobadas encontradas: ${approvedEntries.length}`);

    let updatedCount = 0;
    let unchangedCount = 0;

    // 6. Recalcular cada tarea
    for (const entry of approvedEntries) {
      const { multiplier, reasons } = calculateMultiplier(entry.startTime, entry.endTime);
      const newWeightedHours = entry.hoursWorked * multiplier;
      const newIsOutsideOfficeHours = isOutsideOfficeHours(entry.startTime, entry.endTime);

      // Verificar si hay cambios
      const hasChanges =
        entry.hourMultiplier !== multiplier ||
        entry.weightedHours !== newWeightedHours ||
        entry.isOutsideOfficeHours !== newIsOutsideOfficeHours;

      if (hasChanges) {
        await prisma.timeEntry.update({
          where: { id: entry.id },
          data: {
            hourMultiplier: multiplier,
            weightedHours: newWeightedHours,
            isOutsideOfficeHours: newIsOutsideOfficeHours,
            multiplierReasons: reasons
          }
        });

        console.log(`✅ ACTUALIZADO: ${entry.user.name} - "${entry.title}"`);
        console.log(`   🔄 Multiplicador: ${entry.hourMultiplier || 1.0}x → ${multiplier}x`);
        console.log(`   🔄 Horas ponderadas: ${entry.weightedHours || entry.hoursWorked}h → ${newWeightedHours}h`);
        console.log(`   🔄 Fuera de horario: ${entry.isOutsideOfficeHours || false} → ${newIsOutsideOfficeHours}`);
        console.log(`   📅 Horario: ${new Date(entry.startTime).toLocaleString()} - ${new Date(entry.endTime).toLocaleString()}`);

        updatedCount++;
      } else {
        unchangedCount++;
      }
    }

    console.log(`\n🎯 RECÁLCULO COMPLETADO:`);
    console.log(`   ✅ Actualizadas: ${updatedCount} tareas`);
    console.log(`   ➡️ Sin cambios: ${unchangedCount} tareas`);
    console.log('='.repeat(70));

  } catch (error) {
    console.error('❌ Error durante el recálculo:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar la función
recalculateOfficeHoursDirectly();
