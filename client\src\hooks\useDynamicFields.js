import { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../config';

export const useDynamicFields = (appliesTo = 'both') => {
  const [fields, setFields] = useState([]);
  const [values, setValues] = useState({});
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFields();
  }, [appliesTo]);

  const fetchFields = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/dynamic-fields`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { appliesTo }
      });
      setFields(response.data);
    } catch (error) {
      console.error('Error al cargar campos dinámicos:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadValues = async (entityType, entityId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/dynamic-fields/values/${entityType}/${entityId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      const loadedValues = {};
      response.data.forEach(item => {
        loadedValues[item.fieldId] = item.value;
      });
      setValues(loadedValues);
    } catch (error) {
      console.error('Error al cargar valores de campos dinámicos:', error);
    }
  };

  const saveValues = async (entityType, entityId) => {
    try {
      const token = localStorage.getItem('token');
      await axios.post(`${API_URL}/api/dynamic-fields/values`, {
        entityType,
        entityId,
        values
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return true;
    } catch (error) {
      console.error('Error al guardar valores de campos dinámicos:', error);
      return false;
    }
  };

  const updateValue = (fieldName, value) => {
    setValues(prev => ({
      ...prev,
      [fieldName]: value
    }));
    
    // Limpiar error si existe
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: null
      }));
    }
  };

  const validateFields = () => {
    const newErrors = {};
    let isValid = true;

    fields.forEach(field => {
      if (field.required && (!values[field.name] || values[field.name] === '')) {
        newErrors[field.name] = `${field.label} es requerido`;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const resetValues = () => {
    const defaultValues = {};
    fields.forEach(field => {
      if (field.defaultValue) {
        defaultValues[field.name] = field.defaultValue;
      }
    });
    setValues(defaultValues);
    setErrors({});
  };

  const getFieldValue = (fieldName) => {
    return values[fieldName] || '';
  };

  const getFieldError = (fieldName) => {
    return errors[fieldName] || '';
  };

  // Obtener campos que no son especiales (para renderizar como dinámicos)
  const getDynamicFields = () => {
    const specialFields = ['title', 'description', 'hoursWorked', 'startTime', 'endTime', 'estimatedHours', 'dueDate', 'task', 'priority'];
    return fields.filter(field => !specialFields.includes(field.name));
  };

  // Obtener valores para campos especiales
  const getSpecialFieldValues = () => {
    const specialValues = {};
    const specialFields = ['title', 'description', 'hoursWorked', 'startTime', 'endTime', 'estimatedHours', 'dueDate', 'task', 'priority'];
    
    specialFields.forEach(fieldName => {
      if (values[fieldName] !== undefined) {
        specialValues[fieldName] = values[fieldName];
      }
    });
    
    return specialValues;
  };

  return {
    fields,
    values,
    errors,
    loading,
    updateValue,
    validateFields,
    resetValues,
    loadValues,
    saveValues,
    getFieldValue,
    getFieldError,
    getDynamicFields,
    getSpecialFieldValues
  };
};
